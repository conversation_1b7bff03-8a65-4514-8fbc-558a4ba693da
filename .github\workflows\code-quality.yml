name: Code Quality Check

on:
  push:
    branches:
      - main
      - new_ui
  pull_request:
    branches:
      - main
      - new_ui

jobs:
  code-quality:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20.18.0"

      - name: Install dependencies
        run: yarn install

      - name: Run ESLint
        run: yarn lint

      - name: Run Prettier
        run: yarn format --check
