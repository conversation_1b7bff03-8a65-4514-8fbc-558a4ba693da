# Briky Land

Briky Land is the first Real Estate NFT Exchange Platform, allowing for fractional ownership and seamless trading
without traditional barriers. This React Native application leverages WalletConnect to connect with MetaMask and uses
ethers.js to interact with the blockchain. The project is built using Expo for easier development and deployment.

## Features

- **Connect with MetaMask**: Securely connect your MetaMask wallet using WalletConnect.
- **Blockchain Interaction**: Utilize ethers.js for seamless interaction with the Ethereum blockchain.
- **Fractional Ownership**: Participate in fractional ownership of real estate NFTs.
- **Seamless Trading**: Trade real estate NFTs without traditional barriers.

## Getting Started

These instructions will help you set up and run the Briky Land app on your local machine for development and testing
purposes.

### Prerequisites

- Node.js (>= 14.x)
- npm or yarn
- Expo CLI

### Installation

1. **Clone the repository:**

   ```bash
   git clone https://github.com/brikyland/briky-land-native-app
   cd briky-land-native-app
   ```

2. **Install dependencies:**

   Using npm:

   ```bash
   npm install
   ```

   Or using yarn:

   ```bash
   yarn install
   ```

3. **Run the app:**

   You can run the Briky Land app in different environments (alpha, beta, production) using the following commands.
   These commands set the `APP_ENV` environment variable to determine which configuration to use.

   ```bash
   APP_ENV=env npx expo start
   ```

You can then use the Expo Go app on your mobile device to scan the QR code and run the app, or use an
emulator/simulator.

## Built With

- [React Native](https://reactnative.dev/) - Framework for building native apps using React.
- [Expo](https://expo.dev/) - A platform for making universal native apps for Android, iOS, and the web with JavaScript
  and React.
- [WalletConnect](https://walletconnect.com/) - Open protocol for connecting wallets to dapps.
- [ethers.js](https://docs.ethers.io/v5/) - A library for interacting with the Ethereum blockchain.

## Eas Build

```bash
eas build --platform ios
```

```bash
eas build --platform android
```

```bash
eas build --platform ios --profile beta
```

```bash
eas build --platform android --profile beta
```
