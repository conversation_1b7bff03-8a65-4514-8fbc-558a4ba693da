// app.config.js
module.exports = {
  expo: {
    newArchEnabled: true,
    jsEngine: "hermes",
    name: "Briky Land",
    slug: "briky-land",
    scheme: "brikyland",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.brikyland.app",
      buildNumber: "1",
      infoPlist: {
        LSApplicationQueriesSchemes: [
          "metamask",
          "trust",
          "safe",
          "rainbow",
          "uniswap",
        ],
      },
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#17181E",
      },
      package: "com.brikyland.app",
      versionCode: 10,
      // Lưu ý: 'config.abiFilters' đã cũ, nên chuyển 'abiFilters' trực tiếp ra ngoài 'android'
      // Nhưng để giữ nguyên cấu trúc file gốc của bạn + thêm plugin, tạm để đây:
      config: {
        abiFilters: ["armeabi-v7a", "arm64-v8a"],
      },
      // Thuộc tính đúng thường là 'enableProguardInReleaseBuilds' (viết hoa chữ cái đầu từ thứ 2 trở đi)
      // Nếu file gốc của bạn đúng là chữ thường hết thì giữ nguyên, nhưng có thể bạn gõ nhầm.
      // Giả sử đúng là:
      enableProguardInReleaseBuilds: true,
    },
    web: {
      favicon: "./assets/favicon.png",
    },
    plugins: [
      // Các plugin đã có từ app.json của bạn:
      "expo-font",
      "./queries.js", // Đảm bảo file này tồn tại ở thư mục gốc
      [
        "expo-image-picker",
        {
          microphonePermission: false,
        },
      ],
      [
        "expo-splash-screen",
        {
          backgroundColor: "#17181E",
          image: "./assets/splash.png",
          dark: {
            image: "./assets/splash.png",
            backgroundColor: "#17181E",
          },
          imageWidth: 200,
        },
      ],

      // *** Thêm plugin mới của bạn vào đây: ***
      "./withRemoveAndroidPermission", // Đường dẫn tới file plugin .ts hoặc .js
    ],
    owner: "brikyland",
    extra: {
      eas: {
        projectId: "298406a5-743d-4f5a-b863-617282248b1f",
      },
    },
  },
}
