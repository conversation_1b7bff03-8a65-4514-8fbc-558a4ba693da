module.exports = function (api) {
  api.cache(true)
  return {
    presets: ["babel-preset-expo"],
    plugins: [
      [
        "module-resolver",
        {
          alias: {
            // This needs to be mirrored in tsconfig.json
            components: "./src/components",
            src: "./src",
            assets: "./assets",
            navigation: "./src/navigation",
            screens: "./src/screens",
            hooks: "./src/hooks",
            const: "./src/const",
            utils: "./src/utils",
          },
        },
      ],
    ],
  }
}
