{"name": "briky-land", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "cross-env NODE_ENV=development npx expo start", "test": "cross-env NODE_ENV=test npx expo start", "dev": "cross-env NODE_ENV=development npx expo start", "prod": "cross-env NODE_ENV=production npx expo start", "ios": "npx expo run:ios", "android": "expo run:android", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "i18n:generate": "node scripts/generate-i18n-keys.js"}, "dependencies": {"@ethersproject/units": "^5.8.0", "@expo-google-fonts/inter": "^0.2.3", "@expo/react-native-action-sheet": "^4.1.1", "@hookform/resolvers": "^3.10.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "4.5.6", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@reown/appkit-wagmi-react-native": "^1.2.3", "@tanstack/query-core": "^5.62.3", "@tanstack/react-query": "^5.74.3", "@walletconnect/react-native-compat": "^2.19.2", "axios": "^1.8.4", "big.js": "^6.2.2", "cross-env": "^7.0.3", "currency.js": "^2.0.4", "dotenv": "^16.5.0", "ethers": "5", "expo": "^53.0.0", "expo-application": "~6.1.4", "expo-checkbox": "~4.1.4", "expo-clipboard": "~7.1.4", "expo-dev-client": "~5.1.7", "expo-font": "~13.3.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "fastestsmallesttextencoderdecoder": "^1.0.22", "i18next": "^24.2.3", "immer": "^10.1.1", "jotai": "^2.12.3", "lottie-react-native": "7.2.2", "react": "19.0.0", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-native": "0.79.2", "react-native-currency-input": "^1.1.1", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "~1.11.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.13.1", "react-native-pie-chart": "^3.1.0", "react-native-reanimated": "~3.17.4", "react-native-root-toast": "^3.6.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "uuid": "^11.1.0", "viem": "^2.27.0", "wagmi": "^2.14.16", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/js": "^9.24.0", "@types/big.js": "6.2.2", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-native": "^5.0.0", "globals": "^16.0.0", "prettier": "^3.5.3", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["@reown/appkit-wagmi-react-native", "i18next", "react-native-element-dropdown", "react-native-pie-chart"]}}}}