import axios from "axios"
import { EXPO_PUBLIC_BASE_API_URL } from "src/config/env"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { checkErrorCode } from "src/api/errors/handleError"
import Logger from "src/utils/logger"

const axiosInstance = axios.create({
  baseURL: EXPO_PUBLIC_BASE_API_URL,
  headers: {
    accept: "*/*",
    "accept-language": "en-US,en;q=0.7",
    "content-type": "application/json",
  },
})

const logger = new Logger({ tag: "API" })

axiosInstance.interceptors.request.use(
  (config) => {
    // Log request details
    logger.info(`Request: ${config.method?.toUpperCase()} ${config.url}`, {
      url: config.url,
      method: config.method,
      headers: config.headers,
      baseURL: config.baseURL,
      timeout: config.timeout,
      withCredentials: config.withCredentials,
    })

    // Log request data separately for better visibility
    if (config.data) {
      logger.info(
        `Request Data for ${config.method?.toUpperCase()} ${config.url}:`,
        {
          data: config.data,
        }
      )
    }

    // Log request params separately if present
    if (config.params && Object.keys(config.params).length > 0) {
      logger.info(
        `Request Params for ${config.method?.toUpperCase()} ${config.url}:`,
        {
          params: config.params,
        }
      )
    }

    return config
  },
  (error) => {
    logger.error("Request error", {
      message: error.message,
      stack: error.stack,
      config: error.config,
      code: error.code,
    })
    return Promise.reject(error)
  }
)

export const setupResponseInterceptor = (logout: () => void) => {
  axiosInstance.interceptors.response.use(
    (response) => {
      // Log response details
      logger.success(`Response: ${response.status} ${response.config.url}`, {
        url: response.config.url,
        method: response.config.method,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      })

      // Log response data separately for better visibility
      logger.success(
        `Response Data for ${response.config.method?.toUpperCase()} ${response.config.url}:`,
        {
          data: response.data,
        }
      )

      // Log request details that led to this response
      logger.debug(
        `Original Request for ${response.config.method?.toUpperCase()} ${response.config.url}:`,
        {
          url: response.config.url,
          method: response.config.method,
          headers: response.config.headers,
          data: response.config.data,
          params: response.config.params,
        }
      )

      return response
    },
    async (error) => {
      if (error.response?.status === 401) {
        logger.warn("Unauthorized access, logging out")
        logout()
      }

      if (error.response) {
        // Log error response details
        logger.error(
          `Response error: ${error.response.status} ${error.response.config.url}`,
          {
            url: error.response.config.url,
            method: error.response.config.method,
            status: error.response.status,
            statusText: error.response.statusText,
            headers: error.response.headers,
          }
        )

        // Log error response data separately
        logger.error(
          `Error Response Data for ${error.response.config.method?.toUpperCase()} ${error.response.config.url}:`,
          {
            data: error.response.data,
            errorCode: error?.response?.data?.meta?.code,
          }
        )

        // Log original request that led to this error
        logger.debug(
          `Original Request for ${error.response.config.method?.toUpperCase()} ${error.response.config.url}:`,
          {
            url: error.response.config.url,
            method: error.response.config.method,
            headers: error.response.config.headers,
            data: error.response.config.data,
            params: error.response.config.params,
          }
        )

        const errorCode = error?.response?.data?.meta?.code
        checkErrorCode(errorCode)
      } else {
        // Network or other errors without response
        logger.error("Network error", {
          message: error.message,
          stack: error.stack,
          code: error.code,
        })

        // Log request config if available
        if (error.config) {
          logger.debug("Failed Request Config:", {
            url: error.config.url,
            method: error.config.method,
            headers: error.config.headers,
            data: error.config.data,
            params: error.config.params,
            baseURL: error.config.baseURL,
            timeout: error.config.timeout,
          })
        }
      }
      return Promise.reject(error)
    }
  )
}

// Add helper functions
export const setAuthToken = async (
  token: string,
  tokenExpiredTimeInSeconds?: number,
  refreshToken?: string,
  refreshTokenExpiredTimeInSeconds?: number,
  address?: string
) => {
  axiosInstance.defaults.headers.common["Authorization"] = `Bearer ${token}`
  await AsyncStorage.setItem("token", token)
  await AsyncStorage.setItem(
    "tokenExpiryTimeInSeconds",
    tokenExpiredTimeInSeconds?.toString() || ""
  )
  if (refreshToken) {
    await AsyncStorage.setItem("refreshToken", refreshToken)
    await AsyncStorage.setItem(
      "refreshTokenExpiredTimeInSeconds",
      refreshTokenExpiredTimeInSeconds?.toString() || ""
    )
  }
  if (address) {
    await AsyncStorage.setItem("address", address)
  }
}

export const getStoredToken = async () => {
  const address = await AsyncStorage.getItem("address")
  const token = await AsyncStorage.getItem("token")
  const tokenExpiryTime = await AsyncStorage.getItem("tokenExpiryTime")
  const refreshToken = await AsyncStorage.getItem("refreshToken")
  const refreshTokenExpiredTimeInSeconds = await AsyncStorage.getItem(
    "refreshTokenExpiredTimeInSeconds"
  )
  return {
    address,
    token,
    tokenExpiryTime,
    refreshToken,
    refreshTokenExpiredTimeInSeconds,
  }
}

export const clearStoredToken = async () => {
  await AsyncStorage.removeItem("address")
  await AsyncStorage.removeItem("token")
  await AsyncStorage.removeItem("tokenExpiryTime")
  await AsyncStorage.removeItem("refreshToken")
  await AsyncStorage.removeItem("refreshTokenExpiredTimeInSeconds")
  delete axiosInstance.defaults.headers.common["Authorization"]
}

export default axiosInstance
