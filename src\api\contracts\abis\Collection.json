{"_format": "hh-sol-artifact-1", "contractName": "Collection", "sourceName": "contracts/Collection.sol", "abi": [{"inputs": [], "name": "AlreadyWithdrawn", "type": "error"}, {"inputs": [], "name": "Cancelled", "type": "error"}, {"inputs": [], "name": "Deprecated", "type": "error"}, {"inputs": [], "name": "FailedOwnershipTransferring", "type": "error"}, {"inputs": [], "name": "FailedRefund", "type": "error"}, {"inputs": [], "name": "FailedTransfer", "type": "error"}, {"inputs": [], "name": "InsufficientFunds", "type": "error"}, {"inputs": [], "name": "InvalidInput", "type": "error"}, {"inputs": [], "name": "InvalidPercentage", "type": "error"}, {"inputs": [], "name": "InvalidRequestId", "type": "error"}, {"inputs": [], "name": "InvalidTokenId", "type": "error"}, {"inputs": [], "name": "InvalidWithdrawing", "type": "error"}, {"inputs": [], "name": "MaxSellingAmountExceeded", "type": "error"}, {"inputs": [], "name": "NotEnoughSoldAmount", "type": "error"}, {"inputs": [], "name": "PublicSaleEnded", "type": "error"}, {"inputs": [], "name": "StillSelling", "type": "error"}, {"inputs": [], "name": "Tokenized", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "newValue", "type": "string"}], "name": "BaseURIUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "CommissionRateUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "uri", "type": "string"}, {"indexed": false, "internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"indexed": false, "internalType": "uint8", "name": "decimals", "type": "uint8"}], "name": "DirectTokenization", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "GovernorHubUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "requester", "type": "address"}, {"indexed": false, "internalType": "string", "name": "uri", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "min<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "max<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "unitPrice", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "currency", "type": "address"}, {"indexed": false, "internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"indexed": false, "internalType": "uint8", "name": "decimals", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "duration", "type": "uint256"}], "name": "NewTokenizationRequest", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "RoyaltyFeeRateUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "TokenDeprecation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint40", "name": "newExpireAt", "type": "uint40"}], "name": "TokenExpirationExtension", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "withdrawer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokenWithdrawal", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}], "name": "TokenizationCancellation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "soldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "commission", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "commissionReceiver", "type": "address"}], "name": "TokenizationConfirmation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "depositor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "TokenizationDeposit", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "requestId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "withdrawer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "TokenizationDepositWithdrawal", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "TokenizationFeeRateUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256[]", "name": "ids", "type": "uint256[]"}, {"indexed": false, "internalType": "uint256[]", "name": "values", "type": "uint256[]"}], "name": "TransferBatch", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "TransferSingle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "value", "type": "string"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "URI", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_account", "type": "address"}, {"internalType": "uint256", "name": "_tokenId", "type": "uint256"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_account", "type": "address"}, {"internalType": "uint256", "name": "_tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "_at", "type": "uint256"}], "name": "balanceOfAt", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}], "name": "balanceOfBatch", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_requestId", "type": "uint256"}], "name": "cancelTokenization", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "commissionRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_requestId", "type": "uint256"}, {"internalType": "address", "name": "_commissionReceiver", "type": "address"}], "name": "confirmTokenization", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_requestId", "type": "uint256"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "depositTokenization", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "deposits", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}], "name": "deprecateToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_uri", "type": "string"}, {"internalType": "uint40", "name": "_expireAt", "type": "uint40"}, {"internalType": "uint8", "name": "_decimals", "type": "uint8"}, {"internalType": "address[]", "name": "_accounts", "type": "address[]"}, {"internalType": "uint256[]", "name": "_amounts", "type": "uint256[]"}], "name": "directlyTokenize", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}], "name": "exists", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}, {"internalType": "uint40", "name": "_expireAt", "type": "uint40"}], "name": "extendTokenExpiration", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "feeReceiver", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}], "name": "getTokenInfo", "outputs": [{"components": [{"internalType": "uint256", "name": "tokenizationRequestId", "type": "uint256"}, {"internalType": "uint40", "name": "createdAt", "type": "uint40"}, {"internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "bool", "name": "isDeprecated", "type": "bool"}], "internalType": "struct ICollection.TokenInfo", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_requestId", "type": "uint256"}], "name": "getTokenizationRequest", "outputs": [{"components": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "minSellingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "maxSellingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "soldAmount", "type": "uint256"}, {"internalType": "uint256", "name": "unitPrice", "type": "uint256"}, {"internalType": "address", "name": "currency", "type": "address"}, {"internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "uint40", "name": "publicSaleEndsAt", "type": "uint40"}, {"internalType": "address", "name": "requester", "type": "address"}], "internalType": "struct ICollection.TokenizationRequest", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "address", "name": "", "type": "address"}], "name": "hasWithdrawn", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_uri", "type": "string"}, {"internalType": "address", "name": "_admin", "type": "address"}, {"internalType": "address", "name": "_feeReceiver", "type": "address"}, {"internalType": "uint256", "name": "_royaltyFeeRate", "type": "uint256"}, {"internalType": "uint256", "name": "_tokenizationFeeRate", "type": "uint256"}, {"internalType": "uint256", "name": "_commissionRate", "type": "uint256"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}], "name": "isAvailable", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes[]", "name": "_signatures", "type": "bytes[]"}], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_requester", "type": "address"}, {"internalType": "string", "name": "_uri", "type": "string"}, {"internalType": "uint256", "name": "_totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "_minSellingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_maxSellingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "_unitPrice", "type": "uint256"}, {"internalType": "address", "name": "_currency", "type": "address"}, {"internalType": "uint40", "name": "_expireAt", "type": "uint40"}, {"internalType": "uint8", "name": "_decimals", "type": "uint8"}, {"internalType": "uint40", "name": "_duration", "type": "uint40"}], "name": "requestTokenization", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "royaltyFeeRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "_salePrice", "type": "uint256"}], "name": "royaltyInfo", "outputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeBatchTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "_interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenNumber", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenizationFeeRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenizationRequestNumber", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes[]", "name": "_signatures", "type": "bytes[]"}], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_uri", "type": "string"}, {"internalType": "bytes[]", "name": "_signatures", "type": "bytes[]"}], "name": "updateBaseURI", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_commissionRate", "type": "uint256"}, {"internalType": "bytes[]", "name": "_signatures", "type": "bytes[]"}], "name": "updateCommissionRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_governor<PERSON><PERSON>", "type": "address"}, {"internalType": "bytes[]", "name": "_signatures", "type": "bytes[]"}], "name": "updateGovernorHub", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_royaltyFeeRate", "type": "uint256"}, {"internalType": "bytes[]", "name": "_signatures", "type": "bytes[]"}], "name": "updateRoyaltyFeeRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}, {"internalType": "string", "name": "_uri", "type": "string"}], "name": "updateTokenURI", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenizationFeeRate", "type": "uint256"}, {"internalType": "bytes[]", "name": "_signatures", "type": "bytes[]"}], "name": "updateTokenizationFeeRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_tokenId", "type": "uint256"}], "name": "uri", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_requestId", "type": "uint256"}], "name": "withdrawToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_requestId", "type": "uint256"}], "name": "withdrawTokenizationDeposit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}