{"_format": "hh-sol-artifact-1", "contractName": "IDriptributor", "sourceName": "contracts/land/interfaces/IDriptributor.sol", "abi": [{"inputs": [], "name": "AlreadyStaked", "type": "error"}, {"inputs": [], "name": "InsufficientFunds", "type": "error"}, {"inputs": [], "name": "InvalidCurrency", "type": "error"}, {"inputs": [], "name": "InvalidDistributionId", "type": "error"}, {"inputs": [], "name": "InvalidInput", "type": "error"}, {"inputs": [], "name": "InvalidPercentage", "type": "error"}, {"inputs": [], "name": "InvalidUpdating", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "distributionId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": false, "internalType": "uint40", "name": "distributeAt", "type": "uint40"}, {"indexed": false, "internalType": "uint40", "name": "vestingDuration", "type": "uint40"}, {"indexed": false, "internalType": "uint256", "name": "totalAmount", "type": "uint256"}], "name": "NewDistribution", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "distributionId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Stake", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "distributionId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "distributedAmount", "outputs": [{"internalType": "uint256", "name": "distributedAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "distributionNumber", "outputs": [{"internalType": "uint256", "name": "distributionNumber", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "distributionId", "type": "uint256"}], "name": "getDistribution", "outputs": [{"components": [{"internalType": "uint256", "name": "totalAmount", "type": "uint256"}, {"internalType": "uint256", "name": "withdrawnAmount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint40", "name": "distributeAt", "type": "uint40"}, {"internalType": "uint40", "name": "vestingDuration", "type": "uint40"}, {"internalType": "bool", "name": "isStaked", "type": "bool"}], "internalType": "struct IDriptributor.Distribution", "name": "distribution", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "primaryToken", "outputs": [{"internalType": "address", "name": "primaryToken", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "distributionIds", "type": "uint256[]"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stakeToken", "outputs": [{"internalType": "address", "name": "stakeToken", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalAmount", "outputs": [{"internalType": "uint256", "name": "totalAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "version", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "distributionIds", "type": "uint256[]"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}