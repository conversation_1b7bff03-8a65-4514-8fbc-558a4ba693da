{"_format": "hh-sol-artifact-1", "contractName": "Treasury", "sourceName": "contracts/Treasury.sol", "abi": [{"inputs": [], "name": "InsufficientFunds", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "LiquidityProvision", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "LiquidityWithdrawal", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "operator", "type": "address"}], "name": "OperationFundWithdrawal", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "PrimaryTokenUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currency", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_admin", "type": "address"}, {"internalType": "address", "name": "_currency", "type": "address"}, {"internalType": "address", "name": "_primaryToken", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "liquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "operationFund", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes[]", "name": "_signatures", "type": "bytes[]"}], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "primaryToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "provideLiquidity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes[]", "name": "_signatures", "type": "bytes[]"}], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "withdrawLiquidity", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "address", "name": "_operator", "type": "address"}, {"internalType": "bytes[]", "name": "_signatures", "type": "bytes[]"}], "name": "withdrawOperationFund", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}