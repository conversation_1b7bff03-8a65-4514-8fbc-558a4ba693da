import auctionJson from "./abis/Auction.json"
import { Undefinable } from "src/api/types"
import { useReadContract } from "wagmi"

export const auctionAbi = auctionJson.abi

export const useAuctionEndAt = (
  contractAddress?: `0x${string}`
): Undefinable<number> => {
  const { data } = useReadContract({
    abi: auctionAbi,
    address: contractAddress,
    functionName: "endAt",
  })
  return data as Undefinable<number>
}

export const useAuctionTotalDeposit = (
  contractAddress?: `0x${string}`
): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: auctionAbi,
    address: contractAddress,
    functionName: "totalDeposit",
    query: {
      refetchInterval: 15000,
    },
  })
  return data as Undefinable<bigint>
}

export const useAuctionWithdrawableAmount = (
  contractAddress: `0x${string}`,
  userAddress?: string
) => {
  const { data } = useReadContract({
    abi: auctionAbi,
    address: contractAddress,
    functionName: "withdrawableTokenAmountOf",
    args: [userAddress],
    query: {
      refetchInterval: 15000,
    },
  })
  return data as Undefinable<bigint>
}

export const useAuctionTotalIssuance = (
  contractAddress: `0x${string}`
): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: auctionAbi,
    address: contractAddress,
    functionName: "totalToken",
    query: {
      refetchInterval: 15000,
    },
  })
  return data as Undefinable<bigint>
}

export const useAuctionLiquidityPercentage = (
  contractAddress?: `0x${string}`
): Undefinable<number> => {
  const { data } = useReadContract({
    abi: auctionAbi,
    address: contractAddress,
    functionName: "liquidityPercentage",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) {
    return undefined
  }
  return Number(data as bigint) / 100
}

export type AuctionDepositor = {
  depositedAmountWei: bigint
  isWhitelisted: boolean
  hasWithdrawn: boolean
}
export const useAuctionGetDepositor = (
  contractAddress: `0x${string}`,
  userAddress?: string
): Undefinable<AuctionDepositor> => {
  const { data } = useReadContract({
    abi: auctionAbi,
    address: contractAddress,
    functionName: "getDepositor",
    args: [userAddress],
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) {
    return undefined
  }
  const { deposit, isWhitelisted, hasWithdrawn } = data as {
    deposit: bigint
    isWhitelisted: boolean
    hasWithdrawn: boolean
  }
  return {
    depositedAmountWei: deposit,
    isWhitelisted: isWhitelisted,
    hasWithdrawn: hasWithdrawn,
  }
}
