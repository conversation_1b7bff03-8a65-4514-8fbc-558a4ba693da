import { Config, useReadContract } from "wagmi"
import collectionJson from "./abis/Collection.json"
import { CONTRACT_ADDRESS_ESTATE_TOKEN } from "src/config/env"
import { Undefinable } from "src/api/types"

export const collectionAbi = collectionJson.abi

export const useNftBalanceOf = (
  userAddress: string,
  tokenId: string
): bigint => {
  const { data } = useReadContract({
    abi: collectionAbi,
    address: CONTRACT_ADDRESS_ESTATE_TOKEN,
    functionName: "allowance",
    args: [userAddress, BigInt(tokenId)],
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return 0n
  return data as bigint
}

export const useCollectionHasWithdrawn = (
  requestId: string,
  userAddress: string
): boolean => {
  const { data } = useReadContract({
    abi: collectionAbi,
    address: CONTRACT_ADDRESS_ESTATE_TOKEN,
    functionName: "hasWithdrawn",
    args: [BigInt(requestId), userAddress],
    query: {
      refetchInterval: 15000,
    },
  })
  return Boolean(data)
}

export const useCollectionDepositedAmount = (
  requestId: string,
  userAddress: string
): bigint => {
  const { data } = useReadContract({
    abi: collectionAbi,
    address: CONTRACT_ADDRESS_ESTATE_TOKEN,
    functionName: "deposits",
    args: [BigInt(requestId), userAddress],
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return 0n
  return data as bigint
}

export const useCollectionIsApprovedForAll = (
  userAddress: string,
  contractAddress: string
): boolean => {
  const { data } = useReadContract({
    abi: collectionAbi,
    address: CONTRACT_ADDRESS_ESTATE_TOKEN,
    functionName: "isApprovedForAll",
    args: [userAddress, contractAddress],
    query: {
      refetchInterval: 15000,
    },
  })
  return Boolean(data)
}

export type TokenizationRequestContract = {
  requestId: bigint
  tokenId: bigint
  uri: string
  totalSupply: bigint
  minSellingAmount: bigint
  maxSellingAmount: bigint
  soldAmount: bigint
  unitPrice: bigint
  currency: string
  expireAt: number
  publicSaleEndsAt: number
  requester: string
}

export const useCollectionGetTokenizationRequest = (
  requestId: string
): Undefinable<TokenizationRequestContract> => {
  const { data } = useReadContract<
    typeof collectionAbi,
    "getTokenizationRequest",
    [bigint],
    Config,
    Undefinable<TokenizationRequestContract>
  >({
    abi: collectionAbi,
    address: CONTRACT_ADDRESS_ESTATE_TOKEN,
    functionName: "getTokenizationRequest",
    args: [BigInt(requestId)],
    query: {
      refetchInterval: 15000,
    },
  })
  return data
}
