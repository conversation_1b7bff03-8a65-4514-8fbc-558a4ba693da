import erc20UpgradeableJson from "./abis/ERC20Upgradeable.json"
import { useReadContract } from "wagmi"
import { Undefinable } from "src/api/types"
import { useFormatToken, useFormatTokenFixed, useParseToken } from "./utils"

export const erc20Abi = erc20UpgradeableJson.abi

export const useErc20Allowance = (
  userAddress?: string,
  erc20TokenAddress?: `0x${string}`,
  contractAddress?: string
): bigint => {
  const { data } = useReadContract({
    abi: erc20Abi,
    address: erc20TokenAddress,
    functionName: "allowance",
    args: [userAddress, contractAddress],
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return 0n
  return data as bigint
}

export const useErc20Balance = (
  userAddress?: string,
  erc20TokenAddress?: `0x${string}`
): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: erc20Abi,
    address: erc20TokenAddress,
    functionName: "balanceOf",
    args: [userAddress],
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}

export const useErc20Decimals = (erc20TokenAddress?: `0x${string}`) => {
  const { data } = useReadContract({
    abi: erc20Abi,
    address: erc20TokenAddress,
    functionName: "decimals",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return Number(data)
}

export const useErc20Name = (erc20TokenAddress?: `0x${string}`) => {
  const { data } = useReadContract({
    abi: erc20Abi,
    address: erc20TokenAddress,
    functionName: "name",
    query: {
      refetchInterval: 15000,
    },
  })
  return data
}

export const useErc20Symbol = (erc20TokenAddress?: `0x${string}`) => {
  const { data } = useReadContract({
    abi: erc20Abi,
    address: erc20TokenAddress,
    functionName: "symbol",
    query: {
      refetchInterval: 15000,
    },
  })
  return data
}

export const useErc20Formatter = (erc20TokenAddress?: `0x${string}`) => {
  const decimals = useErc20Decimals(erc20TokenAddress)
  const format = useFormatToken(decimals)
  const formatFixed = useFormatTokenFixed(decimals)
  const parse = useParseToken(decimals)

  return { format, formatFixed, parse }
}

export const useErc20TotalSupply = (
  erc20TokenAddress?: `0x${string}`
): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: erc20Abi,
    address: erc20TokenAddress,
    functionName: "totalSupply",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}
