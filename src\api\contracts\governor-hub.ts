import governorHubJson from "./abis/GovernorHub.json"
import { CONTRACT_ADDRESS_GOVERNOR_HUB } from "src/config/env"
import { useReadContract } from "wagmi"

export const governorHubAbi = governorHubJson.abi

export const useExtendExpirationProposalFee = () => {
  const { data } = useReadContract({
    abi: governorHub<PERSON><PERSON>,
    address: CONTRACT_ADDRESS_GOVERNOR_HUB,
    functionName: "extendExpirationProposalFee",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return 0n
  return data as bigint
}
