import treasuryJson from "./abis/Treasury.json"
import { useReadContract } from "wagmi"
import { Undefinable } from "src/api/types"
import { CONTRACT_ADDRESS_TREASURY } from "src/config/env"

export const treasuryAbi = treasuryJson.abi

export const useTreasuryLiquidity = (): Undefinable<bigint> => {
  const { data } = useReadContract({
    abi: treasuryAbi,
    address: CONTRACT_ADDRESS_TREASURY,
    functionName: "liquidity",
    query: {
      refetchInterval: 15000,
    },
  })
  if (!data) return undefined
  return data as bigint
}
