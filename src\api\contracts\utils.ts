import { BigNumberish } from "ethers"
import { formatUnits, parseUnits } from "@ethersproject/units"
import { useMemo } from "react"
import { BASE128, FORMAT_PRECISION, toFixed } from "src/utils"
import Big, { BigSource } from "big.js"

export const useFormatToken = (unitName: BigNumberish | undefined) => {
  return useMemo(() => {
    return (wei: BigNumberish = 0) => {
      if (!unitName) return 0
      return parseFloat(formatUnits(wei, unitName))
    }
  }, [unitName])
}

export const useFormatTokenFixed = (unitName: BigNumberish | undefined) => {
  return useMemo(() => {
    return (wei: BigNumberish = 0, precision: number = FORMAT_PRECISION) => {
      if (!unitName) return Number(0).toFixed(precision)
      return toFixed(formatUnits(wei, unitName), precision)
    }
  }, [unitName])
}

export const useParseToken = (unitName: BigNumberish | undefined = 0) => {
  return useMemo(() => {
    return (value: string) => {
      return parseUnits(value, unitName).toBigInt() || 0n
    }
  }, [unitName])
}

export const useFixedToDecimal = (base: string | number = BASE128) => {
  const formatter = useMemo(() => {
    return (value: BigSource | any) => {
      if (!value) return 0
      return new Big(value).div(base).toNumber()
    }
  }, [base])
  return formatter
}
