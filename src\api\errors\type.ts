// ----------------------- 400_xxx -----------------------
export const BadRequestBindMultipartFormError = new Error("400001")
export const BadRequestInvalidRequestError = new Error("400002")
export const BadRequestInvalidNonceError = new Error("400003")
export const BadRequestInvalidSignatureError = new Error("400004")
export const BadRequestInvalidFileError = new Error("400005")
export const BadRequestParseMultipartFormError = new Error("400006")
export const BadRequestMultipartFileError = new Error("400007")
export const BadRequestMultipartFormError = new Error("400008")
export const BadRequestAtLeast2CredentialPhotosError = new Error("400009")
export const BadRequestAtLeast4EstatePhotosError = new Error("400010")
export const BadRequestZoneNotSupportedError = new Error("400011")
export const BadRequestBindPaginationError = new Error("400012")
export const BadRequestInvalidPathParamError = new Error("400013")
export const BadRequestWrongBrokerEmailCodeError = new Error("400014")
export const BadRequestBindFilterQueryError = new Error("400015")
export const BadRequestInvalidQueryParamError = new Error("400016")
export const BadRequestUnavailableCurrencyError = new Error("400017")
export const BadRequestInvalidUnitPriceError = new Error("400018")
export const BadRequestBrokerEmailVerifiedError = new Error("400019")
export const BadRequestBrokerEmailCodeExpiredError = new Error("400020")

// ----------------------- 401_xxx -----------------------
export const UnauthorizedInvalidAuthHeaderError = new Error("401001")
export const UnauthorizedInvalidJWTError = new Error("401002")
export const UnauthorizedMalformedJWTError = new Error("401003")
export const UnauthorizedWrongPasswordError = new Error("401004")

// ----------------------- 403_xxx -----------------------
export const ForbiddenError = new Error("403001")
export const ForbiddenUserVerifiedError = new Error("403002")

// ----------------------- 404_xxx -----------------------
export const NotFoundNonceError = new Error("404001")
export const NotFoundCurrencyError = new Error("404002")
export const NotFoundBrokerWalletError = new Error("404003")
export const NotFoundEstateTokenMetadataError = new Error("404004")
export const NotFoundEstateTokenApplicationError = new Error("404005")
export const NotFoundBrokerEmailError = new Error("404006")

// ----------------------- 500_xxx -----------------------
export const InternalError = new Error("500000")
export const InternalErrorGenerateNonceError = new Error("500001")
export const InternalErrorSetNonceRedisError = new Error("500002")
export const InternalErrorGetNonceRedisError = new Error("500003")
export const InternalErrorMakeJWTStringError = new Error("500004")
export const InternalErrorMyAddressGinCtxError = new Error("500005")
export const InternalErrorMyAddressGinCtxValidError = new Error("500006")
export const InternalErrorGetOrCreateMeError = new Error("500007")
export const InternalErrorCreateEstateTokenMetadataError = new Error("500008")
export const InternalErrorCreateTokenizationAppError = new Error("500009")
export const InternalErrorUploadTokenizationAppFilesError = new Error("500010")
export const InternalErrorCreateTokenizationAppFilesError = new Error("500011")
export const InternalErrorDatabaseError = new Error("500012")
export const InternalErrorGetFileS3Error = new Error("500013")
export const InternalErrorPinFilePinataError = new Error("500014")
export const InternalErrorMarshalEstateTokenJSONError = new Error("500015")
export const InternalErrorEncryptBrokerPasswordError = new Error("500016")
export const InternalErrorSendEmailError = new Error("500017")
export const InternalErrorEmailVerificationNilError = new Error("500018")
export const InternalErrorBrokerIDGinCtxError = new Error("500019")
export const InternalErrorInvalidBrokerIDGinCtxError = new Error("500020")
export const InternalErrorOpenNationalIDCardImageError = new Error("500021")
export const InternalErrorUploadNationalIDCardImageError = new Error("500022")
export const InternalErrorCreateFileRecordError = new Error("500023")
export const InternalErrorOverrideUserKYCDataError = new Error("500024")
export const InternalErrorOpenAvatarImageError = new Error("500025")
export const InternalErrorUploadAvatarImageError = new Error("500026")
export const InternalErrorOverrideUserDataError = new Error("500027")
export const InternalErrorFindEstateError = new Error("500028")
export const InternalErrorCreateAppraisalDocumentsError = new Error("500029")
export const InternalErrorCreateLandRegistryDocumentsError = new Error("500030")
