import { queryOptions } from "@tanstack/react-query"
import {
  EXPO_PUBLIC_BASE_API_URL,
  EXPO_PUBLIC_BASE_API_V2_URL,
} from "src/config/env"
import axiosInstance, { setAuthToken } from "src/api/axiosInstance"
import * as types from "src/api/types"
import { EstateActivity, ListResponse, SignInResponse } from "src/api/types"
import { QueryKeys } from "src/config/queryKeys"
import Logger from "src/utils/logger"
import { LeaderboardData, LeaderboardResponse } from "./types/leaderboard"

const logger = new Logger({ tag: "API" })

const baseUrl = EXPO_PUBLIC_BASE_API_URL
const baseUrlV2 = EXPO_PUBLIC_BASE_API_V2_URL

export * from "src/api/types"

const getAuthHeader = (address?: string) => {
  return {
    "X-Timestamp": Date.now().toString(),
    "X-Chain-Address": address ?? "",
  }
}

// Lọc bỏ các giá trị undefined để tránh gửi params không cần thiết
const removeUndefinedValues = (
  params: Record<string, any>
): Record<string, any> => {
  return Object.fromEntries(
    Object.entries(params).filter((entry) => entry[1] !== undefined)
  )
}

export const getHomeLeaderboard = async (): Promise<LeaderboardData> => {
  const res = await axiosInstance.get<LeaderboardResponse>(
    `${baseUrl}/briky/api/estates/leaderboard`
  )
  return res.data.data
}

export const getHomeRecentOffers = async (): Promise<
  types.MarketplaceOffer[]
> => {
  const res = await axiosInstance.get<{
    meta: Record<string, any>
    data: types.MarketplaceOffer[]
  }>(`${baseUrl}/briky/api/leaderboard/recent-offers`)
  return res.data.data || []
}

export const getMyProfile = async (address?: string): Promise<types.User> => {
  const response = await axiosInstance.get<{ data: types.User }>(
    `${baseUrl}/briky/api/users/${address}`,
    {
      headers: {
        ...getAuthHeader(address),
      },
    }
  )
  return response.data.data
}

export const verifyProfile = async (formData: FormData): Promise<string> => {
  const res = await axiosInstance.patch<{ data?: string }>(
    `${baseUrl}/briky/api/users/me/kyc-data`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    }
  )
  return res.data?.data ?? ""
}

export const updateProfile = async (payload: FormData) => {
  return axiosInstance.put<{ data?: string }>(
    `${baseUrl}/briky/api/users/me`,
    payload,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    }
  )
}

export const uploadAvatar = async (payload: FormData) => {
  return axiosInstance.patch<{ data?: string }>(
    `${baseUrl}/briky/api/users/me/avatar`,
    payload
  )
}

export const updateAvatar = async (formData: FormData): Promise<string> => {
  const res = await axiosInstance.patch<{ data?: string }>(
    `${baseUrl}/briky/api/users/me/avatar`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    }
  )
  return res.data?.data ?? ""
}

export const getCurrencies = async (): Promise<types.Currency[]> => {
  const res = await axiosInstance.get(`${baseUrl}/briky/api/currencies`)
  return res.data.data
}

export const fetchMyReferralsQueryOptions = (address?: string) => {
  return queryOptions({
    queryKey: [QueryKeys.REFERRAL.LIST],
    queryFn: () => fetchMyReferrals(address),
  })
}

export const fetchMyReferrals = async (
  address?: string
): Promise<types.Referral[]> => {
  logger.debug("Fetching user referrals", { address })
  return []
}

export const postMyReferral = async (payload: FormData, address?: string) => {
  // TODO: remove this
  return axiosInstance.post<{ data?: string }>(
    `${baseUrl}/briky/api/users/me/referrals`,
    payload,
    {
      headers: getAuthHeader(address),
    }
  )
}

export const createApplication = async (
  formData: FormData
): Promise<string> => {
  const res = await axiosInstance.post<{ data?: string }>(
    `${baseUrl}/briky/api/users/me/applications`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    }
  )
  return res.data?.data ?? ""
}

export const submitFeedback = async (formData: FormData): Promise<string> => {
  const res = await axiosInstance.post<{ data?: string }>(
    `${baseUrl}/briky/api/in-touch`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    }
  )
  return res.data?.data ?? ""
}

export const getEstateRequests = async (
  params: any
): Promise<types.TokenizationRequest[]> => {
  const res = await axiosInstance.get<{
    data?: { list: types.TokenizationRequest[] }
  }>(`${baseUrl}/briky/api/estates/requests`, {
    params,
  })
  return res.data?.data?.list || []
}

export const getMyTokenizations = async (
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<ListResponse<types.MyTokenization>> => {
  const res = await axiosInstance.get(
    `${baseUrl}/briky/api/users/me/tokenizations`,
    {
      params,
    }
  )
  return res.data?.data
}

export const getEstates = async (
  params: {
    itemsPerPage: number
    currentPage: number
    sortDescending?: boolean
    sort?: string
  } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<types.Estate[]> => {
  const res = await axiosInstance.get<{ data?: { list: types.Estate[] } }>(
    `${baseUrl}/briky/api/estates`,
    {
      params,
    }
  )
  return res.data?.data?.list || []
}

export const getMyEstates = async (
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<ListResponse<types.MyEstate>> => {
  const res = await axiosInstance.get(`${baseUrl}/briky/api/users/estates`, {
    params,
  })
  return res.data?.data
}

export const getMarketPlaceOffers = async (
  estateId?: string,
  itemsPerPage: number = 100,
  currentPage: number = 0
): Promise<types.MarketplaceOffer[]> => {
  const params = {
    itemsPerPage,
    currentPage,
    estateId,
  }
  const res = await axiosInstance.get<{
    data?: { list: types.MarketplaceOffer[] }
  }>(`${baseUrl}/briky/api/offers`, {
    params,
  })
  return res.data?.data?.list || []
}

export const getPaginatedMarketPlaceOffers = async (
  estateId?: string,
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<ListResponse<types.MarketplaceOffer>> => {
  const res = await axiosInstance.get(`${baseUrl}/briky/api/offers`, {
    params: {
      ...params,
      estateId,
    },
  })
  return res.data?.data
}

export const getMarketPlaceOffersFilter = async ({
  itemsPerPage = 100,
  currentPage = 0,
  states,
}: {
  itemsPerPage?: number
  currentPage?: number
  states?: string
} = {}): Promise<types.MarketplaceOffer[]> => {
  const params = removeUndefinedValues({ itemsPerPage, currentPage, states })

  const res = await axiosInstance.get<{
    data?: { list: types.MarketplaceOffer[] }
  }>(`${baseUrl}/briky/api/offers`, { params })

  return res.data?.data?.list ?? []
}

export const getMyMarketPlaceOffers = async (
  seller?: string,
  itemsPerPage: number = 100,
  currentPage: number = 0
): Promise<types.MarketplaceOffer[]> => {
  const params = {
    itemsPerPage,
    currentPage,
    seller,
  }
  const res = await axiosInstance.get<{
    data?: { list: types.MarketplaceOffer[] }
  }>(`${baseUrl}/briky/api/offers`, {
    params,
  })
  return res.data?.data?.list || []
}

export const getMyActivities = async (
  account?: string,
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<ListResponse<types.EstateActivity>> => {
  const res = await axiosInstance.get(
    `${baseUrl}/briky/api/users/me/estates/activities`,
    {
      params: {
        ...params,
        account,
      },
    }
  )
  return res.data?.data
}

export const getMortgageLoans = async (
  estateId?: string,
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<types.MortgageTokenLoan[]> => {
  const res = await axiosInstance.get<{
    data?: { list: types.MortgageTokenLoan[] }
  }>(`${baseUrl}/briky/api/loans`, {
    params,
    ...(estateId && {
      params: {
        ...params,
        estateId,
      },
    }),
  })
  return res.data?.data?.list || []
}

export const getMortgageLoansFilter = async ({
  itemsPerPage = 100,
  currentPage = 0,
  states,
}: {
  itemsPerPage?: number
  currentPage?: number
  states?: string
} = {}): Promise<types.MortgageTokenLoan[]> => {
  const params = removeUndefinedValues({ itemsPerPage, currentPage, states })

  const res = await axiosInstance.get<{
    data?: { list: types.MortgageTokenLoan[] }
  }>(`${baseUrl}/briky/api/loans`, { params })

  return res.data?.data?.list ?? []
}

export const getMyMortgageLoans = async (
  involvedAccount?: string,
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<types.MortgageTokenLoan[]> => {
  const res = await axiosInstance.get<{
    data?: { list: types.MortgageTokenLoan[] }
  }>(`${baseUrl}/briky/api/loans`, {
    params,
    ...(involvedAccount && {
      params: {
        ...params,
        involvedAccount,
      },
    }),
  })
  return res.data?.data?.list || []
}

export const getMortgageOffers = async (
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<ListResponse<types.MarketplaceOffer>> => {
  const res = await axiosInstance.get(`${baseUrl}/briky/api/offers`, {
    params,
  })
  return res.data?.data
}

export const getApplications = async (
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 1,
  }
): Promise<ListResponse<types.Application>> => {
  const res = await axiosInstance.get(
    `${baseUrl}/briky/api/users/applications`,
    {
      params,
    }
  )
  return res.data?.data
}

export const getEstateRequestDetailById = async (
  id: string
): Promise<types.TokenizationRequest> => {
  const res = await axiosInstance.get<{ data: types.TokenizationRequest }>(
    `${baseUrl}/briky/api/estates/requests/${id}`
  )
  return res.data.data
}

export const getDepositorsByEstateRequestId = async (
  id: string
): Promise<types.TokenizationDepositor[]> => {
  const res = await axiosInstance.get<{
    data?: { list: types.TokenizationDepositor[] }
  }>(`${baseUrl}/briky/api/estates/requests/${id}/depositors`)
  return res.data?.data?.list || []
}

export const getPaginatedDepositorsByEstateRequestId = async (
  id: string,
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<ListResponse<types.TokenizationDepositor>> => {
  const res = await axiosInstance.get(
    `${baseUrl}/briky/api/estates/requests/${id}/depositors`,
    {
      params,
    }
  )
  return res.data?.data
}

export const getEstateDetailById = async (
  id: string
): Promise<types.Estate> => {
  const res = await axiosInstance.get<{ data: types.Estate }>(
    `${baseUrl}/briky/api/estates/${id}`
  )
  return res.data.data
}

export const getOracles = async (): Promise<types.Oracle> => {
  const res = await axiosInstance.get<{ data: types.Oracle }>(
    `${baseUrlV2}/land/api/oracles`
  )
  return res.data.data
}

export const getEstateBalances = async (
  id: string
): Promise<types.EstateTokenBalance[]> => {
  const res = await axiosInstance.get<{
    data?: { list: types.EstateTokenBalance[] }
  }>(`${baseUrl}/briky/api/estates/${id}/balances`)
  return res.data?.data?.list || []
}

export const getPaginatedEstateTokenBalances = async (
  id: string,
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<ListResponse<types.EstateTokenBalance>> => {
  const res = await axiosInstance.get(
    `${baseUrl}/briky/api/estates/${id}/balances`,
    {
      params,
    }
  )
  return res.data?.data
}

export const getDepositListByRequestId = async (
  id: string,
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 0,
  }
): Promise<ListResponse<types.TokenizationDeposit>> => {
  const res = await axiosInstance.get(
    `${baseUrl}/briky/api/estates/requests/${id}/deposits`,
    {
      params,
    }
  )
  return res.data.data
}

export const getNonce = async (address: string): Promise<string> => {
  const formData = new FormData()
  formData.append("address", address)

  const res = await axiosInstance.post<{ data: { nonce: string } }>(
    `${baseUrl}/briky/api/auth/nonce`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    }
  )

  return res.data.data.nonce
}

export const getEstateActivities = async (
  id: string,
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 1,
  }
): Promise<ListResponse<EstateActivity>> => {
  const res = await axiosInstance.get(
    `${baseUrl}/briky/api/estates/${id}/activities`,
    { params }
  )
  return res.data?.data
}

export const getPaginatedEstateActivities = async (
  id: string,
  params: { itemsPerPage: number; currentPage: number } = {
    itemsPerPage: 100,
    currentPage: 1,
  }
): Promise<ListResponse<EstateActivity>> => {
  const res = await axiosInstance.get(
    `${baseUrl}/briky/api/estates/${id}/activities`,
    { params }
  )
  return res.data?.data
}

export const getApplicationDetailById = async (
  id: string
): Promise<types.Application> => {
  const res = await axiosInstance.get<{ data: types.Application }>(
    `${baseUrl}/briky/api/estates/applications/${id}`
  )
  return res.data.data
}

export const getLegalRequirements = async (
  id: string
): Promise<ListResponse<types.LegalRequirement>> => {
  const res = await axiosInstance.get(
    `${baseUrl}/briky/api/estates/metadatas/${id}/documents`
  )
  return res.data?.data
}

// Helper function to handle auth response and token storage
const handleAuthResponse = async (
  responseData: SignInResponse,
  address?: string
): Promise<SignInResponse> => {
  if (responseData) {
    const {
      token,
      tokenExpiredTimeInSeconds,
      refreshToken,
      refreshTokenExpiredTimeInSeconds,
    } = responseData
    await setAuthToken(
      token,
      tokenExpiredTimeInSeconds,
      refreshToken,
      refreshTokenExpiredTimeInSeconds,
      address
    )
  }
  return responseData
}

export const login = async (
  address: string,
  nonce: string,
  signature: string
): Promise<SignInResponse> => {
  const formData = new FormData()
  formData.append("address", address)
  formData.append("nonce", nonce)
  formData.append("signature", signature)

  const res = await axiosInstance.post(
    `${baseUrl}/briky/api/auth/sign-in`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    }
  )

  return handleAuthResponse(res.data.data, address)
}

export const refreshToken = async (
  refreshToken: string
): Promise<SignInResponse> => {
  const formData = new FormData()
  formData.append("refreshToken", refreshToken)
  const res = await axiosInstance.post(
    `${baseUrl}/briky/api/auth/refresh`,
    formData,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    }
  )
  return handleAuthResponse(res.data.data)
}
