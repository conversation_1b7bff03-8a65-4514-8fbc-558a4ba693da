import {
  QueryClient,
  type QueryFunction,
  useQuery,
  UseQueryOptions,
} from "@tanstack/react-query"
import { useEffect } from "react"
import { useHandleError } from "./errors/handleError"

export const buildQueryOptions = <T>(fn: QueryFunction<T>) => ({
  queryKey: [fn.name],
  queryFn: fn,
})

export const queryClient = new QueryClient()

export const useQueryWithErrorHandling = <T>(options: UseQueryOptions<T>) => {
  const { handleError } = useHandleError()
  const { data, isLoading, isFetching, error, refetch } = useQuery<T>(options)

  useEffect(() => {
    if (error && !isFetching && !isLoading) {
      handleError(error)
    }
  }, [error, isFetching, isLoading, handleError])

  return { data, isLoading, isFetching, error, refetch }
}
