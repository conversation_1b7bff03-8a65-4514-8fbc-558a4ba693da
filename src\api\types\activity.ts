import { EstateTokenEstateBrief, UserBrief } from "src/api/types/estate"

export enum EstateActivityCategory {
  SALE = "SALE",
  TRANSFER = "TRANSFER",
  RETRIEVE = "RETRIEVE",
  FORECLOSED = "FORECLOSED",
  MORTGAGE = "MORTGAGE",
}

export type EstateActivity = {
  txHash: string
  amount: string
  blockTimestamp: number
  category: EstateActivityCategory
  from: UserBrief
  to: UserBrief
  estate: EstateTokenEstateBrief
}
