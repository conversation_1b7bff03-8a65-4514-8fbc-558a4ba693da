import React from "react"
import { Pressable, StyleSheet, Text, View } from "react-native"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"

interface DetailItemViewProps {
  label: string
  value: string
  onPress?: () => void
}

const DetailItemView: React.FC<DetailItemViewProps> = ({
  label,
  value,
  onPress,
}) => {
  return (
    <View style={styles.detailItem}>
      <Text style={styles.detailLabel}>{label}</Text>
      {onPress ? (
        <Pressable style={styles.detailPressable} onPress={onPress}>
          <Text style={styles.detailValueLink}>{value}</Text>
        </Pressable>
      ) : (
        <Text style={styles.detailValue}>{value}</Text>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  detailItem: {
    flexDirection: "row",
    marginVertical: 4,
  },
  detailLabel: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
    flex: 1,
  },
  detailPressable: {
    flexDirection: "row",
  },
  detailValueLink: {
    ...textStyles.bodyM,
    color: Colors.blueLink,
    marginHorizontal: 4,
  },
  detailValue: {
    ...textStyles.bodyM,
  },
})

export { DetailItemView }
