import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { CustomPressable } from "./common/CustomPressable"
import { textStyles, viewStyles } from "src/config/styles"
import filterIcon from "assets/images/ic_filter.png"

interface FilterViewProps {
  onClickFilter: () => void
}

const FilterView: React.FC<FilterViewProps> = ({ onClickFilter }) => {
  const { t } = useTranslation()

  return (
    <CustomPressable onPress={onClickFilter}>
      <View style={styles.container}>
        <Image source={filterIcon} style={viewStyles.smallIcon} />
        <Text style={styles.title}>{t("Filters")}</Text>
      </View>
    </CustomPressable>
  )
}

export { FilterView }

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    borderRadius: 8,
    padding: 8,
    alignItems: "center",
    alignSelf: "flex-start",
    backgroundColor: Colors.surface,
  },
  button: {
    marginRight: 10,
    width: 40,
    height: 40,
    padding: 8,
    backgroundColor: Colors.surface,
    borderRadius: 8,
  },
  title: {
    ...textStyles.labelL,
    marginStart: 8,
  },
})
