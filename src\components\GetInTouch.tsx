import React, { useCallback } from "react"
import { Image, StyleSheet, Text } from "react-native"
import Colors, { textColors } from "src/config/colors"
import { PrimaryButton } from "./common/Button"
import { textStyles, viewStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import { Controller, useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { InputField } from "./common/InputField"
import { useMutation } from "@tanstack/react-query"
import { submitFeedback } from "src/api"
import teleIcon from "assets/images/ic_tele.png"
import { showSuccess } from "src/utils/toast"
import { CardView } from "./common/CardView"
import { useHandleError } from "src/api/errors/handleError"

const useFormSchema = () => {
  const { t } = useTranslation()

  const formSchema = z.object({
    fullName: z
      .string()
      .min(2, t("Full name must be at least 2 characters"))
      .max(100, t("Full name must be at most 100 characters")),
    email: z.string().email(t("Invalid email")),
    message: z
      .string()
      .min(1, t("Please input message"))
      .max(512, t("Message must not exceed 512 characters")),
  })

  return formSchema
}

const GetInTouch: React.FC = () => {
  const { t } = useTranslation()
  const { handleError } = useHandleError()
  const formSchema = useFormSchema()

  type FormValues = z.infer<typeof formSchema>

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      message: "",
    },
  })

  const { mutate } = useMutation({
    mutationFn: (formData: FormData) => {
      return submitFeedback(formData)
    },
    onSuccess: () => {
      form.reset()
      showSuccess(t("We have received your message. Thank you!"))
    },
    onError: (err) => {
      handleError(err, t("Request sent failed"))
    },
  })

  const onSubmit = useCallback(
    (data: FormValues) => {
      const body = new FormData()
      body.append("email", data.email)
      body.append("full_name", data.fullName)
      body.append("message", data.message)
      mutate(body)
    },
    [mutate]
  )

  const renderInput = useCallback(
    (
      name: keyof FormValues,
      label: string,
      options?: {
        multiline?: boolean
        height?: number
      }
    ) => (
      <Controller
        control={form.control}
        name={name}
        render={({ field: { onChange, onBlur, value } }) => (
          <InputField
            label={t(label)}
            value={value}
            onBlur={onBlur}
            style={{ marginTop: 8 }}
            onChangeText={onChange}
            error={form.formState.errors[name]?.message}
            {...options}
          />
        )}
      />
    ),
    [form.control, t]
  )

  return (
    <CardView style={styles.container}>
      <Text style={[styles.title, textStyles.titleL]}>{t("Get in touch")}</Text>
      {renderInput("fullName", t("Full name"))}
      {renderInput("email", t("Email"))}
      {renderInput("message", t("Message"), {
        multiline: true,
        height: 100,
      })}
      <PrimaryButton
        onPress={form.handleSubmit(onSubmit)}
        color={Colors.primary}
        contentColor={textColors.textBlack}
        borderRadius={8}
        width={120}
        title={t("Send")}
        icon={<Image source={teleIcon} style={viewStyles.icon} />}
        style={styles.button}
      />
    </CardView>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    borderWidth: 1,
    borderColor: Colors.black5,
    padding: 12,
    margin: 4,
    borderRadius: 12,
    marginBottom: 60,
  },
  title: {
    marginTop: 12,
    marginBottom: 16,
    width: "100%",
    textAlign: "center",
  },
  label: {
    marginTop: 20,
  },
  input: {
    width: "100%",
    borderRadius: 8,
    borderWidth: 0.5,
    borderColor: Colors.border,
    marginTop: 8,
    paddingHorizontal: 12,
    ...textStyles.bodyM,
  },
  button: {
    marginTop: 20,
    width: "100%",
    alignSelf: "center",
  },
})

export { GetInTouch }
