import React from "react"
import { StyleSheet, Text, View } from "react-native"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { CustomPressable } from "components/common/CustomPressable"

interface GroupButtonTitleViewProps {
  buttonTitles: string[]
  selectedIndex: number
  setButtonIndex: (index: number) => void
}

interface ButtonTitleProps {
  title: string
  selected: boolean
  onPress: () => void
}

const ButtonTitle: React.FC<ButtonTitleProps> = ({
  title,
  selected,
  onPress,
}) => (
  <CustomPressable
    style={selected ? styles.activeButton : styles.button}
    onPress={onPress}
  >
    <Text style={styles.title}>{title}</Text>
  </CustomPressable>
)

const GroupButtonTitleView: React.FC<GroupButtonTitleViewProps> = ({
  buttonTitles,
  selectedIndex,
  setButtonIndex,
}) => {
  return (
    <View style={styles.container}>
      {buttonTitles.map((title, index) => (
        <ButtonTitle
          key={title}
          title={title}
          selected={selectedIndex === index}
          onPress={() => setButtonIndex(index)}
        />
      ))}
    </View>
  )
}

export { GroupButtonTitleView }

const styles = StyleSheet.create({
  title: {
    ...textStyles.labelM,
    paddingHorizontal: 8,
  },
  container: {
    flexDirection: "row",
    flexWrap: "wrap",
    paddingVertical: 10,
  },
  button: {
    backgroundColor: Colors.black3,
    paddingVertical: 4,
    paddingHorizontal: 2,
    borderRadius: 8,
    marginVertical: 4,
    marginEnd: 4,
  },
  activeButton: {
    backgroundColor: Colors.primaryLight,
    paddingVertical: 4,
    paddingHorizontal: 2,
    borderRadius: 8,
    marginVertical: 4,
    marginEnd: 4,
  },
})
