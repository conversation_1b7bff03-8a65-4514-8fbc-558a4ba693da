import React, { useContext } from "react"
import { Image, Pressable, StyleSheet, Text, View } from "react-native"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import walletIcon from "assets/images/ic_wallet.png"
import { AuthContext } from "src/context/AuthContext"

export const LoginView: React.FC = () => {
  const { t } = useTranslation()
  const authContext = useContext(AuthContext)

  const handleLogin = async () => {
    authContext.connectWallet()
  }

  const renderLoginButton = () => (
    <Pressable onPress={handleLogin}>
      <View style={styles.loginButton}>
        <Image source={walletIcon} style={styles.walletIcon} />
        <Text style={[textStyles.labelL, styles.loginText]}>{t("Login")}</Text>
      </View>
    </Pressable>
  )

  return <>{renderLoginButton()}</>
}

const styles = StyleSheet.create({
  walletIcon: {
    width: 24,
    height: 24,
    marginEnd: 8,
  },
  loginButton: {
    width: 110,
    height: 40,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    backgroundColor: Colors.primary,
  },
  loginText: {
    fontWeight: "700",
  },
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: Colors.opacityBlack60,
  },
  dismissArea: {
    flex: 1,
  },
  bottomSheetContainer: {
    width: "100%",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: "hidden",
  },
  guideContainer: {
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.white,
    alignSelf: "stretch",
    paddingVertical: 24,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  guideIcon: {
    width: 80,
    height: 80,
  },
  guideSteps: {
    alignSelf: "stretch",
    paddingHorizontal: 16,
  },
})
