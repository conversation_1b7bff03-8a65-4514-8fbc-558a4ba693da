import React, { useContext } from "react"
import { Image, Pressable, StyleSheet, Text, View } from "react-native"
import { IconButton } from "react-native-paper"
import { DrawerNavigationProp } from "@react-navigation/drawer"
import { ParamListBase } from "@react-navigation/routers"
import { useNavigation } from "@react-navigation/native"
import { shortenAddress } from "utils/stringExt"
import Colors from "src/config/colors"
import { Router } from "src/navigation/Router"
import { textStyles } from "src/config/styles"
import { useAccount } from "wagmi"
import walletIcon from "assets/images/ic_wallet.png"
import logoWithText from "assets/images/logo-with-text.png"
import { LoginView } from "./LoginView"
import { AuthContext } from "src/context/AuthContext"

interface AddressViewProps {
  address: string
}

const WalletIcon: React.FC = () => (
  <Image source={walletIcon} style={styles.walletIcon} />
)

const AddressView: React.FC<AddressViewProps> = ({ address }) => (
  <View style={styles.addressContainer}>
    <WalletIcon />
    <Text style={[textStyles.bodyM, styles.addressText]}>
      {shortenAddress(address)}
    </Text>
  </View>
)

const Logo: React.FC<{ onPress: () => void }> = ({ onPress }) => (
  <Pressable style={styles.expand} onPress={onPress}>
    <Image style={styles.logo} source={logoWithText} resizeMode="cover" />
  </Pressable>
)

const NavigationBar: React.FC = () => {
  const { address } = useAccount()
  const { isAuthenticated } = useContext(AuthContext)
  const navigation = useNavigation<DrawerNavigationProp<ParamListBase>>()
  const handleOpenDrawer = () => navigation.openDrawer()
  const handleGoHome = () => navigation.navigate(Router.HomePath)
  return (
    <>
      <View style={styles.appBar}>
        <Logo onPress={handleGoHome} />
        {isAuthenticated ? <AddressView address={address!} /> : <LoginView />}
        <IconButton icon="menu" size={24} onPress={handleOpenDrawer} />
      </View>
    </>
  )
}

export default NavigationBar

const styles = StyleSheet.create({
  expand: {
    flex: 1,
  },
  logo: {
    width: 88,
    height: 36,
    marginStart: 16,
  },
  appBar: {
    flexDirection: "row",
    height: 48,
    alignItems: "center",
    backgroundColor: Colors.Black,
  },
  addressContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  walletIcon: {
    width: 24,
    height: 24,
    marginEnd: 8,
  },
  addressText: {
    fontWeight: "500",
  },
  loginButton: {
    width: 110,
    height: 40,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    backgroundColor: Colors.primary,
  },
  loginText: {
    fontWeight: "700",
  },
})
