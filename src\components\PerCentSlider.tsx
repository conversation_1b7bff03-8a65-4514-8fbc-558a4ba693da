import React from "react"
import { StyleSheet } from "react-native"
import Slider from "@react-native-community/slider"
import Colors from "src/config/colors"

interface PercentSliderProps {
  onAmountChange?: (percent: number) => void
  amount?: number
  maxAmount?: number
  disabled?: boolean
}

export const PerCentSlider: React.FC<PercentSliderProps> = ({
  onAmountChange,
  amount = 0,
  maxAmount = 0,
  disabled = false,
}) => {
  const isDisabled = disabled || maxAmount <= 0
  const percentage = maxAmount > 0 ? (amount * 100) / maxAmount : 0
  const thumbColor = isDisabled ? Colors.gray600 : Colors.primary

  return (
    <Slider
      style={styles.slider}
      minimumValue={0}
      maximumValue={100}
      value={percentage}
      step={1}
      onValueChange={onAmountChange}
      disabled={isDisabled}
      minimumTrackTintColor={Colors.primary}
      maximumTrackTintColor={Colors.gray600}
      thumbTintColor={thumbColor}
    />
  )
}

const styles = StyleSheet.create({
  slider: {
    marginTop: 16,
  },
})
