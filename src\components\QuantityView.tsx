import React from "react"
import { Image, StyleSheet, TextInput, View } from "react-native"
import { CustomPressable } from "./common/CustomPressable"
import Colors, { textColors } from "src/config/colors"
import minusIcon from "assets/images/ic_minus.png"
import plusIcon from "assets/images/ic_plus.png"
import { viewStyles } from "src/config/styles"

interface QuantityViewProps {
  value?: number
  onChangeValue: (value: number) => void
  minValue?: number
  maxValue?: number
}

const QuantityView: React.FC<QuantityViewProps> = ({
  value = 0,
  onChangeValue,
  minValue = 0,
  maxValue = Number.MAX_SAFE_INTEGER,
}) => {
  const handleValueChange = (newValue: number) => {
    if (newValue >= minValue && newValue <= maxValue) {
      onChangeValue(newValue)
    }
  }

  const onMinus = () => handleValueChange(value - 1)
  const onPlus = () => handleValueChange(value + 1)

  const onChangeText = (text: string) => {
    const textNumber = text === "" ? minValue : parseInt(text)
    if (isNaN(textNumber)) return
    handleValueChange(textNumber)
  }

  return (
    <View style={styles.container}>
      <CustomPressable onPress={onMinus} enabled={value > minValue}>
        <Image style={viewStyles.tinyIcon} source={minusIcon} />
      </CustomPressable>
      <TextInput
        style={styles.input}
        value={value.toString()}
        onChangeText={onChangeText}
        keyboardType="numeric"
      />
      <CustomPressable onPress={onPlus} enabled={value < maxValue}>
        <Image style={viewStyles.tinyIcon} source={plusIcon} />
      </CustomPressable>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    textAlign: "center",
    borderWidth: 1,
    height: 40,
    borderRadius: 8,
    width: 60,
    marginHorizontal: 8,
    borderColor: Colors.black5,
    color: textColors.textBlack11,
  },
})

export { QuantityView }
