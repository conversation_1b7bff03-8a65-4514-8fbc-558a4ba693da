import React from "react"
import { Image, StyleSheet, TextInput, View, ViewStyle } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { InputModeOptions } from "react-native/Libraries/Components/TextInput/TextInput"
import { ErrorLabel } from "./common/Label"
import searchIcon from "assets/images/ic_search.png"

interface SearchViewProps {
  label?: string
  value: string | number
  onChangeText: (text: string | number) => void
  placeholder?: string
  multiline?: boolean
  height?: number
  inputMode?: InputModeOptions
  style?: ViewStyle
  onBlur?: () => void
  error?: string
  type?: "string" | "number"
}

const SearchView: React.FC<SearchViewProps> = ({
  value,
  onChangeText,
  placeholder,
  multiline = false,
  height = 40,
  inputMode = "text",
  style,
  onBlur,
  error,
  type = "string",
}) => {
  const handleChangeText = (text: string) => {
    if (type === "number" && !Number.isNaN(Number(text))) {
      onChangeText(Number(text))
    } else {
      onChangeText(text)
    }
  }

  return (
    <View style={style}>
      <View style={styles.inputContainer}>
        <TextInput
          onBlur={onBlur}
          value={value.toString()}
          onChangeText={handleChangeText}
          placeholder={placeholder}
          multiline={multiline}
          inputMode={inputMode}
          style={[styles.input, { height }]}
        />
        <Image
          source={searchIcon}
          style={[viewStyles.tinyIcon, styles.margingEndDefault]}
        />
      </View>
      <ErrorLabel error={error} />
    </View>
  )
}

const styles = StyleSheet.create({
  label: {
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.black5,
  },
  input: {
    flex: 1,
    paddingHorizontal: 12,
    ...textStyles.bodyM,
    color: textColors.textBlack9,
    paddingVertical: 12,
  },
  margingEndDefault: {
    marginEnd: 8,
  },
})

export { SearchView }
