import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { CustomPressable } from "./common/CustomPressable"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import nextIcon from "assets/images/ic_next.png"

interface SelectItemViewProps {
  icon: React.ReactNode
  title: string
  note?: string
  showNextIcon?: boolean
  onPress: () => void
}

const SelectItemView: React.FC<SelectItemViewProps> = ({
  icon,
  title,
  note,
  showNextIcon = true,
  onPress,
}) => {
  return (
    <CustomPressable onPress={onPress}>
      <View style={styles.settingItem}>
        <View style={styles.row}>
          {icon}
          <Text style={styles.title}>{title}</Text>
        </View>
        <View style={styles.row}>
          {note && <Text style={styles.note}>{note}</Text>}
          {showNextIcon && <Image source={nextIcon} style={viewStyles.icon} />}
        </View>
      </View>
    </CustomPressable>
  )
}

const styles = StyleSheet.create({
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  settingItem: {
    justifyContent: "space-between",
    flexDirection: "row",
    padding: 12,
    borderRadius: 8,
    marginTop: 4,
    backgroundColor: Colors.black2,
  },
  title: {
    ...textStyles.titleS,
    marginStart: 8,
    color: textColors.textBlack11,
  },
  note: {
    ...textStyles.bodyM,
    color: textColors.textBlack9,
    marginEnd: 4,
  },
})

export { SelectItemView }
