import React from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { convertDateFromTimeStamp, DateTimeFormat } from "src/utils/timeExt"

interface TimeViewProps {
  style?: ViewStyle
  title?: string
  time: number
}

const TimeView: React.FC<TimeViewProps> = ({ style, time, title }) => (
  <View style={[style, styles.row]}>
    {title && <Text style={styles.title}>{title}</Text>}
    <Text style={styles.postedTime}>
      {convertDateFromTimeStamp(time, DateTimeFormat.LONG)}
    </Text>
  </View>
)

const styles = StyleSheet.create({
  row: {
    alignItems: "center",
    flexDirection: "row",
  },
  title: {
    ...textStyles.labelS,
    color: Colors.black7,
    marginEnd: 4,
  },
  postedTime: {
    ...textStyles.labelS,
    color: Colors.black7,
  },
})

export { TimeView }
