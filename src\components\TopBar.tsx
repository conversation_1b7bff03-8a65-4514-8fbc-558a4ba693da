import React from "react"
import { Image, StyleSheet, Text, View, ViewStyle } from "react-native"
import { CustomPressable } from "./common/CustomPressable"
import { textStyles, viewStyles } from "src/config/styles"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import backIcon from "assets/images/ic_back.png"

interface TopBarProps {
  rightIcon?: React.ReactNode
  enableBack?: boolean
  style?: ViewStyle
  title: string
  rightAction?: () => void
}

const TopBar: React.FC<TopBarProps> = ({
  enableBack,
  title,
  style,
  rightIcon,
  rightAction,
}) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const onGoBack = () => {
    navigation.goBack()
  }

  return (
    <View style={style ? style : styles.container}>
      {enableBack && (
        <CustomPressable style={styles.leftContainer} onPress={onGoBack}>
          <Image source={backIcon} style={viewStyles.icon} />
        </CustomPressable>
      )}
      <Text style={styles.title} numberOfLines={1}>
        {title}
      </Text>
      {rightIcon && (
        <CustomPressable
          style={styles.rightContainer}
          onPress={() => {
            rightAction?.()
          }}
        >
          {rightIcon}
        </CustomPressable>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    marginBottom: 12,
  },
  leftContainer: {
    flex: 1,
    alignItems: "center",
  },
  rightContainer: {
    flex: 1,
    alignItems: "flex-end",
  },
  title: {
    flex: 8,
    ...textStyles.titleL,
    textAlign: "left",
  },
})

export { TopBar }
