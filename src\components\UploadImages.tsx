import React, { useCallback, useEffect, useState } from "react"
import {
  getImageType,
  getPhotoFileName,
  useChoosePhoto,
} from "utils/choosePhotoExt"
import { Image, ScrollView, StyleSheet, View } from "react-native"
import { CustomPressable } from "components/common/CustomPressable"
import Colors from "src/config/colors"
import uploadIcon from "assets/images/ic_upload.png"
import trashIcon from "assets/images/ic_trash.png"
import { viewStyles } from "src/config/styles"

interface ImageData {
  url: string
  fileName: string
  type: string
}

interface UploadImagesProps {
  images: ImageData[]
  isHideAddImageIcon?: boolean
  onChangeImages: (images: ImageData[]) => void
}

interface ImageItemProps {
  image: ImageData
  ratio: number
  onRemove: () => void
}

const ImageItem: React.FC<ImageItemProps> = ({ image, ratio, onRemove }) => (
  <View key={image.url}>
    <Image
      source={{ uri: image.url }}
      style={[styles.image, { aspectRatio: ratio }]}
      resizeMode="contain"
    />
    <CustomPressable onPress={onRemove} style={styles.removeButton}>
      <Image source={trashIcon} style={styles.trash} />
    </CustomPressable>
  </View>
)

const AddImageButton: React.FC<{ onPress: () => void }> = ({ onPress }) => (
  <CustomPressable onPress={onPress} style={styles.addButton}>
    <View style={styles.uploadView}>
      <Image source={uploadIcon} style={viewStyles.icon} />
    </View>
  </CustomPressable>
)

const UploadImages: React.FC<UploadImagesProps> = ({
  images,
  onChangeImages,
  isHideAddImageIcon,
}) => {
  const [imageRatios, setImageRatios] = useState<Record<string, number>>({})
  const { handleChoosePhoto } = useChoosePhoto()

  useEffect(() => {
    images.forEach((image) => {
      Image.getSize(image.url, (width, height) => {
        setImageRatios((prevRatios) => ({
          ...prevRatios,
          [image.url]: width / height,
        }))
      })
    })
  }, [images])

  const pickImage = useCallback(() => {
    handleChoosePhoto(
      (results) => {
        const newImages = results.map((result) => {
          const fileName = getPhotoFileName(result.fileName || null, result.uri)
          const type = getImageType(fileName)
          return {
            url: result.uri,
            fileName,
            type,
          }
        })
        onChangeImages([...images, ...newImages])
      },
      {
        mediaTypes: ["images"],
        allowsEditing: false,
        quality: 1,
        allowsMultipleSelection: true,
      }
    )
  }, [
    images,
    handleChoosePhoto,
    onChangeImages,
    getImageType,
    getPhotoFileName,
  ])

  const removeImage = useCallback(
    (imageToRemove: ImageData) => {
      onChangeImages(images.filter((img) => img !== imageToRemove))
    },
    [images, onChangeImages]
  )

  return (
    <View style={styles.container}>
      {!isHideAddImageIcon && <AddImageButton onPress={pickImage} />}
      <ScrollView horizontal>
        <View style={styles.imagesContainer}>
          {images.map((image) => (
            <ImageItem
              key={image.url}
              image={image}
              ratio={imageRatios[image.url] ?? 1}
              onRemove={() => removeImage(image)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  addButton: {
    padding: 30,
  },
  imagesContainer: {
    flexDirection: "row",
  },
  image: {
    height: 200,
    margin: 4,
    borderRadius: 8,
  },
  removeButton: {
    position: "absolute",
    top: 0,
    right: 0,
    padding: 4,
  },
  uploadView: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 8,
  },
  trash: {
    width: 24,
    height: 24,
    marginTop: 8,
    marginEnd: 8,
  },
})

export { UploadImages }
