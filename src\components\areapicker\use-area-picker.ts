import { useCallback, useState } from "react"

export const useAreaPicker = () => {
  const [location, setLocation] = useState({
    addressCodeLevel1: "",
    addressCodeLevel2: "",
    addressCodeLevel3: "",
  })

  const resetLocation = () => {
    setLocation({
      addressCodeLevel1: "",
      addressCodeLevel2: "",
      addressCodeLevel3: "",
    })
  }
  const onChangeLocation = useCallback((level: string, value: string) => {
    if (level === "addressCodeLevel1") {
      setLocation((prev) => ({
        ...prev,
        addressCodeLevel1: value,
        addressCodeLevel2: "",
        addressCodeLevel3: "",
      }))
    } else if (level === "addressCodeLevel2") {
      setLocation((prev) => ({
        ...prev,
        addressCodeLevel2: value,
        addressCodeLevel3: "",
      }))
    } else {
      setLocation((prev) => ({
        ...prev,
        addressCodeLevel3: value,
      }))
    }
  }, [])

  return { location, onChangeLocation, resetLocation }
}
