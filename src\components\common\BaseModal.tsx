import React, { useCallback } from "react"
import { Modal, StyleSheet, Text, View } from "react-native"
import { MaterialIcons } from "@expo/vector-icons"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"

interface BaseModalProps {
  visible: boolean
  isShowCloseIcon?: boolean
  isDisableClose?: boolean
  title?: string
  children: React.ReactNode
  onClose: () => void
}

export const BaseModal: React.FC<BaseModalProps> = ({
  isShowCloseIcon = false,
  title,
  visible,
  isDisableClose = false,
  children,
  onClose,
}) => {
  const handleClose = useCallback(() => {
    if (!isDisableClose) {
      onClose()
    }
  }, [isDisableClose, onClose])

  return (
    <Modal visible={visible} onRequestClose={handleClose} transparent>
      <View style={styles.overlay}>
        <View style={styles.container}>
          {title && (
            <View style={styles.header}>
              <Text style={styles.title}>{title}</Text>
            </View>
          )}

          {children}

          {isShowCloseIcon && (
            <MaterialIcons
              style={styles.closeIcon}
              name="close"
              size={20}
              color={Colors.black7}
              onPress={handleClose}
            />
          )}
        </View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    padding: 8,
    backgroundColor: Colors.opacityBlack60,
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    width: "95%",
    position: "relative",
    backgroundColor: Colors.white,
    borderRadius: 8,
    padding: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingBottom: 8,
  },
  title: {
    ...textStyles.titleL,
    padding: 0,
    flex: 1,
    textAlign: "center",
  },
  closeIcon: {
    position: "absolute",
    top: 8,
    right: 12,
  },
})
