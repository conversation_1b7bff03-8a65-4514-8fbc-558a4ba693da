import React from "react"
import {
  ActivityIndicator,
  DimensionValue,
  Image,
  ImageSourcePropType,
  Pressable,
  StyleSheet,
  Text,
  TextStyle,
  View,
  ViewStyle,
} from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { MaterialIcons } from "@expo/vector-icons"
import { CustomPressable } from "./CustomPressable"
import Colors, { textColors } from "src/config/colors"
import { convertHexToRGBA } from "utils"
import { textStyles } from "src/config/styles"

interface GradientButtonProps {
  title: string
  onPress: () => void
  color?: string
  colors?: string[]
  contentColor?: string
  style?: ViewStyle
  width?: DimensionValue
  height?: DimensionValue
  borderRadius?: number
  iconName?: keyof typeof MaterialIcons.glyphMap
  iconSize?: number
  iconColor?: string
  iconSource?: ImageSourcePropType
  iconWidth?: number
  iconHeight?: number
  enabled?: boolean
  isLoading?: boolean
}

export const GradientButton: React.FC<GradientButtonProps> = ({
  title,
  onPress,
  color,
  colors,
  contentColor = Colors.white,
  style,
  width = "100%",
  height = 50,
  borderRadius = 20,
  iconName,
  iconSize = 24,
  iconColor = Colors.white,
  iconSource,
  iconWidth = 24,
  iconHeight = 24,
  enabled = true,
  isLoading = false,
}) => {
  const gradientColors = !enabled
    ? [Colors.gray, Colors.gray]
    : color
      ? [color, color]
      : colors
        ? colors
        : [Colors.brandOrange, Colors.brandRed, Colors.brandPurple]

  return (
    <Pressable
      onPress={enabled && !isLoading ? onPress : undefined}
      disabled={!enabled || isLoading}
      style={[styles.button, style, { width, height }]}
    >
      <LinearGradient
        colors={gradientColors as [string, string, ...string[]]}
        style={[styles.gradient, { borderRadius }]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.content}>
          {isLoading ? (
            <ActivityIndicator color={contentColor} style={styles.icon} />
          ) : (
            <>
              {iconName && (
                <MaterialIcons
                  name={iconName}
                  size={iconSize}
                  color={iconColor}
                  style={styles.icon}
                />
              )}
              {iconSource && (
                <Image
                  source={iconSource}
                  tintColor={contentColor}
                  style={[
                    styles.icon,
                    { width: iconWidth, height: iconHeight },
                  ]}
                />
              )}
            </>
          )}
          <Text style={[styles.text, { color: contentColor }]}>{title}</Text>
        </View>
      </LinearGradient>
    </Pressable>
  )
}

interface PrimaryButtonProps {
  title: string
  onPress?: () => void
  style?: ViewStyle
  textStyle?: TextStyle
  width?: DimensionValue
  height?: DimensionValue
  borderRadius?: number
  icon?: React.ReactNode
  enabled?: boolean
  color?: string
  contentColor?: string
  isLoading?: boolean
}

export const PrimaryButton: React.FC<PrimaryButtonProps> = ({
  title,
  onPress,
  style,
  textStyle = textStyles.labelL,
  width = "auto",
  height = 40,
  borderRadius = 8,
  icon,
  enabled = true,
  color = Colors.primary,
  contentColor = Colors.black12,
  isLoading = false,
}) => {
  const isEnable = enabled && !isLoading
  return (
    <CustomPressable onPress={onPress} style={style} enabled={isEnable}>
      <View
        style={[
          style,
          {
            width,
            height,
            borderRadius,
            flexDirection: "row",
            backgroundColor: convertHexToRGBA(color, isEnable ? 1 : 0.5),
            justifyContent: "center",
            alignItems: "center",
            paddingHorizontal: 16,
          },
        ]}
      >
        {isLoading && (
          <ActivityIndicator
            size="small"
            color={convertHexToRGBA(contentColor, 0.5)}
          />
        )}
        {!isLoading && icon}
        {(isLoading || icon) && <View style={{ width: 8 }} />}
        <Text
          style={[
            textStyle,
            {
              color: convertHexToRGBA(contentColor, isEnable ? 1 : 0.5),
            },
          ]}
        >
          {title}
        </Text>
      </View>
    </CustomPressable>
  )
}

export const SecondaryButton: React.FC<PrimaryButtonProps> = ({
  title,
  onPress,
  style,
  width,
  height = 40,
  borderRadius = 8,
  icon,
  enabled = true,
  isLoading = false,
}) => {
  const isEnable = enabled && !isLoading
  const contentColor = convertHexToRGBA(
    textColors.textGray900,
    isEnable ? 1 : 0.5
  )
  return (
    <CustomPressable onPress={onPress} style={style} enabled={isEnable}>
      <View
        style={[
          style,
          {
            width,
            height,
            borderRadius,
            flexDirection: "row",
            backgroundColor: enabled ? Colors.white : Colors.gray,
            justifyContent: "center",
            alignItems: "center",
            paddingHorizontal: 16,
            borderWidth: 0.5,
            borderColor: Colors.border,
          },
        ]}
      >
        {isLoading && <ActivityIndicator size="small" color={contentColor} />}
        {!isLoading && icon}
        {(isLoading || icon) && <View style={{ width: 8 }} />}
        <Text style={[textStyles.labelL, { color: contentColor }]}>
          {title}
        </Text>
      </View>
    </CustomPressable>
  )
}

export const IconButton: React.FC<{
  icon: React.ReactNode
  onPress?: () => void
  enabled?: boolean
  style?: ViewStyle
}> = ({ icon, onPress, enabled = true, style }) => {
  return (
    <CustomPressable onPress={onPress} enabled={enabled} style={style}>
      {icon}
    </CustomPressable>
  )
}

const styles = StyleSheet.create({
  button: {
    overflow: "hidden",
  },
  gradient: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
  },
  icon: {
    marginRight: 8,
  },
  text: {
    ...textStyles.titleM,
    color: Colors.white,
    textAlign: "center",
  },
})
