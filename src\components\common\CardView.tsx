import React from "react"
import { StyleProp, StyleSheet, View, ViewStyle } from "react-native"
import Colors from "src/config/colors"

interface CardViewProps {
  children: React.ReactNode
  style?: StyleProp<ViewStyle>
  borderRadius?: number
  shadowColor?: string
  shadowOffset?: {
    width: number
    height: number
  }
  shadowOpacity?: number
  shadowRadius?: number
  elevation?: number
}

const defaultShadowProps = {
  borderRadius: 10,
  shadowColor: Colors.black12,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 5,
}

export const CardView: React.FC<CardViewProps> = ({
  children,
  style,
  ...shadowProps
}) => {
  const finalShadowProps = {
    ...defaultShadowProps,
    ...shadowProps,
  }

  return <View style={[styles.card, style, finalShadowProps]}>{children}</View>
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.Neutral950,
  },
})
