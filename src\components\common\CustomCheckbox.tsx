import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { textStyles } from "src/config/styles"
import Checkbox from "expo-checkbox"
import Colors, { textColors } from "src/config/colors"

interface CustomCheckboxProps {
  label: string
  disabled?: boolean
  isChecked: boolean
  onToggle: (value: boolean) => void
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  label,
  disabled = false,
  isChecked,
  onToggle,
}) => {
  return (
    <View style={styles.container}>
      <Checkbox
        style={styles.checkbox}
        color={disabled ? Colors.black7 : Colors.blueLink}
        value={isChecked}
        onValueChange={() => !disabled && onToggle(!isChecked)}
      />
      <Text style={styles.label}>{label}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  label: {
    ...textStyles.bodyM,
    marginStart: 8,
    color: textColors.textBlack,
  },
  checkbox: {
    margin: 4,
    width: 20,
    height: 20,
    borderRadius: 4,
  },
})

export { CustomCheckbox }
