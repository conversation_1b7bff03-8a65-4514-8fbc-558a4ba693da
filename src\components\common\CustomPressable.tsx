import React, { ReactNode } from "react"
import { Pressable, StyleProp, View, ViewStyle, Insets } from "react-native"

interface CustomPressableProps {
  onPress?: () => void
  children: ReactNode
  enabled?: boolean
  style?: StyleProp<ViewStyle>
  feedbackOpacity?: number
  hitSlop?: number | Insets
  androidRipple?: boolean
  androidRippleColor?: string
  androidRippleBorderless?: boolean
}

export const CustomPressable: React.FC<CustomPressableProps> = ({
  onPress,
  children,
  enabled = true,
  style,
  feedbackOpacity = 0.7,
  hitSlop,
  androidRipple = false,
  androidRippleColor = "#00000020",
  androidRippleBorderless = false,
}) => {
  const renderContent = () => {
    if (!enabled) {
      return <View style={style}>{children}</View>
    }

    return (
      <Pressable
        onPress={onPress}
        hitSlop={hitSlop}
        android_ripple={
          androidRipple
            ? {
                color: androidRippleColor,
                borderless: androidRippleBorderless,
              }
            : null
        }
        style={({ pressed }) => [
          {
            opacity: pressed ? feedbackOpacity : 1,
          },
          style,
        ]}
      >
        {children}
      </Pressable>
    )
  }

  return renderContent()
}
