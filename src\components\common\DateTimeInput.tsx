import React, { useCallback, useState } from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import RNDateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker"
import { CustomPressable } from "components/common/CustomPressable"
import { LabelView } from "./LabelView"
import calendarIcon from "../../../assets/images/ic_calendar.png"

interface DateTimeInputProps {
  title: string
  value: string
  require?: boolean
  disabled?: boolean
  onChangeDate: (selectedDate?: Date) => void
  placeholder?: string
  minimumDate?: Date
}

const DateTimeInput: React.FC<DateTimeInputProps> = ({
  title,
  value,
  require,
  onChangeDate,
  placeholder = "dd/mm/yyyy",
  disabled = false,
  minimumDate,
}) => {
  const [showDatePicker, setShowDatePicker] = useState(false)

  const handlePress = useCallback(() => {
    setShowDatePicker(true)
  }, [])

  const handleDateChange = useCallback(
    (event: DateTimePickerEvent, selectedDate?: Date) => {
      const currentDate = selectedDate || new Date()
      onChangeDate(currentDate)
      setShowDatePicker(false)
    },
    [onChangeDate]
  )

  return (
    <View style={styles.container}>
      <LabelView label={title} require={require} style={{ marginBottom: 4 }} />
      <CustomPressable onPress={handlePress} enabled={!disabled}>
        <View style={styles.input}>
          <Text style={[textStyles.bodyM, styles.valueText]}>
            {value || placeholder}
          </Text>
          <Image source={calendarIcon} style={viewStyles.icon} />
        </View>
      </CustomPressable>

      {showDatePicker && (
        <RNDateTimePicker
          value={value ? new Date(value) : new Date()}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={minimumDate}
        />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 12,
    width: "100%",
  },
  label: {
    marginTop: 20,
    marginBottom: 8,
  },
  cardContainer: {
    borderRadius: 8,
    shadowOpacity: 0.1,
    elevation: 2,
  },
  input: {
    width: "100%",
    flexDirection: "row",
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: Colors.black5,
    justifyContent: "space-between",
    alignItems: "center",
  },
  valueText: {
    color: Colors.gray600,
  },
})

export { DateTimeInput }
