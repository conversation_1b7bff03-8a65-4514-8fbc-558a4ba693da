import Colors from "src/config/colors"
import React from "react"
import { Image, StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import defaultEmptyImage from "../../../assets/images/ic_empty.png"

interface EmptyViewProps {
  style?: ViewStyle
  title?: string
  subtitle?: string
  imageSource?: any
  imageSize?: number
}

const EmptyView: React.FC<EmptyViewProps> = ({
  style,
  title,
  subtitle,
  imageSource = defaultEmptyImage,
  imageSize = 100,
}) => {
  const { t } = useTranslation()

  return (
    <View style={[styles.container, style]}>
      <Text style={[textStyles.titleL, styles.title]}>
        {title ? title : t("Oops!")}{" "}
        <Text style={[textStyles.titleL, { color: Colors.PalleteWhite }]}>
          {subtitle ? subtitle : t("It's Empty")}
        </Text>
      </Text>
      <Image
        source={imageSource}
        style={[styles.image, { width: imageSize, height: imageSize }]}
      />
    </View>
  )
}

export { EmptyView }

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  image: {
    marginTop: 16,
  },
  title: {
    color: Colors.primary,
  },
})
