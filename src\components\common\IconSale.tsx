import React from "react"
import { Image, ImageStyle, StyleSheet } from "react-native"
import Colors from "src/config/colors"
import moneyIcon from "../../../assets/images/ic_money.png"

interface IconSaleProps {
  color?: string
  size: number
}

const IconSale: React.FC<IconSaleProps> = ({
  color = Colors.black12,
  size,
}) => {
  const imageStyle: ImageStyle = {
    width: size,
    height: size,
  }

  return (
    <Image
      source={moneyIcon}
      style={[styles.image, imageStyle]}
      tintColor={color}
    />
  )
}

const styles = StyleSheet.create({
  image: {
    resizeMode: "contain",
  },
})

export { IconSale }
