import React from "react"
import { StyleSheet, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import { TextInput } from "react-native-gesture-handler"
import Colors, { textColors } from "src/config/colors"
import { InputModeOptions } from "react-native/Libraries/Components/TextInput/TextInput"
import { ErrorLabel } from "./Label"
import { LabelView } from "./LabelView"

interface InputFieldProps {
  label?: string
  value: string | number
  onChangeText: (text: string | number) => void
  placeholder?: string
  multiline?: boolean
  height?: number
  inputMode?: InputModeOptions
  require?: boolean
  style?: ViewStyle
  onBlur?: () => void
  error?: string
  type?: "string" | "number"
  disabled?: boolean
}

const InputField: React.FC<InputFieldProps> = ({
  label,
  value,
  onChangeText,
  require,
  placeholder,
  multiline = false,
  height = 40,
  inputMode = "text",
  style,
  onBlur,
  error,
  type = "string",
  disabled = false,
}) => {
  const handleChangeText = (text: string) => {
    const trimmedText = text.trimStart()
    if (type === "number" && !Number.isNaN(Number(trimmedText))) {
      onChangeText(Number(trimmedText))
    } else if (type === "string") {
      onChangeText(trimmedText)
    }
  }

  return (
    <View style={style}>
      {label && (
        <LabelView label={label} require={require} style={styles.label} />
      )}
      <TextInput
        onBlur={onBlur}
        value={value.toString()}
        onChangeText={handleChangeText}
        placeholder={placeholder}
        multiline={multiline}
        inputMode={inputMode}
        style={[styles.input, { height }, disabled && styles.disabledInput]}
        editable={!disabled}
      />
      <ErrorLabel error={error} />
    </View>
  )
}

const styles = StyleSheet.create({
  label: {
    marginBottom: 8,
  },
  input: {
    height: 40,
    width: "100%",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.black5,
    paddingHorizontal: 12,
    ...textStyles.bodyM,
    color: textColors.textBlack9,
  },
  disabledInput: {
    backgroundColor: Colors.black4,
    color: textColors.textBlack10,
  },
})

export { InputField }
