import React from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"

interface LabelViewProps {
  label: string
  require?: boolean
  style?: ViewStyle
}

export const LabelView: React.FC<LabelViewProps> = ({
  label,
  require,
  style,
}) => {
  return (
    <View style={[style, styles.container]}>
      <Text style={textStyles.labelL}>{label}</Text>
      {require && <Text style={[textStyles.labelL, styles.require]}>(*)</Text>}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
  },
  label: {
    ...textStyles.labelL,
    color: textColors.textBlack9,
  },
  require: {
    color: Colors.redLight,
    marginStart: 4,
  },
})
