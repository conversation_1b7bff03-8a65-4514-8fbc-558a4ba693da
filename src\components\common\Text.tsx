import React from "react"
import { Text, TextStyle } from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import MaskedView from "@react-native-masked-view/masked-view"
import Colors from "src/config/colors"

interface GradientTextProps {
  text: string
  style?: TextStyle
  colors?: readonly [string, string, ...string[]]
}

export const GradientText: React.FC<GradientTextProps> = ({
  text,
  style,
  colors = [Colors.brandOrange, Colors.brandRed, Colors.brandPurple] as const,
}) => {
  return (
    <MaskedView maskElement={<Text style={style}>{text}</Text>}>
      <LinearGradient
        colors={colors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <Text style={[style, { opacity: 0 }]}>{text}</Text>
      </LinearGradient>
    </MaskedView>
  )
}
