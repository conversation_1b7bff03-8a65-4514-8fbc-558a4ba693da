import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { EstateTokenAreaUnit } from "src/api"
import { formatCurrency } from "../../utils"
import { textStyles } from "../../config/styles"
import { textColors } from "../../config/colors"
import { useTranslation } from "react-i18next"

interface AreaUnitViewProps {
  area: number
  areaUnit: EstateTokenAreaUnit
}

const AreaUnitView: React.FC<AreaUnitViewProps> = ({ area, areaUnit }) => {
  const { t } = useTranslation()

  return (
    <View style={styles.row}>
      {areaUnit === EstateTokenAreaUnit.SQM ? (
        <>
          <Text style={styles.value}>{`${formatCurrency(area)} m`}</Text>
          <Text style={styles.areaUnit}>2</Text>
        </>
      ) : (
        <Text>{`${formatCurrency(area)} ${t("sqft")}`}</Text>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  row: {
    flexDirection: "row",
  },
  value: {
    ...textStyles.bodyM,
    color: textColors.textBlack11,
  },
  areaUnit: {
    marginBottom: 4,
    fontSize: 8,
  },
})

export { AreaUnitView }
