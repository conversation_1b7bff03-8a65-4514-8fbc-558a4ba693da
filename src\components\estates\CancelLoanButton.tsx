import React, { useState } from "react"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import { StyleSheet } from "react-native"
import { useEthersProvider } from "hooks"
import { CONTRACT_ADDRESS_MORTGAGE_TOKEN } from "src/config/env"
import { PrimaryButton } from "../common/Button"
import { mortgageTokenAbi } from "src/api/contracts/mortgage-token"
import { MortgageTokenLoan } from "src/api"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import { SimpleLoadingView } from "../SimpleLoadingView"
import Logger from "src/utils/logger"

interface CancelLoanButtonProps {
  loan: MortgageTokenLoan
  onRefresh?: () => void
}

const logger = new Logger({ tag: "TokenizationLoansView" })

const CancelLoanButton: React.FC<CancelLoanButtonProps> = ({
  loan,
  onRefresh,
}) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [canCancelLoading, setCanCancelLoading] = useState<boolean>(true)

  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const handleCancel = async () => {
    if (isLoading || !ethersProvider) return
    setIsLoading(true)
    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
        abi: mortgageTokenAbi,
        functionName: "cancel",
        args: [BigInt(loan.id)],
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        setIsLoading(false)
        showSuccessWhenCallContract(
          t("Cancel success") + ". " + t("Data will be updated in few seconds")
        )
        onRefresh?.()
      } else {
        throw new Error(t("Cancel failed"))
      }
    } catch (e) {
      setIsLoading(false)
      showError(t("Cancel failed"))
      logger.error("Cancel failed", e)
    }
  }

  if (!address) {
    return null
  }

  return (
    <>
      <PrimaryButton
        enabled={Boolean(address)}
        title={t("Cancel")}
        onPress={handleCancel}
        textStyle={styles.buttonText}
        color={Colors.surfaceNormal}
        borderRadius={8}
        style={{ marginTop: 4 }}
      />
      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </>
  )
}

const styles = StyleSheet.create({
  buttonText: {
    ...textStyles.labelL,
    textAlign: "center",
  },
})

export { CancelLoanButton }
