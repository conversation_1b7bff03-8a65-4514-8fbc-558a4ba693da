import React, { useState } from "react"
import { Alert, StyleSheet } from "react-native"
import { SimpleLoadingView } from "components"
import { PrimaryButton } from "../common/Button"
import { MarketplaceOffer } from "src/api"
import { useTranslation } from "react-i18next"
import { CONTRACT_ADDRESS_MARKETPLACE } from "src/config/env"
import { marketplaceAbi } from "src/api/contracts/marketplace"
import { useEthersProvider } from "hooks"
import { useAccount, useWriteContract } from "wagmi"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import Logger from "src/utils/logger"

interface CancelOfferButtonProps {
  offer: Omit<MarketplaceOffer, "seller">
  onRefresh?: () => void
}

const logger = new Logger({ tag: "CancelOfferButton" })

export const CancelOfferButton: React.FC<CancelOfferButtonProps> = ({
  offer,
  onRefresh,
}) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [canCancelLoading, setCanCancelLoading] = useState<boolean>(true)

  const ethersProvider = useEthersProvider()
  const { address } = useAccount()
  const { writeContractAsync } = useWriteContract()

  const openCancelOfferAlert = () =>
    Alert.alert(
      t("Cancel Offer"),
      t(
        "Are you sure you want to cancel this offer? This action cannot be undone."
      ),
      [
        {
          text: t("No"),
          onPress: () => {},
          style: "cancel",
        },
        { text: t("Yes"), onPress: handleCancelOffer },
      ],
      { cancelable: false }
    )

  const handleCancelOffer = async () => {
    if (isLoading || !ethersProvider) return
    setIsLoading(true)
    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MARKETPLACE,
        abi: marketplaceAbi,
        functionName: "cancelOffer",
        args: [offer.id],
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status !== 1) {
        throw new Error(t("Fail to cancel offer"))
      } else {
        showSuccessWhenCallContract(
          t("Offer has been cancelled") +
            ". " +
            t("Data will be updated in few seconds")
        )
        onRefresh?.()
      }
    } catch (error) {
      showError(t("Fail to cancel offer"))
      logger.error("Failed to cancel offer", error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!address) {
    return null
  }

  return (
    <>
      <PrimaryButton
        title={t("Cancel")}
        onPress={openCancelOfferAlert}
        width={"100%"}
        style={styles.button}
        color={Colors.surfaceNormal}
        enabled={Boolean(address)}
      />
      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </>
  )
}

const styles = StyleSheet.create({
  button: {
    ...textStyles.labelL,
    textAlign: "center",
    marginTop: 8,
  },
  icon: {
    width: 16,
    height: 16,
  },
})
