import { queryOptions, useSuspenseQuery } from "@tanstack/react-query"
import React from "react"
import { Image, StyleSheet, Text, TextStyle } from "react-native"
import { getCurrencies } from "src/api"
import usdtIcon from "../../../assets/images/ic_usdt.png"
import brikiIcon from "../../../assets/images/ic_briki.png"
import brikIcon from "../../../assets/images/ic_brik.png"
import { QueryKeys } from "src/config/queryKeys"
import { textStyles } from "src/config/styles"

const getCurrenciesQueryOptions = queryOptions({
  queryKey: [QueryKeys.CURRENCY.LIST],
  queryFn: getCurrencies,
})

type Props = {
  currency: string
  style?: TextStyle
  shouldShowIcon?: boolean
}

export const CurrencySymbol: React.FC<Props> = ({
  currency = "",
  style = styles.amount,
  shouldShowIcon = false,
}) => {
  const { data: currencies = [] } = useSuspenseQuery(getCurrenciesQueryOptions)
  const data = currencies.find((c) => c.currency === currency)
  if (data?.symbol === "USDT" && shouldShowIcon) {
    return <Image source={usdtIcon} style={styles.image} resizeMode="contain" />
  }
  if (data?.symbol === "USDT" && shouldShowIcon) {
    return <Image source={usdtIcon} style={styles.image} resizeMode="contain" />
  }
  if (data?.symbol === "BRIKI" && shouldShowIcon) {
    return (
      <Image source={brikiIcon} style={styles.image} resizeMode="contain" />
    )
  }
  if (data?.symbol === "BRIK" && shouldShowIcon) {
    return <Image source={brikIcon} style={styles.image} resizeMode="contain" />
  }
  return <Text style={style}>{data?.symbol}</Text>
}

const styles = StyleSheet.create({
  image: {
    width: 16,
    height: 16,
    marginStart: 4,
  },
  amount: {
    ...textStyles.bodyM,
    marginStart: 4,
  },
})
