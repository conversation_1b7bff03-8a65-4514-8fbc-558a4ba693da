import React from "react"
import { StyleSheet, Text, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { useTranslation } from "react-i18next"

interface EstateStateConfig {
  color: string
  label: string
  textColor: string
}

interface EstateStateProps {
  style?: ViewStyle
  state: string
  color?: string
}

const getEstateStateConfig = (
  state: string,
  t: (key: string) => string
): EstateStateConfig => {
  const configs: Record<string, EstateStateConfig> = {
    VALIDATING: {
      color: Colors.primary,
      label: t("Validating"),
      textColor: textColors.textBlack11,
    },
    SELLING: {
      color: Colors.surface,
      label: t("Selling"),
      textColor: textColors.textBlack11,
    },
    TRANSFERRING_OWNERSHIP: {
      color: Colors.pinkLight,
      label: t("Transferring ownership"),
      textColor: textColors.textBlack11,
    },
    TRANSFERRING_OWNERSHIP_FAILED: {
      color: Colors.redLight,
      label: t("Failed to transfer ownership"),
      textColor: textColors.textWhite,
    },
    INSUFFICIENT_SOLD_AMOUNT: {
      color: Colors.pinkLight,
      label: t("Insufficient sold amount"),
      textColor: textColors.textBlack11,
    },
    TOKENIZED: {
      color: Colors.greenLight,
      label: t("Tokenized"),
      textColor: textColors.textBlack11,
    },
    CONFIRMED: {
      color: Colors.greenLight,
      label: t("Tokenized"),
      textColor: textColors.textBlack11,
    },
    CANCELLED: {
      color: Colors.redLight,
      label: t("Cancelled"),
      textColor: textColors.textBlack11,
    },
    EXPIRED: {
      color: Colors.redLight,
      label: t("Expired"),
      textColor: textColors.textWhite,
    },
    PENDING: {
      color: Colors.goldLight,
      label: t("Pending"),
      textColor: textColors.textBlack11,
    },
    SOLD: {
      color: Colors.greenLight,
      label: t("Sold"),
      textColor: textColors.textBlack11,
    },
    REPAID: {
      color: Colors.greenLight,
      label: t("Repaid"),
      textColor: textColors.textBlack11,
    },
    SUPPLIED: {
      color: Colors.greenLight,
      label: t("Supplied"),
      textColor: textColors.textBlack11,
    },
  }

  return (
    configs[state] || {
      color: Colors.green,
      label: t("Unknown state"),
      textColor: textColors.textBlack11,
    }
  )
}

const EstateState: React.FC<EstateStateProps> = ({ state, color, style }) => {
  const { t } = useTranslation()
  const config = getEstateStateConfig(state, t)

  return (
    <Text
      style={[
        style,
        styles.state,
        { backgroundColor: color || config.color, color: config.textColor },
      ]}
    >
      {config.label}
    </Text>
  )
}

const styles = StyleSheet.create({
  state: {
    ...textStyles.labelM,
    alignSelf: "flex-start",
    borderRadius: 4,
    padding: 4,
  },
})

export { EstateState }
