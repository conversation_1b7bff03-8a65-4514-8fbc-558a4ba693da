import React, { useState } from "react"
import { MortgageTokenLoan } from "src/api"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import { useEthersProvider } from "hooks"
import { CONTRACT_ADDRESS_MORTGAGE_TOKEN } from "src/config/env"
import { PrimaryButton } from "../common/Button"
import { mortgageTokenAbi } from "src/api/contracts/mortgage-token"
import Colors from "src/config/colors"
import { StyleSheet } from "react-native"
import { textStyles } from "src/config/styles"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import { SimpleLoadingView } from "../SimpleLoadingView"
import Logger from "src/utils/logger"

interface ForeCloseLoanButtonProps {
  loan: MortgageTokenLoan
  onRefresh?: () => void
}

const logger = new Logger({ tag: "ForeCloseLoanButton" })

const ForeCloseLoanButton: React.FC<ForeCloseLoanButtonProps> = ({
  loan,
  onRefresh,
}) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [canCancelLoading, setCanCancelLoading] = useState<boolean>(true)
  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const handleForeclose = async () => {
    if (isLoading || !ethersProvider) return
    setIsLoading(true)
    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
        abi: mortgageTokenAbi,
        functionName: "foreclose",
        args: [BigInt(loan.id)],
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Foreclose success") +
            ". " +
            t("Data will be updated in few seconds")
        )
        onRefresh?.()
      } else {
        showError(t("Foreclose failed"))
      }
    } catch (e: any) {
      showError(t("Foreclose failed"))
      logger.error("Foreclose failed", e)
    } finally {
      setIsLoading(false)
    }
  }

  if (!address) {
    return null
  }

  return (
    <>
      <PrimaryButton
        title={t("Foreclose")}
        onPress={handleForeclose}
        textStyle={styles.buttonText}
        color={Colors.surfaceNormal}
        style={{ marginTop: 4 }}
      />
      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </>
  )
}

const styles = StyleSheet.create({
  buttonText: {
    ...textStyles.labelL,
    textAlign: "center",
  },
})

export { ForeCloseLoanButton }
