import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { AreaUnitView } from "./AreaUnitView"
import { formatNumber, formatNumericByDecimals, shortenNumber } from "utils"
import { textStyles, viewStyles } from "src/config/styles"
import usdtIcon from "../../../assets/images/ic_usdt.png"
import areaIcon from "../../../assets/images/ic_area.png"
import squareIcon from "../../../assets/images/ic_square.png"
import moneyIcon from "../../../assets/images/ic_money.png"
import { useTranslation } from "react-i18next"
import { textColors } from "src/config/colors"
import { EstateTokenAreaUnit } from "src/api"

interface LandInfoProps {
  unitPrice: string
  decimals: number
  totalSupply: string
  area: number
  areaUnit: EstateTokenAreaUnit
}

interface InfoItemViewProps {
  icon: any
  children: React.ReactNode
}

const InfoItemView: React.FC<InfoItemViewProps> = ({ icon, children }) => (
  <View style={styles.row}>
    <Image source={icon} style={viewStyles.icon} />
    <View style={[styles.contentContainer, styles.marginStartDefault]}>
      {children}
    </View>
  </View>
)

export const LandInfo: React.FC<LandInfoProps> = ({
  unitPrice,
  decimals,
  totalSupply,
  area,
  areaUnit,
}) => {
  const { t } = useTranslation()
  const unitPriceConverted = formatNumericByDecimals(unitPrice, decimals)
  const totalSupplyConverted = formatNumericByDecimals(totalSupply, decimals)

  return (
    <View style={styles.landInfo}>
      <InfoItemView icon={squareIcon}>
        <Text style={styles.textValue}>
          {shortenNumber(Number(totalSupplyConverted))} {t("NFT")}
        </Text>
      </InfoItemView>

      <InfoItemView icon={moneyIcon}>
        <Text style={styles.textValue}>
          {formatNumber(Number(unitPriceConverted))}
        </Text>
        <Image
          source={usdtIcon}
          style={[viewStyles.tinyIcon, styles.marginStartDefault]}
        />
        <Text style={styles.textValue}>/1 {t("NFT")}</Text>
      </InfoItemView>

      <InfoItemView icon={areaIcon}>
        <AreaUnitView area={area} areaUnit={areaUnit} />
      </InfoItemView>
    </View>
  )
}

const styles = StyleSheet.create({
  landInfo: {
    justifyContent: "flex-start",
    alignContent: "flex-start",
  },
  row: {
    flexDirection: "row",
    marginTop: 4,
  },
  marginStartDefault: {
    marginStart: 4,
  },
  textValue: {
    ...textStyles.bodyM,
    color: textColors.textBlack,
    marginStart: 4,
  },
  areaUnit: {
    marginBottom: 4,
    fontSize: 8,
  },
  contentContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
})
