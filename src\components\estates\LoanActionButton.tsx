import React from "react"
import { MortgageTokenLoan, MortgageTokenLoanState } from "src/api"
import { useAccount } from "wagmi"
import { CancelLoanButton } from "./CancelLoanButton"
import { ForeCloseLoanButton } from "./ForeCloseLoanButton"
import { LendLoanButton } from "./LendLoanButton"
import { RepayLoanButton } from "./RepayLoanButton"

interface LoanActionButtonProps {
  loan: MortgageTokenLoan
  onRefresh?: () => void
}

export const LoanActionButton: React.FC<LoanActionButtonProps> = ({
  loan,
  onRefresh,
}) => {
  const { address } = useAccount()
  const { state, lender, borrower, dueInSeconds } = loan

  const overdue =
    state !== MortgageTokenLoanState.PENDING &&
    state !== MortgageTokenLoanState.CANCELLED &&
    new Date().getTime() / 1000 > dueInSeconds

  const showLendBtn =
    state === MortgageTokenLoanState.PENDING &&
    address?.toLowerCase() !== borrower.address
  const showCancelBtn =
    state === MortgageTokenLoanState.PENDING &&
    address?.toLowerCase() === borrower.address
  const showRepayBtn =
    state === MortgageTokenLoanState.SUPPLIED &&
    address?.toLowerCase() === borrower.address
  const showForecloseBtn =
    state === MortgageTokenLoanState.SUPPLIED &&
    overdue &&
    address?.toLowerCase() === lender.address

  return (
    <>
      {showForecloseBtn && (
        <ForeCloseLoanButton loan={loan} onRefresh={onRefresh} />
      )}
      {showLendBtn && <LendLoanButton loan={loan} onRefresh={onRefresh} />}
      {showCancelBtn && <CancelLoanButton loan={loan} onRefresh={onRefresh} />}
      {showRepayBtn && <RepayLoanButton loan={loan} onRefresh={onRefresh} />}
    </>
  )
}
