import React from "react"
import { StyleSheet, Text, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { useTranslation } from "react-i18next"

interface LoanStateConfig {
  color: string
  label: string
  textColor: string
}

interface LoanStateProps {
  style?: ViewStyle
  state: string
  overdue: boolean
  color?: string
}

const getLoanStateConfig = (
  state: string,
  overdue: boolean,
  t: (key: string) => string
): LoanStateConfig => {
  const configs: Record<string, LoanStateConfig> = {
    PENDING: {
      color: Colors.goldLight,
      label: t("Mortgage Open"),
      textColor: textColors.textBlack11,
    },
    SUPPLIED: {
      color: overdue ? Colors.redLight : Colors.greenLight,
      label: overdue ? t("Mortgage Overdue") : t("Mortgage Lent"),
      textColor: overdue ? textColors.textWhite : textColors.textBlack11,
    },
    OVERDUE: {
      color: Colors.redLight,
      label: t("Mortgage Overdue"),
      textColor: textColors.textWhite,
    },
    REPAID: {
      color: Colors.greenLight,
      label: t("Mortgage Repaid"),
      textColor: textColors.textBlack11,
    },
    FORECLOSED: {
      color: Colors.redLight,
      label: t("Mortgage Foreclosed"),
      textColor: textColors.textWhite,
    },
    CANCELLED: {
      color: Colors.black5,
      label: t("Mortgage Cancelled"),
      textColor: textColors.textBlack11,
    },
  }

  return (
    configs[state] || {
      color: Colors.primary,
      label: t("Unknown status"),
      textColor: textColors.textBlack11,
    }
  )
}

const LoanState: React.FC<LoanStateProps> = ({
  state,
  overdue,
  color,
  style,
}) => {
  const { t } = useTranslation()
  const config = getLoanStateConfig(state, overdue, t)

  return (
    <Text
      style={[
        style,
        styles.status,
        { backgroundColor: color || config.color, color: config.textColor },
      ]}
    >
      {config.label}
    </Text>
  )
}

const styles = StyleSheet.create({
  status: {
    ...textStyles.labelM,
    alignSelf: "flex-start",
    borderRadius: 4,
    padding: 4,
  },
})

export { LoanState }
