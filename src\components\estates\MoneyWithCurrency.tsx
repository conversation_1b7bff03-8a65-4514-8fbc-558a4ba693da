import React from "react"
import { StyleSheet, Text, TextStyle, View, ViewStyle } from "react-native"
import { formatNumericByDecimalsDisplay } from "utils"
import { textStyles } from "src/config/styles"
import { CurrencySymbol } from "./CurrencySymbol"
import { textColors } from "src/config/colors"

interface MoneyWithCurrencyProps {
  amount: string
  decimals?: number
  currency?: string
  shouldShowIcon?: boolean
  style?: ViewStyle
  amountStyle?: TextStyle
}

export const MoneyWithCurrency: React.FC<MoneyWithCurrencyProps> = ({
  style,
  amountStyle = styles.amount,
  amount,
  decimals = 18,
  currency = "",
  shouldShowIcon = true,
}) => {
  return (
    <View style={[styles.container, style]}>
      <Text style={amountStyle}>
        {formatNumericByDecimalsDisplay(amount, decimals)}
      </Text>
      {currency && (
        <CurrencySymbol currency={currency} shouldShowIcon={shouldShowIcon} />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  amount: {
    ...textStyles.labelM,
    color: textColors.textBlack,
  },
  tinyIcon: {
    width: 16,
    height: 16,
  },
})
