import React from "react"
import { MarketplaceOffer, MarketplaceOfferState } from "src/api"
import { CancelOfferButton } from "./CancelOfferButton"
import TakeOfferButton from "./TakeOfferButton"
import { useAccount } from "wagmi"

interface OfferActionButtonProps {
  offer: MarketplaceOffer
}

export const OfferActionButton: React.FC<OfferActionButtonProps> = ({
  offer,
}) => {
  const { address } = useAccount()

  if (offer.state === MarketplaceOfferState.CANCELLED) return null
  return (
    <>
      {address &&
        address.toLowerCase() === offer.seller.address.toLowerCase() &&
        offer.soldAmount !== offer.sellingAmount && (
          <CancelOfferButton offer={offer} />
        )}
      {address?.toLowerCase() !== offer.seller.address.toLowerCase() &&
        offer.soldAmount !== offer.sellingAmount && (
          <TakeOfferButton offer={offer} />
        )}
    </>
  )
}
