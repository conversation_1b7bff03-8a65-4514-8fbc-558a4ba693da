import React from "react"
import { StyleSheet, Text, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { useTranslation } from "react-i18next"

interface OfferStateConfig {
  color: string
  label: string
  textColor: string
}

interface OfferStateProps {
  style?: ViewStyle
  state: string
  color?: string
}

const getOfferStateConfig = (
  state: string,
  t: (key: string) => string
): OfferStateConfig => {
  const configs: Record<string, OfferStateConfig> = {
    CANCELLED: {
      color: Colors.black5,
      label: t("Cancelled"),
      textColor: textColors.textBlack11,
    },
    SOLD: {
      color: Colors.greenLight,
      label: t("Sold"),
      textColor: textColors.textBlack11,
    },
    SELLING: {
      color: Colors.goldLight,
      label: t("Selling"),
      textColor: textColors.textBlack11,
    },
  }

  return (
    configs[state] || {
      color: Colors.primary,
      label: t("Unknown status"),
      textColor: textColors.textBlack11,
    }
  )
}

const OfferState: React.FC<OfferStateProps> = ({ state, color, style }) => {
  const { t } = useTranslation()
  const config = getOfferStateConfig(state, t)

  return (
    <Text
      style={[
        style,
        styles.status,
        { backgroundColor: color || config.color, color: config.textColor },
      ]}
    >
      {config.label}
    </Text>
  )
}

const styles = StyleSheet.create({
  status: {
    ...textStyles.labelM,
    alignSelf: "flex-start",
    borderRadius: 4,
    padding: 4,
  },
})

export { OfferState }
