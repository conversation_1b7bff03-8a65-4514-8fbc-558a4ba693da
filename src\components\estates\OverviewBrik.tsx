import React from "react"
import {
  Image,
  StyleProp,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import Colors, { textColors } from "src/config/colors"
import { formatCurrency } from "utils"
import icBrik from "../../../assets/images/ic_brik.png"
import icOverView from "../../../assets/images/ic_over_view.png"

interface OverviewBrikItemProps {
  label: string
  value: number
}

const OverViewBrikItem: React.FC<OverviewBrikItemProps> = ({
  label,
  value,
}) => {
  return (
    <View style={styles.brikBox}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.rowSpaceBetween}>
        <Text style={styles.value}>{formatCurrency(value)}</Text>
        <Image source={icBrik} style={viewStyles.tinyIcon} />
      </View>
    </View>
  )
}

interface OverviewBrikProps {
  style?: StyleProp<ViewStyle>
  totalAmount: number
  brikSold: number
  brikUnlock: number
}

const OverviewBrik: React.FC<OverviewBrikProps> = ({
  style = styles.overviewContainer,
  totalAmount,
  brikSold,
  brikUnlock,
}) => {
  const { t } = useTranslation()
  return (
    <View style={style}>
      <View style={styles.row}>
        <Image source={icOverView} style={viewStyles.icon} />
        <Text style={styles.title}>{`${t("Overview")}:`}</Text>
      </View>
      <OverViewBrikItem label={t("Total amount")} value={totalAmount} />
      <View style={styles.brikContainer}>
        <OverViewBrikItem label={t("BRIK sold")} value={brikSold} />
        <OverViewBrikItem label={t("BRIK unlock")} value={brikUnlock} />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    marginHorizontal: 4,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  overviewContainer: {
    marginTop: 20,
  },
  title: {
    ...textStyles.titleM,
    color: textColors.textBlack11,
    marginStart: 8,
  },
  brikContainer: {
    flexDirection: "row",
    marginTop: 8,
  },
  brikBox: {
    flex: 1,
    backgroundColor: Colors.black3,
    padding: 16,
    marginHorizontal: 4,
    borderRadius: 8,
  },
  label: {
    ...textStyles.bodyM,
    color: Colors.black7,
    marginBottom: 4,
  },
  value: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
  },
})

export { OverviewBrik }
