import React, { useState } from "react"
import { MortgageTokenLoan } from "src/api"
import { useTranslation } from "react-i18next"
import { parseEther } from "@ethersproject/units"
import { erc20Abi, useErc20Allowance } from "src/api/contracts"
import { useAccount, useWriteContract } from "wagmi"
import { useEthersProvider } from "hooks"
import { CONTRACT_ADDRESS_MORTGAGE_TOKEN } from "src/config/env"
import { PrimaryButton } from "components/common/Button"
import { mortgageTokenAbi } from "src/api/contracts/mortgage-token"
import { maxUint256 } from "viem"
import Colors from "src/config/colors"
import { StyleSheet } from "react-native"
import { textStyles } from "src/config/styles"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import { SimpleLoadingView } from "../SimpleLoadingView"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "RepayLoanButton" })

interface RepayLoanButtonProps {
  loan: MortgageTokenLoan
  onRefresh?: () => void
}

const RepayLoanButton: React.FC<RepayLoanButtonProps> = ({
  loan,
  onRefresh,
}) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [canCancelLoading, setCanCancelLoading] = useState<boolean>(true)

  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()
  const currencyAllowanceWei = useErc20Allowance(
    address as string,
    loan.currency as `0x${string}`,
    CONTRACT_ADDRESS_MORTGAGE_TOKEN
  )

  const handleRepay = async () => {
    if (isLoading || !ethersProvider) return
    setIsLoading(true)
    try {
      if (
        currencyAllowanceWei < BigInt(parseEther(loan.repayment).toString())
      ) {
        const txHash = await writeContractAsync({
          address: loan.currency as `0x${string}`,
          abi: erc20Abi,
          functionName: "approve",
          args: [CONTRACT_ADDRESS_MORTGAGE_TOKEN, maxUint256],
        })
        setCanCancelLoading(!txHash)
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          showError(t("Failed to approve"))
          throw new Error("Fail to approve currency")
        }
      }
      setCanCancelLoading(true)
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
        abi: mortgageTokenAbi,
        functionName: "repay",
        args: [BigInt(loan.id)],
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Repay success") + ". " + t("Data will be updated in few seconds")
        )
        onRefresh?.()
      } else {
        showError(t("Repay failed"))
      }
    } catch (e) {
      showError(t("Repay failed"))
      logger.error("Failed to repay loan", {
        component: "TokenizationLoansView",
        action: "Repay",
        error: e,
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!address) {
    return null
  }

  return (
    <>
      <PrimaryButton
        enabled={Boolean(address)}
        title={t("Repay")}
        textStyle={styles.buttonText}
        onPress={handleRepay}
        color={Colors.surfaceNormal}
        style={{ marginTop: 4 }}
      />
      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </>
  )
}

const styles = StyleSheet.create({
  buttonText: {
    ...textStyles.labelL,
    textAlign: "center",
  },
})

export { RepayLoanButton }
