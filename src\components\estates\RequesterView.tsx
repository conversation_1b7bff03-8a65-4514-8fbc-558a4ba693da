import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { UserBrief } from "src/api"
import Colors, { textColors } from "src/config/colors"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import { AvatarView } from "../profile/AvatarView"
import { AddressView } from "../profile/AddressView"

interface RequesterViewProps {
  requester: UserBrief
}

const RequesterView: React.FC<RequesterViewProps> = ({ requester }) => {
  const { t } = useTranslation()
  return (
    <View style={styles.sellerContainer}>
      <AvatarView size={40} avatarUrl={requester.avatarUrl ?? ""} />
      <View style={styles.sellerInfo}>
        <Text style={styles.sellerTitle}>{t("Owner")}</Text>
        <Text style={styles.name}>
          {requester.alias || requester.name || t("Unknown")}
        </Text>
      </View>
      <View style={{ flex: 1, alignItems: "flex-end" }}>
        <AddressView address={requester.address} />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  sellerContainer: {
    flexDirection: "row",
    marginVertical: 12,
    alignItems: "flex-end",
  },
  sellerInfo: {
    marginStart: 8,
  },
  sellerTitle: {
    ...textStyles.labelL,
    color: Colors.black7,
  },
  name: {
    ...textStyles.bodyM,
    color: textColors.textBlack11,
  },
})

export { RequesterView }
