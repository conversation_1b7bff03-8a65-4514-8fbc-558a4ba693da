import { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import React, { useEffect } from "react"
import { StyleSheet, ViewStyle } from "react-native"
import { Dropdown } from "react-native-element-dropdown"
import { Currency } from "src/api"
import { formatMoney } from "utils/format"
import { formatEther } from "@ethersproject/units"
import { Control, Controller, UseFormSetValue } from "react-hook-form"
import Logger from "src/utils/logger"

interface SelectCurrencyProps {
  currencies: Currency[]
  style?: ViewStyle
  isShowMinMaxPrice?: boolean
  control: Control<any>
  setValue: UseFormSetValue<any>
}

const logger = new Logger({ tag: "SelectCurrency" })

export const SelectCurrency: React.FC<SelectCurrencyProps> = ({
  currencies,
  control,
  isShowMinMaxPrice = false,
  style,
  setValue,
}) => {
  const deps = currencies.map((i) => i.currency).join()
  useEffect(() => {
    const usdt = currencies.find((i) => i.symbol === "USDT")
    logger.debug("Found USDT currency", usdt)
    if (usdt) {
      logger.debug("Setting currency value", { currency: usdt.currency })
      setValue("currencyId", usdt.currency)
    }
  }, [deps])

  const data = currencies.map((currency) => ({
    label: isShowMinMaxPrice
      ? currency.symbol +
        ` (${formatMoney(formatEther(currency.minUnitPrice))} -> ${formatMoney(formatEther(currency.maxUnitPrice))})`
      : currency.symbol,
    value: currency.currency,
  }))

  return (
    <Controller
      control={control}
      name="currencyId"
      render={({ field: { onChange, value } }) => (
        <Dropdown
          value={value}
          data={data}
          labelField={"label"}
          valueField={"value"}
          onChange={({ value }) => onChange(value)}
          style={[
            style ? style : viewStyles.input,
            { flex: 1, marginStart: 8 },
          ]}
          selectedTextStyle={styles.dropdownItem}
          placeholderStyle={styles.dropdownItem}
          itemTextStyle={styles.dropdownItem}
        />
      )}
    />
  )
}

const styles = StyleSheet.create({
  dropdownItem: {
    ...textStyles.bodyM,
    color: textColors.textBlack9,
    height: 20,
  },
})
