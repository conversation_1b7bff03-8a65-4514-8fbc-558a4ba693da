import React, { ReactNode } from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { MaterialCommunityIcons } from "@expo/vector-icons"

interface StepViewProps {
  progress: string[]
  currentStep: number
  style?: ViewStyle
}

interface StepItemProps {
  step: string
  index: number
  currentStep: number
  isFirst: boolean
}

const StepItem: React.FC<StepItemProps> = ({ index, currentStep, isFirst }) => {
  const isSuccess = index <= currentStep
  const color = calColor(index, currentStep)

  return (
    <View style={[styles.stepItem, { flex: isFirst ? 0 : 1 }]}>
      {!isFirst && <LineView color={color} />}
      <CircleView color={color}>
        {isSuccess && (
          <MaterialCommunityIcons name="check" size={12} color={color} />
        )}
      </CircleView>
    </View>
  )
}

const StepLabel: React.FC<{
  step: string
  index: number
  currentStep: number
  totalSteps: number
}> = ({ step, index, currentStep, totalSteps }) => {
  const color = calColor(index, currentStep)
  const isEdge = index === 0 || index === totalSteps - 1

  return (
    <Text
      style={[
        textStyles.bodyM,
        styles.stepLabel,
        {
          flex: isEdge ? 0 : 1,
          color,
        },
      ]}
    >
      {step}
    </Text>
  )
}

const StepView: React.FC<StepViewProps> = ({
  progress,
  currentStep = -1,
  style,
}) => {
  return (
    <View>
      <View style={[style, styles.stepContainer]}>
        {progress.map((step, index) => (
          <StepItem
            key={index}
            step={step}
            index={index}
            currentStep={currentStep}
            isFirst={index === 0}
          />
        ))}
      </View>

      <View style={styles.labelContainer}>
        {progress.map((step, index) => (
          <StepLabel
            key={index}
            step={step}
            index={index}
            currentStep={currentStep}
            totalSteps={progress.length}
          />
        ))}
      </View>
    </View>
  )
}

const CircleView: React.FC<{
  color: string
  children?: ReactNode
}> = ({ color, children }) => {
  return (
    <View
      style={[
        styles.circle,
        {
          borderColor: color,
          justifyContent: "center",
          alignItems: "center",
        },
      ]}
    >
      {children}
    </View>
  )
}

const LineView: React.FC<{
  color: string
}> = ({ color }) => {
  return <View style={[styles.line, { backgroundColor: color }]} />
}

const calColor = (index: number, currentStep: number): string => {
  return index < currentStep
    ? Colors.green
    : index === currentStep
      ? Colors.blueLink
      : Colors.gray600
}

const styles = StyleSheet.create({
  stepContainer: {
    flexDirection: "row",
  },
  stepItem: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  labelContainer: {
    flexDirection: "row",
  },
  stepLabel: {
    marginTop: 2,
    textAlign: "center",
  },
  circle: {
    width: 20,
    height: 20,
    borderRadius: 16,
    borderWidth: 2,
  },
  line: {
    flex: 1,
    height: 2,
  },
})

export { StepView }
