import React, { useState } from "react"
import { StyleSheet } from "react-native"
import { PrimaryButton } from "../common/Button"
import { MarketplaceOffer } from "src/api"
import { TakeOfferModal } from "./TakeOfferModal"
import { useTranslation } from "react-i18next"
import { useAccount } from "wagmi"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"

interface Props {
  offer: Omit<MarketplaceOffer, "seller">
}

const TakeOfferButton: React.FC<Props> = ({ offer }) => {
  const { address } = useAccount()
  const { t } = useTranslation()
  const [isShow, setIsShow] = useState(false)

  if (!address) return null

  return (
    <>
      <PrimaryButton
        title={t("Buy")}
        onPress={() => setIsShow(true)}
        width={"100%"}
        color={Colors.surfaceNormal}
        borderRadius={8}
        style={styles.takeOfferButton}
        enabled={Boolean(address)}
      />
      <TakeOfferModal
        visible={isShow}
        offer={offer}
        onClose={() => {
          setIsShow(false)
        }}
      />
    </>
  )
}

const styles = StyleSheet.create({
  modalContent: {
    padding: 12,
    backgroundColor: "white",
  },
  takeOfferButton: {
    ...textStyles.labelL,
    textAlign: "center",
    marginTop: 8,
  },
})

export default TakeOfferButton
