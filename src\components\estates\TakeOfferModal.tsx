import React, { useState } from "react"
import { StyleSheet, View } from "react-native"
import { useTranslation } from "react-i18next"
import { BaseModal } from "components/common/BaseModal"
import { InputField, PrimaryButton } from "components"
import { MarketplaceOffer } from "src/api"
import { maxUint256, parseAbi } from "viem"
import { CONTRACT_ADDRESS_MARKETPLACE } from "src/config/env"
import { Controller, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { erc20Abi, useErc20Allowance } from "src/api/contracts"
import { marketplaceAbi } from "src/api/contracts/marketplace"
import { z } from "zod"
import { useAccount, useWriteContract } from "wagmi"
import { useEthersProvider } from "hooks"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import { formatNumericByDecimals } from "utils/format"
import Logger from "src/utils/logger"

interface TakeOfferModalProps {
  visible: boolean
  onClose: () => void
  offer: Omit<MarketplaceOffer, "seller">
}

const logger = new Logger({ tag: "TakeOfferModal" })

const formSchema = z.object({
  quantity: z.number().min(1),
})
type Payload = z.infer<typeof formSchema>

export const TakeOfferModal: React.FunctionComponent<TakeOfferModalProps> = ({
  visible,
  offer,
  onClose,
}) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)

  const { address } = useAccount()
  const {
    estate: { decimals, id: estateId },
    currency: currencyId,
    isDivisible,
    unitPrice,
  } = offer

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    mode: "onTouched",
    defaultValues: {
      quantity: isDivisible
        ? 1
        : Number(formatNumericByDecimals(offer.sellingAmount, decimals)),
    },
  })

  const ethersProvider = useEthersProvider()

  const currencyAllowanceWei = useErc20Allowance(
    address as string,
    currencyId as `0x${string}`,
    CONTRACT_ADDRESS_MARKETPLACE
  )

  const { writeContractAsync } = useWriteContract()

  const onSubmit = async (data: Payload) => {
    if (isLoading) return
    if (!ethersProvider) return
    if (
      data.quantity >
      Number(offer.sellingAmount) - Number(offer.soldAmount)
    ) {
      form.setError("quantity", {
        type: "custom",
        message: `You can only buy up to ${Number(offer.sellingAmount) - Number(offer.soldAmount)} NFTs`,
      })
      return
    }

    setIsLoading(true)
    try {
      if (
        currencyAllowanceWei <
        (BigInt(unitPrice) * BigInt(data.quantity * Math.pow(10, decimals))) /
          BigInt(Math.pow(10, decimals))
      ) {
        const txHash = await writeContractAsync({
          address: offer.currency as `0x${string}`,
          abi: erc20Abi,
          functionName: "approve",
          args: [CONTRACT_ADDRESS_MARKETPLACE, maxUint256],
        })
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          showError(t("Fail to approve currency"))
          throw new Error("Fail to approve currency")
        }
      }
      let txHash: string
      if (isDivisible) {
        txHash = await writeContractAsync({
          address: CONTRACT_ADDRESS_MARKETPLACE,
          abi: parseAbi(["function buyToken(uint256, uint256, uint256)"]),
          functionName: "buyToken",
          args: [
            BigInt(offer.id),
            BigInt(estateId),
            BigInt(data.quantity) * BigInt(Math.pow(10, decimals)),
          ],
        })
      } else {
        txHash = await writeContractAsync({
          address: CONTRACT_ADDRESS_MARKETPLACE,
          abi: marketplaceAbi,
          functionName: "buyToken",
          args: [BigInt(offer.id), BigInt(estateId)],
        })
      }

      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Buy NFT success") + ". " + t("Data will be updated in few seconds")
        )
      } else {
        showError(t("Buy NFT failed"))
      }
    } catch (e: any) {
      logger.error("Buy NFT failed", e)
    } finally {
      closeAndReset()
    }
  }

  const closeAndReset = () => {
    form.reset()
    setIsLoading(false)
    onClose()
  }

  return (
    <BaseModal
      visible={visible}
      isShowCloseIcon={true}
      isDisableClose={isLoading}
      title={t("Take Offer")}
      onClose={closeAndReset}
    >
      <View style={styles.modalContent}>
        <Controller
          control={form.control}
          name="quantity"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputField
              label={t("Amount")}
              onBlur={onBlur}
              onChangeText={onChange}
              value={value.toString()}
              type={"number"}
              inputMode="numeric"
              error={form.formState.errors.quantity?.message}
              disabled={isLoading || !isDivisible}
            />
          )}
        />
        <PrimaryButton
          title={t("Buy NFT")}
          onPress={form.handleSubmit(onSubmit)}
          style={styles.marginTop8}
          isLoading={isLoading}
          enabled={Boolean(address)}
        />
      </View>
    </BaseModal>
  )
}

const styles = StyleSheet.create({
  modalContent: {
    width: "100%",
  },
  marginTop8: {
    marginTop: 8,
  },
})
