import React from "react"
import { Dropdown } from "react-native-element-dropdown"
import { StyleSheet } from "react-native"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"

interface TokenData {
  label: string
  value: string
}

interface TokenPickerViewProps {
  token: string
  onSelectToken: (token: string) => void
  data?: TokenData[]
}

const TokenPickerView: React.FC<TokenPickerViewProps> = ({
  token,
  onSelectToken,
  data = [],
}) => {
  if (!data.length) {
    return null
  }

  const handleChange = (item: TokenData) => {
    onSelectToken(item.value)
  }

  return (
    <Dropdown
      style={styles.dropdown}
      data={data}
      selectedTextStyle={textStyles.bodyM}
      labelField="label"
      valueField="value"
      value={token}
      onChange={handleChange}
    />
  )
}

const styles = StyleSheet.create({
  dropdown: {
    height: 36,
    width: 80,
    borderColor: Colors.border,
    borderWidth: 0.5,
    marginTop: 26,
    marginStart: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
  },
})

export { TokenPickerView }
