import React from "react"
import { StyleSheet, Text, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { useTranslation } from "react-i18next"

interface TokenizationRequestStateConfig {
  color: string
  label: string
  textColor: string
}

interface TokenizationRequestStateProps {
  style?: ViewStyle
  state: string
  color?: string
}

const getTokenizationRequestStateConfig = (
  state: string,
  t: (key: string) => string
): TokenizationRequestStateConfig => {
  const configs: Record<string, TokenizationRequestStateConfig> = {
    CONFIRMED: {
      color: Colors.greenLight,
      label: t("Tokenized"),
      textColor: textColors.textBlack11,
    },
    CANCELLED: {
      color: Colors.black5,
      label: t("Cancelled"),
      textColor: textColors.textBlack11,
    },
    SELLING: {
      color: Colors.goldLight,
      label: t("Public sale"),
      textColor: textColors.textBlack11,
    },
    EXPIRED: {
      color: Colors.redLight,
      label: t("Cancelled"),
      textColor: textColors.textWhite,
    },
    INSUFFICIENT_SOLD_AMOUNT: {
      color: Colors.redLight,
      label: t("Cancelled"),
      textColor: textColors.textWhite,
    },
    TRANSFERRING_OWNERSHIP: {
      color: Colors.pinkLight,
      label: t("Transferring ownership"),
      textColor: textColors.textBlack11,
    },
  }

  return (
    configs[state] || {
      color: Colors.green,
      label: t("Unknown state"),
      textColor: textColors.textBlack11,
    }
  )
}

const TokenizationRequestState: React.FC<TokenizationRequestStateProps> = ({
  state,
  color,
  style,
}) => {
  const { t } = useTranslation()
  const config = getTokenizationRequestStateConfig(state, t)

  return (
    <Text
      style={[
        style,
        styles.state,
        { backgroundColor: color || config.color, color: config.textColor },
      ]}
    >
      {config.label}
    </Text>
  )
}

const styles = StyleSheet.create({
  state: {
    ...textStyles.labelM,
    alignSelf: "flex-start",
    borderRadius: 4,
    padding: 4,
  },
})

export { TokenizationRequestState }
