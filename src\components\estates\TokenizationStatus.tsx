import React from "react"
import { StyleSheet, Text, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { useTranslation } from "react-i18next"

interface TokenizationStatusConfig {
  color: string
  label: string
  textColor: string
}

interface TokenizationStatusProps {
  style?: ViewStyle
  status: string
  color?: string
}

const getTokenizationStatusConfig = (
  status: string,
  t: (key: string) => string
): TokenizationStatusConfig => {
  const configs: Record<string, TokenizationStatusConfig> = {
    APPLICATION_VALIDATING: {
      color: Colors.black5,
      label: t("Awaiting Approval"),
      textColor: textColors.textBlack11,
    },
    APPLICATION_CANCELLED: {
      color: Colors.redLight,
      label: t("Rejected"),
      textColor: textColors.textWhite,
    },
    REQUEST_SELLING: {
      color: Colors.goldLight,
      label: t("Selling"),
      textColor: textColors.textBlack11,
    },
    REQUEST_CANCELLED: {
      color: Colors.black5,
      label: t("Cancelled"),
      textColor: textColors.textBlack11,
    },
    REQUEST_CONFIRMED: {
      color: Colors.greenLight,
      label: t("Confirmed"),
      textColor: textColors.textBlack11,
    },
    REQUEST_INSUFFICIENT_SOLD_AMOUNT: {
      color: Colors.redLight,
      label: t("Cancelled"),
      textColor: textColors.textBlack11,
    },
    REQUEST_TRANSFERRING_OWNERSHIP: {
      color: Colors.surfaceNormal,
      label: t("Transferring ownership"),
      textColor: textColors.textBlack11,
    },
    REQUEST_EXPIRED: {
      color: Colors.black5,
      label: t("Cancelled"),
      textColor: textColors.textBlack11,
    },
  }

  return (
    configs[status] || {
      color: Colors.primary,
      label: t("Unknown status"),
      textColor: textColors.textBlack11,
    }
  )
}

const TokenizationStatus: React.FC<TokenizationStatusProps> = ({
  status,
  color,
  style,
}) => {
  const { t } = useTranslation()
  const config = getTokenizationStatusConfig(status, t)

  return (
    <Text
      style={[
        style,
        styles.status,
        { backgroundColor: color || config.color, color: config.textColor },
      ]}
    >
      {config.label}
    </Text>
  )
}

const styles = StyleSheet.create({
  status: {
    ...textStyles.labelM,
    alignSelf: "flex-start",
    borderRadius: 4,
    padding: 4,
  },
})

export { TokenizationStatus }
