import React from "react"
import { Image, ImageRequireSource, StyleSheet, Text, View } from "react-native"
import { CardView } from "components"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { useErc20Balance, useErc20Formatter } from "src/api/contracts/erc20"
import {
  CONTRACT_ADDRESS_AUCTION_CURRENCY,
  CONTRACT_ADDRESS_PRIMARY_TOKEN,
  CONTRACT_ADDRESS_STAKE_TOKEN,
} from "src/config/env"
import { parseCurrency } from "utils"
import { useTranslation } from "react-i18next"
import { useAccount } from "wagmi"
import brikIcon from "../../../assets/images/ic_brik.png"
import brikiIcon from "../../../assets/images/ic_briki.png"
import usdtIcon from "../../../assets/images/ic_usdt.png"

interface TokenBalance {
  iconSource: ImageRequireSource
  label: string
  value: string
}

const useTokenBalance = (): TokenBalance[] => {
  const { t } = useTranslation()
  const { address: userAddress } = useAccount()

  const currencyTokenBalance = useErc20Balance(
    userAddress,
    CONTRACT_ADDRESS_AUCTION_CURRENCY
  )
  const currencyFormatter = useErc20Formatter(CONTRACT_ADDRESS_AUCTION_CURRENCY)
  const primaryTokenBalance = useErc20Balance(
    userAddress,
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )
  const primaryTokenFormatter = useErc20Formatter(
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )
  const stakeTokenBalance = useErc20Balance(
    userAddress,
    CONTRACT_ADDRESS_STAKE_TOKEN
  )
  const stakeTokenFormatter = useErc20Formatter(CONTRACT_ADDRESS_STAKE_TOKEN)

  return [
    {
      iconSource: brikIcon,
      label: t("brik"),
      value: parseCurrency(
        primaryTokenFormatter.formatFixed(primaryTokenBalance)
      ),
    },
    {
      iconSource: brikiIcon,
      label: t("BRIKI"),
      value: parseCurrency(stakeTokenFormatter.formatFixed(stakeTokenBalance)),
    },
    {
      iconSource: usdtIcon,
      label: t("usdt"),
      value: parseCurrency(currencyFormatter.formatFixed(currencyTokenBalance)),
    },
  ]
}

const AccountStat: React.FC = () => {
  const { t } = useTranslation()
  const { isConnected } = useAccount()
  const tokenBalances = useTokenBalance()

  if (!isConnected) return null
  return (
    <CardView style={styles.card}>
      <View style={styles.container}>
        {tokenBalances.map((token) => {
          return (
            <BalanceItem
              key={token.label}
              iconSource={token.iconSource}
              label={t(token.label).toUpperCase()}
              value={token.value}
            />
          )
        })}
      </View>
    </CardView>
  )
}

interface BalanceItemProps {
  iconSource: ImageRequireSource
  label: string
  value: string
}

const BalanceItem: React.FC<BalanceItemProps> = ({
  iconSource,
  label,
  value,
}) => (
  <View style={styles.balanceItem}>
    <Image source={iconSource} style={viewStyles.bigIcon} />
    <View style={styles.textContainer}>
      <Text style={textStyles.titleL}>{label}</Text>
      <Text style={textStyles.body1}>{value}</Text>
    </View>
  </View>
)

const styles = StyleSheet.create({
  card: {
    alignSelf: "stretch",
    marginVertical: 16,
  },
  container: {
    backgroundColor: Colors.black4,
    width: "100%",
    paddingHorizontal: 12,
    paddingTop: 4,
    paddingBottom: 12,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  balanceItem: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  textContainer: {
    marginStart: 12,
  },
})

export { AccountStat }
