import React, { ReactNode } from "react"
import {
  Modal,
  StyleSheet,
  Text,
  View,
  Pressable,
  Image,
  StyleProp,
  ViewStyle,
} from "react-native"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import iconClose from "assets/imagesV2/ic_close.png"

interface BaseBottomSheetProps {
  visible: boolean
  onClose: () => void
  title?: string
  children: ReactNode
  style?: StyleProp<ViewStyle>
}

export const BaseBottomSheet: React.FC<BaseBottomSheetProps> = ({
  visible,
  onClose,
  title,
  children,
  style,
}) => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Pressable style={styles.dismissArea} onPress={onClose} />
        <View style={[styles.bottomSheetContainer, style]}>
          <View style={styles.header}>
            <Pressable onPress={onClose} style={styles.closeButton}>
              <Image source={iconClose} style={styles.closeIcon} />
            </Pressable>
            {title && <Text style={styles.title}>{title}</Text>}
          </View>
          {children}
        </View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  dismissArea: {
    flex: 1,
  },
  bottomSheetContainer: {
    backgroundColor: Colors.PalleteBlack,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.Neutral900,
    position: "relative",
  },
  closeButton: {
    position: "absolute",
    left: 16,
    padding: 4,
  },
  closeIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.Neutral300,
  },
  title: {
    ...textStyles.LMedium,
    color: Colors.white,
  },
})
