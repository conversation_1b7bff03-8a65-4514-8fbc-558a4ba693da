import React, { useCallback } from "react"
import { Modal, StyleSheet, Text, View, Image } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { CustomPressable } from "./CustomPressable"
import { Divider } from "react-native-paper"
import icClose from "assets/imagesV2/ic_close.png"

interface BaseModalProps {
  visible: boolean
  isShowCloseIcon?: boolean
  isDisableClose?: boolean
  title?: string
  children: React.ReactNode
  onClose: () => void
}

export const BaseModal: React.FC<BaseModalProps> = ({
  isShowCloseIcon = false,
  title,
  visible,
  isDisableClose = false,
  children,
  onClose,
}) => {
  const handleClose = useCallback(() => {
    if (!isDisableClose) {
      onClose()
    }
  }, [isDisableClose, onClose])

  return (
    <Modal visible={visible} onRequestClose={handleClose} transparent>
      <View style={styles.overlay}>
        <View style={styles.container}>
          {title && (
            <View style={styles.header}>
              <Text style={styles.title}>{title}</Text>
            </View>
          )}
          <Divider style={styles.divider} />

          {children}

          {isShowCloseIcon && (
            <CustomPressable onPress={handleClose} style={styles.closeIcon}>
              <Image
                source={icClose}
                style={[
                  viewStyles.size16Icon,
                  { tintColor: Colors.Neutral500 },
                ]}
              />
            </CustomPressable>
          )}
        </View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    padding: 8,
    backgroundColor: Colors.opacityBlack60,
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    width: "95%",
    position: "relative",
    backgroundColor: Colors.Neutral950,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    borderRadius: 8,
    padding: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  closeIcon: {
    position: "absolute",
    top: 8,
    right: 12,
  },
  header: {
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingBottom: 8,
  },
  title: {
    ...textStyles.LMedium,
    color: Colors.PalleteWhite,
    padding: 0,
    flex: 1,
    textAlign: "left",
  },
  divider: {
    height: 1,
    width: "100%",
    backgroundColor: Colors.Neutral900,
  },
})
