import React from "react"
import {
  ActivityIndicator,
  DimensionValue,
  StyleProp,
  Text,
  TextStyle,
  View,
  ViewStyle,
  StyleSheet,
} from "react-native"
import { CustomPressable } from "./CustomPressable"
import Colors from "src/config/colors"
import { convertHexToRGBA } from "src/utils"
import { textStyles } from "src/config/styles"

interface PrimaryButtonProps {
  title: string
  onPress?: () => void
  style?: ViewStyle
  textStyle?: StyleProp<TextStyle>
  width?: DimensionValue
  height?: DimensionValue
  borderRadius?: number
  icon?: React.ReactNode
  trailingIcon?: React.ReactNode
  enabled?: boolean
  color?: string
  contentColor?: string
  isLoading?: boolean
}

export const PrimaryButton: React.FC<PrimaryButtonProps> = ({
  title,
  onPress,
  style,
  textStyle = textStyles.SBold,
  width = "auto",
  height = 24,
  borderRadius = 4,
  icon,
  trailingIcon,
  enabled = true,
  isLoading = false,
  color,
  contentColor,
}) => {
  const isEnable = enabled && !isLoading
  const buttonColor = isEnable ? color || Colors.Primary500 : Colors.Neutral700
  const textColor = isEnable
    ? contentColor || Colors.PalleteBlack
    : Colors.Neutral900
  return (
    <CustomPressable
      onPress={onPress}
      style={[
        style,
        {
          width,
          height,
          borderRadius,
          backgroundColor: buttonColor,
        },
      ]}
      enabled={isEnable}
    >
      <View style={[styles.buttonContainer]}>
        {isLoading && (
          <ActivityIndicator
            size="small"
            color={convertHexToRGBA(textColor, 0.5)}
          />
        )}
        {!isLoading && icon}
        {(isLoading || icon) && <View style={styles.iconSpacer} />}
        <Text
          style={[
            textStyle,
            {
              color: textColor,
            },
          ]}
        >
          {title}
        </Text>
        {trailingIcon && <View style={styles.iconSpacer} />}
        {trailingIcon}
      </View>
    </CustomPressable>
  )
}

export const SecondaryButton: React.FC<PrimaryButtonProps> = ({
  title,
  onPress,
  style,
  width,
  height = 38,
  borderRadius = 8,
  icon,
  trailingIcon,
  enabled = true,
  isLoading = false,
}) => {
  const isEnable = enabled && !isLoading
  const contentColor = isEnable ? Colors.PalleteWhite : Colors.Neutral900
  return (
    <CustomPressable onPress={onPress} style={style} enabled={isEnable}>
      <View
        style={[
          styles.buttonContainer,
          {
            width,
            height,
            borderRadius,
            backgroundColor: enabled ? Colors.Neutral900 : Colors.Neutral700,
          },
        ]}
      >
        {isLoading && <ActivityIndicator size="small" color={contentColor} />}
        {!isLoading && icon}
        {(isLoading || icon) && <View style={styles.iconSpacer} />}
        <Text style={[textStyles.LMedium, { color: contentColor }]}>
          {title}
        </Text>
        {trailingIcon && <View style={styles.iconSpacer} />}
        {trailingIcon}
      </View>
    </CustomPressable>
  )
}

export const IconButton: React.FC<{
  icon: React.ReactNode
  onPress?: () => void
  enabled?: boolean
  style?: ViewStyle
}> = ({ icon, onPress, enabled = true, style }) => {
  return (
    <CustomPressable onPress={onPress} enabled={enabled} style={style}>
      {icon}
    </CustomPressable>
  )
}

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 16,
    height: "100%",
  },
  iconSpacer: {
    width: 8,
  },
})
