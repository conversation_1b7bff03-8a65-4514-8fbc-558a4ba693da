import React from "react"
import { View } from "react-native"
import Svg, { Circle } from "react-native-svg"
import Colors from "src/config/colors"

interface CircularProgressProps {
  percentage: number
  size?: number
  strokeWidth?: number
  colorBackground?: string
  colorProgress?: string
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  percentage,
  size = 10,
  strokeWidth = 2,
  colorBackground = Colors.circleProgressBackground,
  colorProgress = Colors.circleProgress,
}) => {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const progress = (percentage / 100) * circumference

  return (
    <View style={{ width: size, height: size }}>
      <Svg width={size} height={size}>
        {/* Background circle */}
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={colorBackground}
          strokeWidth={strokeWidth}
          fill="none"
        />
        {/* Progress circle */}
        <Circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={colorProgress}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={`${circumference} ${circumference}`}
          strokeDashoffset={circumference - progress}
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
          strokeLinecap="round"
        />
      </Svg>
    </View>
  )
}

export { CircularProgress }
