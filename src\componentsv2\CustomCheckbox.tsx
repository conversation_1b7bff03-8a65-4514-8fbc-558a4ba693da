import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { textStyles } from "src/config/styles"
import Checkbox from "expo-checkbox"
import Colors from "src/config/colors"

interface CustomCheckboxProps {
  label: string
  disabled?: boolean
  isChecked: boolean
  onToggle: (value: boolean) => void
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  label,
  disabled = false,
  isChecked,
  onToggle,
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>
      <Checkbox
        style={styles.checkbox}
        color={disabled ? Colors.Neutral900 : Colors.Primary500}
        value={isChecked}
        onValueChange={() => !disabled && onToggle(!isChecked)}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    ...textStyles.MMedium,
    marginRight: 6,
    color: Colors.Neutral500,
  },
  checkbox: {
    width: 16,
    height: 16,
    borderRadius: 2,
    borderWidth: 1,
    borderColor: Colors.Neutral700,
  },
})

export { CustomCheckbox }
