import React, { useEffect, useState } from "react"
import { StyleSheet, Text, TextStyle, View, ViewStyle } from "react-native"
import Colors, { textColors } from "src/config/colors"
import { textStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import { getTimePartsFromDuration, getTimeLeftInHHMMSS } from "utils/timeExt"

const MILISECONDS_PER_SECOND = 1000

export enum TIME_TYPE {
  DEFAULT = "DEFAULT",
  BRIK = "BRIK",
  SHORT = "SHORT",
  TEXT = "TEXT",
}

const TimeItemView: React.FC<{
  time: string
  unitTime: string
  timeColor: string
}> = ({ time, unitTime, timeColor }) => {
  return (
    <View style={styles.row}>
      <Text style={[styles.time, { color: timeColor }]}>{time}</Text>
      <Text style={styles.timeNote}>{unitTime}</Text>
    </View>
  )
}

const BrikTimeItemView: React.FC<{
  time: string
  unitTime: string
  timeColor: string
}> = ({ time, unitTime, timeColor }) => {
  return (
    <View style={styles.brikItem}>
      <Text style={[styles.time, { color: timeColor }]}>{time}</Text>
      <Text style={styles.timeNote}>{unitTime}</Text>
    </View>
  )
}

interface DefaultTimeViewProps {
  days: string
  hours: string
  minutes: string
  seconds: string
  timeColor: string
}

const DefaultTimeView: React.FC<DefaultTimeViewProps> = ({
  days,
  hours,
  minutes,
  seconds,
  timeColor,
}) => {
  const { t } = useTranslation()
  return (
    <View style={styles.timerContainer}>
      <TimeItemView time={days} unitTime={t("dayUnit")} timeColor={timeColor} />
      <Text style={styles.colon}>:</Text>
      <TimeItemView
        time={hours}
        unitTime={t("hourUnit")}
        timeColor={timeColor}
      />
      <Text style={styles.colon}>:</Text>
      <TimeItemView
        time={minutes}
        unitTime={t("minuteUnit")}
        timeColor={timeColor}
      />
      <Text style={styles.colon}>:</Text>
      <TimeItemView
        time={seconds}
        unitTime={t("secondUnit")}
        timeColor={timeColor}
      />
    </View>
  )
}

const ShortTimeItemView: React.FC<{
  time: string
  unitTime: string
  timeColor: string
}> = ({ time, unitTime, timeColor }) => {
  return (
    <View style={styles.row}>
      <Text style={[styles.shortTime, { color: timeColor }]}>{time}</Text>
      <Text style={[styles.shortTime, { marginEnd: 4 }]}>{unitTime}</Text>
    </View>
  )
}

const ShortTimeView: React.FC<DefaultTimeViewProps> = ({
  days,
  hours,
  minutes,
  seconds,
  timeColor,
}) => {
  const { t } = useTranslation()
  return (
    <View style={styles.timerContainer}>
      <ShortTimeItemView time={days} unitTime={t("d")} timeColor={timeColor} />
      <ShortTimeItemView time={hours} unitTime={t("h")} timeColor={timeColor} />
      <ShortTimeItemView
        time={minutes}
        unitTime={t("m")}
        timeColor={timeColor}
      />
      <ShortTimeItemView
        time={seconds}
        unitTime={t("s")}
        timeColor={timeColor}
      />
    </View>
  )
}

const BrikTimeView: React.FC<DefaultTimeViewProps> = ({
  days,
  hours,
  minutes,
  seconds,
  timeColor,
}) => {
  const { t } = useTranslation()
  return (
    <View style={styles.timerContainer}>
      <BrikTimeItemView
        time={days}
        unitTime={t("day(s)")}
        timeColor={timeColor}
      />
      <BrikTimeItemView
        time={hours}
        unitTime={t("hour(s)")}
        timeColor={timeColor}
      />
      <BrikTimeItemView
        time={minutes}
        unitTime={t("min(s)")}
        timeColor={timeColor}
      />
      <BrikTimeItemView
        time={seconds}
        unitTime={t("sec(s)")}
        timeColor={timeColor}
      />
    </View>
  )
}

const TextTimeView: React.FC<{
  timeLeftText: string
  style?: ViewStyle
}> = ({ timeLeftText, style }) => {
  return (
    <View style={style}>
      <Text style={styles.timeLeftText}>{timeLeftText}</Text>
    </View>
  )
}

interface CustomCountDownTimerProps {
  duration: number
  title?: string
  activeTimeColor?: string
  style?: ViewStyle
  timeStyle?: TextStyle
  type?: TIME_TYPE
  isEnded?: boolean
}

const CustomCountDownTimer: React.FC<CustomCountDownTimerProps> = ({
  type = TIME_TYPE.DEFAULT,
  duration,
  title,
  activeTimeColor = textColors.textBlack,
  style,
  isEnded = false,
}) => {
  const { t } = useTranslation()
  const [timeLeft, setTimeLeft] = useState(duration)

  useEffect(() => {
    if (isEnded || timeLeft <= 0) return

    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer)
          return 0
        }
        return prevTime - 1
      })
    }, MILISECONDS_PER_SECOND)

    return () => clearInterval(timer)
  }, [timeLeft, isEnded])

  const { days, hours, minutes, seconds } = getTimePartsFromDuration(
    timeLeft > 0 ? timeLeft : 0
  )

  const isTimeUp = isEnded || timeLeft <= 0

  const getTimeColor = () => {
    if (isTimeUp) {
      return Colors.red
    } else {
      return activeTimeColor
    }
  }

  const getTimeLeftText = () => {
    if (isTimeUp) {
      return t("Ended")
    } else {
      const timeLeftText = getTimeLeftInHHMMSS(timeLeft)
      return timeLeftText
    }
  }

  if (type === TIME_TYPE.TEXT) {
    return <TextTimeView timeLeftText={getTimeLeftText()} style={style} />
  }

  return (
    <View style={{ ...styles.container, ...style }}>
      {title && <Text style={styles.title}>{title}</Text>}
      {type === TIME_TYPE.DEFAULT && (
        <DefaultTimeView
          days={days}
          hours={hours}
          minutes={minutes}
          seconds={seconds}
          timeColor={getTimeColor()}
        />
      )}
      {type === TIME_TYPE.BRIK && (
        <BrikTimeView
          days={days}
          hours={hours}
          minutes={minutes}
          seconds={seconds}
          timeColor={getTimeColor()}
        />
      )}
      {type === TIME_TYPE.SHORT && (
        <ShortTimeView
          days={days}
          hours={hours}
          minutes={minutes}
          seconds={seconds}
          timeColor={getTimeColor()}
        />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 12,
    paddingVertical: 12,
    paddingStart: 12,
    justifyContent: "center",
    alignItems: "flex-start",
    backgroundColor: Colors.black3,
    borderRadius: 8,
  },
  brikItem: {
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
    paddingVertical: 8,
    borderColor: Colors.black4,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  title: {
    ...textStyles.titleS,
    marginTop: 4,
    marginBottom: 12,
  },
  timerContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  time: {
    ...textStyles.titleL,
    marginEnd: 4,
  },
  shortTime: {
    ...textStyles.bodyM,
    color: textColors.textBlack11,
  },
  colon: {
    ...textStyles.titleL,
    marginEnd: 8,
    marginStart: 4,
    fontWeight: "bold",
  },
  timeNote: {
    ...textStyles.labelM,
    color: Colors.black7,
  },
  timeLeftText: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
})

export { CustomCountDownTimer }
