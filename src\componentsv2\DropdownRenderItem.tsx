import React from "react"
import { Text, StyleSheet } from "react-native"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"

interface DropdownItem {
  label: string
  value: string
}

const DropdownRenderItem = (item: DropdownItem) => (
  <Text style={styles.renderDropdownItem}>{item.label}</Text>
)

const styles = StyleSheet.create({
  renderDropdownItem: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
    backgroundColor: Colors.PalleteBlack,
    alignItems: "center",
    paddingVertical: 8,
    paddingStart: 12,
  },
})

export default DropdownRenderItem
