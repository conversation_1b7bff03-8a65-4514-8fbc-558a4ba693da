import React, { useContext } from "react"
import { Image, StyleSheet, View } from "react-native"
import { PrimaryButton } from "./Button"
import { CustomPressable } from "./CustomPressable"
import Colors from "src/config/colors"
import logoIcon from "assets/imagesV2/ic_logo.png"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import { useNavigation } from "@react-navigation/native"
import { StackNavigationProp } from "@react-navigation/stack"
import { RootStackParamList } from "src/navigatorV2/rootStackParamsList"
import icCirclePlus from "assets/imagesV2/ic_circle_plus.png"
import * as Routes from "src/navigatorV2/routes/RoutesV2"
import { AuthContext } from "src/context/AuthContext"

export const MainTabHeader: React.FC = () => {
  const { t } = useTranslation()
  const authContext = useContext(AuthContext)
  const isAuthenticated = authContext.isAuthenticated
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>()

  const handleTokenize = () => {
    navigation.navigate(Routes.CREATE_NFT)
  }

  return (
    <View style={styles.container}>
      <CustomPressable>
        <Image source={logoIcon} style={styles.logo} />
      </CustomPressable>

      {isAuthenticated && (
        <PrimaryButton
          title={t("Tokenize")}
          onPress={handleTokenize}
          height={28}
          borderRadius={14}
          style={styles.tokenizeButton}
          icon={<Image source={icCirclePlus} style={viewStyles.size12Icon} />}
          textStyle={textStyles.SMedium}
        />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    height: 48,
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: Colors.blackNew,
    paddingHorizontal: 16,
  },
  logo: {
    width: 17,
    height: 20,
  },
  tokenizeButton: {
    marginRight: 4,
  },
})
