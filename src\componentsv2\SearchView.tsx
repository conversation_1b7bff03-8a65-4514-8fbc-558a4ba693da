import React from "react"
import { Image, StyleSheet, TextInput, View, ViewStyle } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { InputModeOptions } from "react-native/Libraries/Components/TextInput/TextInput"
import { ErrorLabel } from "./ErrorLabel"
import searchIcon from "assets/images/ic_search.png"

interface SearchViewProps {
  label?: string
  value: string | number
  onChangeText: (text: string | number) => void
  placeholder?: string
  multiline?: boolean
  height?: number
  inputMode?: InputModeOptions
  style?: ViewStyle
  onBlur?: () => void
  error?: string
  type?: "string" | "number"
}

const SearchView: React.FC<SearchViewProps> = ({
  value,
  onChangeText,
  placeholder,
  multiline = false,
  height,
  inputMode = "text",
  style,
  onBlur,
  error,
  type = "string",
}) => {
  const handleChangeText = (text: string) => {
    if (type === "number" && !Number.isNaN(Number(text))) {
      onChangeText(Number(text))
    } else {
      onChangeText(text)
    }
  }

  return (
    <View style={style}>
      <View style={styles.inputContainer}>
        <TextInput
          onBlur={onBlur}
          value={value.toString()}
          onChangeText={handleChangeText}
          placeholder={placeholder}
          multiline={multiline}
          inputMode={inputMode}
          style={[styles.input, { height }]}
          placeholderTextColor={Colors.Neutral700}
        />
        <Image
          source={searchIcon}
          style={[viewStyles.size12Icon, { tintColor: Colors.Neutral300 }]}
        />
      </View>
      <ErrorLabel error={error} />
    </View>
  )
}

const styles = StyleSheet.create({
  label: {
    marginBottom: 6,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    borderRadius: 6,
    borderWidth: 1,
    padding: 12,
    borderColor: Colors.Neutral900,
  },
  input: {
    flex: 1,
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
})

export { SearchView }
