import React from "react"
import {
  Image,
  Pressable,
  StyleProp,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from "react-native"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import upIcon from "assets/imagesV2/ic_chevron_up.png"
import downIcon from "assets/imagesV2/ic_chevron_down.png"

const SectionHeader: React.FC<{
  style?: StyleProp<ViewStyle>
  icon: React.ReactNode
  title: string
  isOpen: boolean
  onPress: () => void
}> = ({ style = styles.sectionHeader, icon, title, isOpen, onPress }) => (
  <Pressable style={styles.pressable} onPress={onPress}>
    <View style={style}>
      {icon}
      <Text style={styles.sectionTitle}>{title}</Text>
      <Image
        source={isOpen ? upIcon : downIcon}
        style={viewStyles.size20Icon}
      />
    </View>
  </Pressable>
)

export { SectionHeader }

const styles = StyleSheet.create({
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  sectionTitle: {
    ...textStyles.XLMedium,
    color: Colors.PalleteWhite,
    flex: 1,
  },
  pressable: {
    width: "100%",
  },
})
