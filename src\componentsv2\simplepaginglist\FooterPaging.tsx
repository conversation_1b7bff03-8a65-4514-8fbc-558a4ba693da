import React from "react"
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Image,
} from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import icChevronRight from "assets/imagesV2/ic_chevron_right.png"
import icChevronLeft from "assets/imagesV2/ic_chevron_left.png"
import { IconButton } from "../Button"

interface FooterPagingProps {
  itemsOnPage: number
  currentPage: number
  total: number
  totalPages: number
  onPrevPage: () => void
  onNextPage: () => void
}
const FooterPaging: React.FC<FooterPagingProps> = ({
  itemsOnPage,
  currentPage,
  total,
  totalPages,
  onPrevPage,
  onNextPage,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.footer}>
      <View style={styles.selectedRowsTextContainer}>
        <Text style={[styles.selectedRowsText, textStyles.SMedium]}>
          {t("selectedRowsPlaceholder", {
            count: itemsOnPage,
            total: total,
          })}
        </Text>
      </View>
      <View style={styles.wrapper}>
        <IconButton
          style={styles.leftButton}
          onPress={onPrevPage}
          enabled={currentPage > 1}
          icon={
            <Image
              source={icChevronLeft}
              style={styles.buttonIconContainer}
              tintColor={
                currentPage > 1 ? Colors.PalleteWhite : Colors.Neutral400
              }
            />
          }
        />
        <View style={styles.pageTextContainer}>
          <Text style={[styles.pageText, textStyles.SMedium]}>
            {t("pageInfoPlaceholder", {
              current: currentPage,
              total: totalPages,
            })}
          </Text>
        </View>
        <IconButton
          style={styles.rightButton}
          onPress={onNextPage}
          enabled={currentPage < totalPages}
          icon={
            <Image
              source={icChevronRight}
              style={styles.buttonIconContainer}
              tintColor={
                currentPage < totalPages
                  ? Colors.PalleteWhite
                  : Colors.Neutral400
              }
            />
          }
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  footer: {
    flexDirection: "row",
    alignItems: "center",
    padding: 0,
    gap: 2,
    height: 32,
    marginTop: 16,
  } as ViewStyle,

  selectedRowsTextContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    padding: 0,
    gap: 10,
    width: 114,
    height: 12,
  } as ViewStyle,

  selectedRowsText: {
    width: 114,
    height: 12,
    letterSpacing: -0.4,
    color: Colors.Neutral400,
  } as TextStyle,

  wrapper: {
    flexDirection: "row",
    justifyContent: "flex-end",
    alignItems: "center",
    padding: 0,
    height: 28,
    flexGrow: 1,
  } as ViewStyle,

  leftButton: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 8,
    gap: 10,
    width: 28,
    height: 28,
    borderWidth: 1,
    borderColor: Colors.Neutral800,
    borderRadius: 6,
  } as ViewStyle,

  rightButton: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 8,
    gap: 10,
    width: 28,
    height: 28,
    borderWidth: 1,
    borderColor: Colors.Neutral800,
    borderRadius: 6,
  } as ViewStyle,

  buttonIconContainer: {
    width: 16,
    height: 16,
  },

  vectorIcon: {
    position: "absolute",
    left: "37.5%",
    right: "37.5%",
    top: "25%",
    bottom: "25%",
    borderWidth: 1,
    borderColor: Colors.PalleteWhite,
  } as ViewStyle,

  pageTextContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    padding: 0,
    gap: 10,
    width: 80,
    height: 20,
  } as ViewStyle,

  pageText: {
    width: 54,
    height: 12,
    letterSpacing: -0.4,
    color: Colors.PalleteWhite,
  } as TextStyle,
})

export default FooterPaging
