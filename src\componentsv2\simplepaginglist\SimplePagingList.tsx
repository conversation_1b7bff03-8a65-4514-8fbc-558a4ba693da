import { useQuery } from "@tanstack/react-query"
import React, { useState } from "react"
import { FlatList } from "react-native"
import { ListResponse } from "src/api"
import FooterPaging from "./FooterPaging"
import { EmptyView } from "../../components/common/EmptyView"
import { useIsFocused } from "@react-navigation/native"

interface Props<T> {
  getData: (...args: any[]) => Promise<ListResponse<T>>
  renderItem: (item: T) => React.ReactElement | null
  keyExtractor?: (item: T, index: number) => string
  scrollEnabled?: boolean
  queryKeys?: (string | undefined)[]
  initialPage?: number
  pageSize?: number
  emptyMessage?: string
}

function SimplePagingList<T>({
  getData,
  renderItem,
  keyExtractor,
  scrollEnabled = true,
  queryKeys,
  initialPage = 1,
  pageSize = 10,
  emptyMessage,
}: Props<T>): React.ReactElement {
  const isFocused = useIsFocused()

  const [currentPage, setCurrentPage] = useState<number>(initialPage)
  const params = {
    currentPage: currentPage,
    itemsPerPage: pageSize,
  }

  const {
    data = {
      list: [],
      pagination: {
        itemsPerPage: 10,
        currentPage: 1,
        totalItems: 0,
      },
    },
    isFetching,
  } = useQuery({
    queryKey: [...(queryKeys || []), currentPage, pageSize],
    queryFn: () => getData(params),
    refetchInterval: isFocused ? 10_000 : false,
    enabled: isFocused,
  })

  const { list, pagination } = data

  const handleNextPage = () => {
    const totalPages = Math.ceil(
      pagination.totalItems / pagination.itemsPerPage
    )
    if (currentPage < totalPages) {
      setCurrentPage((prev) => prev + 1)
    }
  }

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1)
    }
  }

  const renderFooter = () => {
    if (list.length === 0) {
      return null
    }
    const totalPages = Math.ceil(
      pagination.totalItems / pagination.itemsPerPage
    )
    return (
      <FooterPaging
        itemsOnPage={list.length}
        currentPage={currentPage}
        total={pagination.totalItems}
        totalPages={totalPages}
        onNextPage={handleNextPage}
        onPrevPage={handlePrevPage}
      />
    )
  }

  if (list.length === 0 && !isFetching) {
    return <EmptyView subtitle={emptyMessage} />
  }

  return (
    <>
      <FlatList
        data={list}
        renderItem={({ item }) => renderItem(item)}
        keyExtractor={keyExtractor}
        scrollEnabled={scrollEnabled}
      />
      {renderFooter()}
    </>
  )
}

export default SimplePagingList
