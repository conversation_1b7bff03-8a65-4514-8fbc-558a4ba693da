export const PROVIDER_CHAIN_ID = parseInt(
  (process.env.EXPO_PUBLIC_PROVIDER_CHAINID ?? 0).toString()
)

export const BASE_WEB_URL = process.env.EXPO_PUBLIC_BASE_WEB_URL || ""

export const APP_SCHEME = process.env.EXPO_PUBLIC_REDIRECT_URL || ""
export const WALLET_CONNECT_PROJECT_ID =
  process.env.EXPO_PUBLIC_WALLET_CONNECT_PROJECT_ID || ""

export const PROVIDER_POLLING_INTERVAL = parseInt(
  (process.env.EXPO_PUBLIC_PROVIDER_POLLING_INTERVAL ?? 5000).toString()
)
export const MAX_UINT256 =
  "115792089237316195423570985008687907853269984665640564039457584007913129639935"

export const ISSUANCE_PRIMARY_TOKEN_BACKER_ROUND = 100_000_000
export const ISSUANCE_PRIMARY_TOKEN_CORE_TEAM = 1_000_000_000
export const ISSUANCE_PRIMARY_TOKEN_EXTERNAL_TREASURY = 1_000_000_000
export const ISSUANCE_PRIMARY_TOKEN_MARKET_MAKER = 2_270_000_000
export const ISSUANCE_PRIMARY_TOKEN_PRIVATE_SALE_1 = 30_000_000
export const ISSUANCE_PRIMARY_TOKEN_PRIVATE_SALE_2 = 50_000_000
export const ISSUANCE_PRIMARY_TOKEN_PUBLIC_SALE = 500_000_000
export const ISSUANCE_PRIMARY_TOKEN_SEED_ROUND = 50_000_000

export const PRIMARY_TOKEN_BACKER_ROUND_ISSUANCE = 100_000_000
export const PRIMARY_TOKEN_SEED_ROUND_ISSUANCE = 50_000_000
export const PRIMARY_TOKEN_PRIVATE_SALE_ISSUANCE = 30_000_000
export const PRIMARY_TOKEN_PRE_ICO_ISSUANCE = 50_000_000
export const PRIMARY_TOKEN_AUCTION_ISSUANCE = 100_000_000
export const PRIMARY_TOKEN_MARKET_MAKER_ISSUANCE = 2_270_000_000
export const PRIMARY_TOKEN_CORE_TEAM_ISSUANCE = 1_000_000_000
export const PRIMARY_TOKEN_EXTERNAL_TREASURY_ISSUANCE = 1_000_000_000
export const PRIMARY_TOKEN_STAKE_DAILY_ISSUANCE = 14_000_000
export const PRIMARY_TOKEN_MAX_SUPPLY = 20_000_000_000

export const LOCAL_STORAGE_KEY_LANGUAGE = "LANGUAGE"
export const EXPO_PUBLIC_BASE_API_URL = process.env.EXPO_PUBLIC_BASE_API_URL
export const EXPO_PUBLIC_BASE_API_V2_URL =
  process.env.EXPO_PUBLIC_BASE_API_V2_URL
export const EXPO_PUBLIC_BASE_IMAGE_URL = process.env.EXPO_PUBLIC_BASE_IMAGE_URL
export const BSCSCAN_URL = process.env.EXPO_PUBLIC_BSCSCAN_URL

export const CONTRACT_ADDRESS_AUCTION_BACKER_ROUND = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_AUCTION_BACKER_ROUND as `0x${string}`
export const CONTRACT_ADDRESS_AUCTION_SEED_ROUND = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_AUCTION_SEED_ROUND as `0x${string}`
export const CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_1 = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_1 as `0x${string}`
export const CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_2 = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_2 as `0x${string}`
export const CONTRACT_ADDRESS_AUCTION_PUBLIC_SALE = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_AUCTION_PUBLIC_SALE as `0x${string}`

export const CONTRACT_ADDRESS_AUCTION_CURRENCY = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_AUCTION_CURRENCY as `0x${string}`
export const CONTRACT_ADDRESS_PRIMARY_TOKEN = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_PRIMARY_TOKEN as `0x${string}`
export const CONTRACT_ADDRESS_TREASURY = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_TREASURY as `0x${string}`
export const CONTRACT_ADDRESS_STAKE_TOKEN = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_STAKE_TOKEN as `0x${string}`
export const CONTRACT_ADDRESS_ESTATE_TOKEN = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_ESTATE_TOKEN as `0x${string}`
export const CONTRACT_ADDRESS_MARKETPLACE = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_MARKETPLACE as `0x${string}`
export const CONTRACT_ADDRESS_GOVERNOR_HUB = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_GOVERNOR_HUB as `0x${string}`
export const CONTRACT_ADDRESS_MORTGAGE_TOKEN = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_MORTGAGE_TOKEN as `0x${string}`
export const CONTRACT_ADDRESS_ESTATE_FORGER = process.env
  .EXPO_PUBLIC_CONTRACT_ADDRESS_ESTATE_FORGER as `0x${string}`
