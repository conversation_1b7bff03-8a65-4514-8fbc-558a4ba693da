import i18n from "i18next"
import { initReactI18next } from "react-i18next"
import english from "locales/en"
import vietnamese from "locales/vi"
import { loadLanguage } from "src/store/LocalAsyncStore"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "i18n" })

const initI18n = async () => {
  try {
    const lng = await loadLanguage()
    await i18n.use(initReactI18next).init({
      lng,
      fallbackLng: "en",
      interpolation: {
        escapeValue: false,
      },
      resources: {
        vi: { translation: vietnamese },
        en: { translation: english },
      },
    })
    logger.info("i18n initialized successfully", { language: lng })
  } catch (error) {
    logger.error("Error initializing i18n", error)
  }
}

initI18n()

export default i18n
