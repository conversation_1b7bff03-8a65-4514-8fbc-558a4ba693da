import React, { useCallback } from "react"
import { LinkingO<PERSON>s, NavigationContainer } from "@react-navigation/native"
import { createDrawerNavigator } from "@react-navigation/drawer"
import { createStackNavigator } from "@react-navigation/stack"
import NavigationBar from "src/components"
import { Router } from "./Router"
import SlideBar from "src/screens/slidebar"
import {
  ContactScreen,
  CreateNFTScreen,
  EstateDetailScreen,
  EstateRequestDetailScreen,
  EstatesScreen,
  InvestmentDetailScreen,
  MarketPlaceOffersScreen,
  MortgageHubLoansScreen,
  MyProfileScreen,
  OfficesScreen,
  ReferencesScreen,
  SelectLanguageScreen,
  SettingsScreen,
  StakingPoolScreen,
  TokenomicsAllocationScreen,
  TreasuryScreen,
  VerifyAccountScreen,
} from "screens"
import HomeScreen from "src/screensV2/home"
import EstateScreen from "../screensV2/estate/EstateScreen"
import { RootStackParamList } from "./rootStackParamsList"

const prefix = "exp://"
const linking: LinkingOptions<RootStackParamList> = {
  prefixes: [prefix],
  config: {
    screens: {
      HomeScreen: "home",
      [Router.Profile]: {
        screens: {
          [Router.MyProfile]: "",
          [Router.VerifyAccount]: "verify",
        },
      },
    },
  },
}

const Drawer = createDrawerNavigator()
const Stack = createStackNavigator<RootStackParamList>()
const Navigator: React.FC = () => {
  return (
    <NavigationContainer linking={linking}>
      <DrawerNavigator />
    </NavigationContainer>
  )
}

const SettingsStack: React.FC<{ screen: React.ComponentType }> = () => {
  const Settings = Router.Settings as keyof RootStackParamList
  return (
    <Stack.Navigator
      initialRouteName={Settings}
      screenOptions={{
        header: () => <NavigationBar />,
      }}
    >
      <Stack.Screen
        name={Settings}
        component={SettingsScreen}
        options={{
          headerShown: true,
        }}
      />
      <Stack.Screen
        name={"SelectLanguage"}
        component={SelectLanguageScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={"References"}
        component={ReferencesScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={"Contact"}
        component={ContactScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={"Offices"}
        component={OfficesScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  )
}

const MarketPlaceStack: React.FC<{ screen: React.ComponentType }> = ({
  screen,
}) => {
  const ListLands = Router.ListLands as keyof RootStackParamList
  return (
    <Stack.Navigator
      initialRouteName={ListLands}
      screenOptions={{
        header: () => <NavigationBar />,
      }}
    >
      <Stack.Screen
        name={ListLands}
        component={screen}
        options={{
          headerShown: true,
        }}
      />
      <Stack.Screen
        name={"EstateRequestDetail"}
        component={EstateRequestDetailScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={"EstateDetail"}
        component={EstateDetailScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={"CreateNFT"}
        component={CreateNFTScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  )
}

const ProfileStack: React.FC = () => {
  const MyProfile = Router.MyProfile as keyof RootStackParamList
  const VerifyAccount = Router.VerifyAccount as keyof RootStackParamList
  return (
    <Stack.Navigator
      initialRouteName={MyProfile}
      screenOptions={{
        header: () => <NavigationBar />,
      }}
    >
      <Stack.Screen
        name={MyProfile}
        component={MyProfileScreen}
        options={{
          headerShown: true,
        }}
      />
      <Stack.Screen
        name={VerifyAccount}
        component={VerifyAccountScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={"EstateRequestDetail"}
        component={EstateRequestDetailScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={"EstateDetail"}
        component={EstateDetailScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  )
}

const InvestmentStack: React.FC = () => {
  const InvestmentDetail = Router.InvestmentDetail as keyof RootStackParamList
  const TokenomicsAllocation =
    Router.TokenomicsAllocation as keyof RootStackParamList

  return (
    <Stack.Navigator
      initialRouteName={InvestmentDetail}
      screenOptions={{
        header: () => <NavigationBar />,
      }}
    >
      <Stack.Screen
        name={InvestmentDetail}
        component={InvestmentDetailScreen}
        options={{
          headerShown: true,
        }}
      />
      <Stack.Screen
        name={TokenomicsAllocation}
        component={TokenomicsAllocationScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  )
}

const DrawerNavigator: React.FC = () => {
  const tokenizationStack = useCallback(() => {
    return <MarketPlaceStack screen={EstateScreen} />
  }, [])

  const estateStack = useCallback(() => {
    return <MarketPlaceStack screen={EstatesScreen} />
  }, [])

  const marketPlaceStack = useCallback(() => {
    return <MarketPlaceStack screen={MarketPlaceOffersScreen} />
  }, [])

  const mortgageStack = useCallback(() => {
    return <MarketPlaceStack screen={MortgageHubLoansScreen} />
  }, [])

  const settingsStack = useCallback(() => {
    return <SettingsStack screen={SettingsScreen} />
  }, [])

  const invesmentStack = useCallback(() => {
    return <InvestmentStack />
  }, [])

  return (
    <Drawer.Navigator
      drawerContent={(props) => <SlideBar {...props} />}
      initialRouteName={Router.HomePath}
      screenOptions={{
        drawerPosition: "right",
        drawerType: "front",
        header: () => <NavigationBar />,
      }}
    >
      <Drawer.Screen name={Router.HomePath} component={HomeScreen} />

      <Drawer.Screen
        name={Router.Profile}
        component={ProfileStack}
        options={{ unmountOnBlur: true, headerShown: false }}
      />
      <Drawer.Screen
        name={Router.Setting}
        component={settingsStack}
        options={{ unmountOnBlur: true, headerShown: false }}
      />
      <Drawer.Screen
        name={Router.Tokenization}
        component={tokenizationStack}
        options={{ unmountOnBlur: true, headerShown: false }}
      />
      <Drawer.Screen
        name={Router.Estate}
        component={estateStack}
        options={{ unmountOnBlur: true, headerShown: false }}
      />
      <Drawer.Screen
        name={Router.MarketPlace}
        component={marketPlaceStack}
        options={{ unmountOnBlur: true, headerShown: false }}
      />
      <Drawer.Screen
        name={Router.MortgageHub}
        component={mortgageStack}
        options={{ unmountOnBlur: true, headerShown: false }}
      />
      <Drawer.Screen
        name={Router.Investment}
        component={invesmentStack}
        options={{ unmountOnBlur: true, headerShown: false }}
      />
      <Drawer.Screen
        name={Router.StakingPool}
        component={StakingPoolScreen}
        options={{ unmountOnBlur: true }}
      />
      <Drawer.Screen
        name={Router.Treasury}
        component={TreasuryScreen}
        options={{ unmountOnBlur: true }}
      />
    </Drawer.Navigator>
  )
}

export default Navigator
