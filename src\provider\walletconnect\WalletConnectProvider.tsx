import "@walletconnect/react-native-compat"
import { WagmiProvider } from "wagmi"
import React from "react"
import {
  APP_SCHEME,
  PROVIDER_CHAIN_ID,
  PROVIDER_POLLING_INTERVAL,
  WALLET_CONNECT_PROJECT_ID,
} from "src/config/env"
import Constants from "expo-constants"
import { defineChain, http } from "viem"
import {
  arbitrum,
  base,
  baseSepolia,
  bsc,
  bscTestnet,
  goerli,
  linea,
  mainnet,
  polygon,
} from "wagmi/chains"
import { defaultWagmiConfig } from "@reown/appkit-wagmi-react-native"

const projectId = WALLET_CONNECT_PROJECT_ID

export const bscTestnetChain = defineChain({
  ...bscTestnet,
  rpcUrls: {
    default: {
      http: [
        "https://data-seed-prebsc-1-s1.bnbchain.org:8545",
        "https://bsc-testnet-dataseed.bnbchain.org",
        "https://bsc-testnet.bnbchain.org",
        "https://bsc-prebsc-dataseed.bnbchain.org",
        ...bscTestnet.rpcUrls.default.http,
      ],
    },
  },
})

export const bscMainnetChain = defineChain({
  ...bsc,
  rpcUrls: {
    default: {
      http: [
        "https://rpc.ankr.com/bsc",
        "https://bsc-dataseed.bnbchain.org",
        "https://bsc-dataseed1.bnbchain.org",
        "https://bsc-dataseed2.bnbchain.org",
        "https://bsc-dataseed3.bnbchain.org",
        "https://bsc-dataseed4.bnbchain.org",
        "https://bsc-dataseed.binance.org",
        "https://bsc-dataseed1.defibit.io",
        "https://bsc-dataseed1.ninicoin.io",
        "https://bsc-dataseed1.binance.org",
        ...bsc.rpcUrls.default.http,
      ],
    },
  },
})

export const appChains = [
  bscTestnetChain,
  bscMainnetChain,
  mainnet,
  goerli,
  linea,
  arbitrum,
  baseSepolia,
  polygon,
  base,
] as const

export const activeChain =
  appChains.find((chain) => chain.id === PROVIDER_CHAIN_ID) || bscTestnetChain

const metadata = {
  name: "Briky Land",
  description:
    "Briky Land - The First Blockchain-Based Real Estate Metaverse Platform",
  url: "https://brikyland.com/",
  icons: ["https://brikyland.com/logo_briki.png"],
  redirect: {
    native: Constants.executionEnvironment
      ? `exp://${Constants?.expoConfig?.hostUri}`
      : APP_SCHEME,
    // TODO: Add universal link
    universal: "YOUR_APP_UNIVERSAL_LINK.com",
  },
}

export const wagmiConfig = defaultWagmiConfig({
  chains: appChains,
  projectId,
  metadata,
  transports: {
    [bscTestnetChain.id]: http(),
    [bscMainnetChain.id]: http(),
  },
  pollingInterval: PROVIDER_POLLING_INTERVAL,
})

export const WalletConnectProvider: React.FC<{
  children: React.ReactNode
}> = ({ children }) => {
  return (
    <>
      <WagmiProvider config={wagmiConfig}>{children}</WagmiProvider>
    </>
  )
}
