import React from "react"
import { BasicSectionItem } from "../../types"
import { BasicSectionEstateView } from "src/screensV2/shared/components"

interface BasicSectionProps {
  item: BasicSectionItem
}

const BasicSection: React.FC<BasicSectionProps> = ({ item }) => {
  const { application } = item

  const {
    metadata: {
      estatePhotoUrls: images,
      metadata: { name, address, area, locale_detail },
    },
    requester: { avatarUrl: sellerAvatar, address: sellerAddress },
    createdAtInSeconds,
    totalSupply,
    decimals,
  } = application

  return (
    <BasicSectionEstateView
      images={images}
      name={name}
      sellerAvatar={sellerAvatar}
      sellerAddress={sellerAddress}
      createdAt={createdAtInSeconds * 1000}
      address={address}
      totalSupply={totalSupply}
      area={area}
      decimals={decimals}
      locale_detail={locale_detail}
    />
  )
}

export default BasicSection
