import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { LegalSectionItem } from "../../types"
import { CardView } from "src/components"
import { useTranslation } from "react-i18next"

interface LegalSectionProps {
  item: LegalSectionItem
}

const LegalSection: React.FC<LegalSectionProps> = ({ item }) => {
  const { t } = useTranslation()
  const { application } = item

  return (
    <CardView style={styles.container}>
      <View style={styles.contentContainer}>
        <Text style={styles.sectionTitle}>{t("LEGAL")}</Text>
        <Text style={styles.credentialId}>
          {t("Credential ID")}: {application.metadata.metadata.credential_id}
        </Text>
        <Text style={styles.credentialType}>
          {t("Credential Type")}:{" "}
          {application.metadata.metadata.credential_type}
        </Text>
      </View>
    </CardView>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  contentContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  credentialId: {
    fontSize: 14,
    marginBottom: 4,
  },
  credentialType: {
    fontSize: 14,
  },
})

export default LegalSection
