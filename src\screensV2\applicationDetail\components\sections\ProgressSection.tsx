import React from "react"
import { ProgressSectionItem } from "../../types"
import { ProgressSectionView } from "src/screensV2/shared/components"

interface ProgressSectionProps {
  item: ProgressSectionItem
}

const ProgressSection: React.FC<ProgressSectionProps> = ({ item }) => {
  const { application } = item
  const { totalSupply, minSellingAmount, maxSellingAmount, state, decimals } =
    application

  return (
    <ProgressSectionView
      isApplicationDetail={true}
      totalSupply={totalSupply}
      minSellingAmount={minSellingAmount}
      maxSellingAmount={maxSellingAmount}
      soldAmount={"0"}
      publicSaleEndsAtInSeconds={0}
      state={state}
      decimals={decimals}
    />
  )
}

export default ProgressSection
