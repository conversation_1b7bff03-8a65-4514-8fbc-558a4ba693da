import React, { createContext, useContext, ReactNode } from "react"
import { ApplicationDetailContextState } from "./types"
import { Application, Currency } from "src/api/types"
import { useApplicationDetailProvider } from "../hooks/useApplicationDetailProvider"

// Create the context with default values
const ApplicationDetailContext = createContext<ApplicationDetailContextState>({
  // Core data
  applicationId: "",
  applicationDetail: null,

  // Related data
  currencies: [],

  // Loading states
  isLoadingApplicationDetail: false,
  isLoadingCurrencies: false,

  // Error states
  applicationDetailError: null,
  currenciesError: null,

  // Actions
  refreshAll: async () => {},
  refreshApplicationDetail: async () => {},
  refreshCurrencies: async () => {},
})

// Hook to use the context
export const useApplicationDetailContext = () =>
  useContext(ApplicationDetailContext)

// Provider props
interface ApplicationDetailProviderProps {
  children: ReactNode
  applicationId: string

  // Initial data (optional, for testing or SSR)
  initialApplicationDetail?: Application | null
  initialCurrencies?: Currency[]
}

// Provider component
export const ApplicationDetailProvider: React.FC<
  ApplicationDetailProviderProps
> = ({
  children,
  applicationId,
  initialApplicationDetail,
  initialCurrencies,
}) => {
  // Use the custom hook to get the context value
  const contextValue = useApplicationDetailProvider(
    applicationId,
    initialApplicationDetail,
    initialCurrencies
  )

  return (
    <ApplicationDetailContext.Provider value={contextValue}>
      {children}
    </ApplicationDetailContext.Provider>
  )
}

export default ApplicationDetailContext
