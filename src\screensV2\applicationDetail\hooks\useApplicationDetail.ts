import { useQueryWithErrorHandling } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import { getApplicationDetailById, Application } from "src/api"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "useApplicationDetail" })

/**
 * Hook to fetch application details by ID
 * @param applicationId The ID of the application to fetch
 */
export const useApplicationDetail = (applicationId: string) => {
  logger.debug("Fetching application detail", { applicationId })

  return useQueryWithErrorHandling<Application>({
    queryKey: QueryKeys.PROFILE.APPLICATION_DETAIL(applicationId),
    queryFn: () => getApplicationDetailById(applicationId),
    refetchInterval: 10_000,
    refetchOnMount: true,
    refetchIntervalInBackground: true,
  })
}
