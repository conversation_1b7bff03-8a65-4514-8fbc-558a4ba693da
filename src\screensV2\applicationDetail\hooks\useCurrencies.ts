import { useQueryWithErrorHandling } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import { getCurrencies, Currency } from "src/api"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "useCurrencies" })

/**
 * Hook to fetch available currencies
 */
export const useCurrencies = () => {
  logger.debug("Fetching currencies")

  return useQueryWithErrorHandling<Currency[]>({
    queryKey: QueryKeys.CURRENCY.LIST,
    queryFn: () => getCurrencies(),
  })
}
