import React, { useEffect, useCallback, useState } from "react"
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  Image,
  View,
} from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import { useAtom } from "jotai"
import { profileAtom } from "src/context/AuthContext"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import Colors from "src/config/colors"
import Logger from "src/utils/logger"
import { useAccount } from "wagmi"
import { PrimaryButton } from "components"
import { useCreateNFTForm, useCreateNFTMutation, useUserSearch } from "./hooks"
import {
  PropertyDetailsSection,
  TokenEconomicSection,
  PropertyImageSection,
  UserInfoSection,
} from "./components"
import { durationUnitMap } from "src/utils/timeExt"
import verifiedIcon from "assets/imagesV2/ic_circle_check.png"
import notVerifiedIcon from "assets/imagesV2/ic_circle_alert.png"
import verifiedCloseIcon from "assets/imagesV2/ic_x_btn.png"
import * as Routes from "src/navigatorV2/routes/RoutesV2"
import { CustomPressable } from "src/componentsv2"

const logger = new Logger({ tag: "CreateNFTScreen" })

const CreateNFTScreen: React.FC = () => {
  const { t } = useTranslation()
  const [profile] = useAtom(profileAtom)
  const { address } = useAccount()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const [isVerifiedBlockVisible, setIsVerifiedBlockVisible] = useState(true)

  // Kiểm tra trạng thái người dùng
  const isVerified = profile?.status === "VERIFIED"
  const isUnverified = profile?.status === "UNVERIFIED"
  const isCurrentUserAdmin = profile?.isManager || profile?.isModerator
  const isCurrentUserJustBroker = profile?.isBroker && !isCurrentUserAdmin
  const isCurrentUserJustSeller =
    !isCurrentUserJustBroker && !isCurrentUserAdmin

  // Khởi tạo form với các hook mới
  const { form, currencies, handleLocationChange, updateFormValidation } =
    useCreateNFTForm({
      isCurrentUserJustSeller,
    })

  // Các giá trị theo dõi từ form
  const [
    addressCodeLevel1,
    addressCodeLevel2,
    addressCodeLevel3,
    unitPrice,
    currencyId,
    isInfinite,
    totalSupply,
    decimals,
    sellingLimit,
  ] = form.watch([
    "addressCodeLevel1",
    "addressCodeLevel2",
    "addressCodeLevel3",
    "unitPrice",
    "currencyId",
    "isInfinite",
    "totalSupply",
    "decimals",
    "sellingLimit",
  ])

  const minSellingAmount = sellingLimit?.minSellingAmount || 0
  const maxSellingAmount = sellingLimit?.maxSellingAmount || 0

  // Lấy thông tin currency
  const selectedCurrency = currencies.find((c) => c.currency === currencyId)
  const currenSymbol = selectedCurrency?.symbol

  // Cập nhật validation dựa trên các giá trị hiện tại
  useEffect(() => {
    const minUnitPrice = selectedCurrency?.minUnitPrice || "0"
    const maxUnitPrice = selectedCurrency?.maxUnitPrice || "0"

    updateFormValidation(
      totalSupply,
      minUnitPrice,
      maxUnitPrice,
      decimals,
      isInfinite,
      minSellingAmount,
      maxSellingAmount
    )
  }, [
    selectedCurrency,
    totalSupply,
    decimals,
    isInfinite,
    updateFormValidation,
    minSellingAmount,
    maxSellingAmount,
  ])

  // Sử dụng hook tìm kiếm người dùng
  const {
    isLoading: isSearching,
    brokerInfo,
    searchBrokerWarning,
    sellerInfo,
    searchSellerWarning,
    onChangeSearchUser,
  } = useUserSearch(profile)

  // Sử dụng hook tạo NFT
  const { isLoading: isSubmitting, onSubmit } = useCreateNFTMutation(
    navigation,
    address || undefined
  )

  // Tổng hợp trạng thái loading
  const isLoading = isSearching || isSubmitting

  // Chuẩn bị đối tượng location cho AreaPicker
  const location = {
    addressCodeLevel1,
    addressCodeLevel2,
    addressCodeLevel3,
  }

  // Log lỗi form
  Object.keys(form.formState.errors).forEach((error) => {
    logger.error("Form state error", error)
  })

  const handleSubmit = useCallback(
    form.handleSubmit((values) => {
      onSubmit(values)
    }),
    [onSubmit, form]
  )

  return (
    <ScrollView
      style={styles.scrollView}
      showsHorizontalScrollIndicator={false}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.container}>
          <Text style={styles.screenTitle}>
            {t("Request for real estate tokenization")}
          </Text>

          {isVerified && isVerifiedBlockVisible && (
            <View style={styles.verifiedContainer}>
              <Image source={verifiedIcon} style={styles.verifiedIcon} />
              <Text style={styles.verifiedText}>
                {t(
                  "Your account has been verified. You can create a tokenization request now"
                )}
              </Text>
              <CustomPressable onPress={() => setIsVerifiedBlockVisible(false)}>
                <Image
                  source={verifiedCloseIcon}
                  style={[
                    viewStyles.size20Icon,
                    { tintColor: Colors.PalleteWhite },
                  ]}
                />
              </CustomPressable>
            </View>
          )}

          {isUnverified && (
            <View style={styles.notVerifiedContainer}>
              <Image source={notVerifiedIcon} style={styles.notVerifiedIcon} />
              <View>
                <Text style={styles.notVerifiedTitle}>{t("Warning")}</Text>
                <Text style={styles.notVerifiedText}>
                  {t("You need to verify your account to start creating NFT.")}
                </Text>
                <PrimaryButton
                  title={t("Verify now")}
                  onPress={() => navigation.navigate(Routes.VERIFY_ACCOUNT)}
                  style={styles.verifyButton}
                  textStyle={styles.verifyButtonText}
                />
              </View>
            </View>
          )}

          <View style={styles.contentContainer}>
            <PropertyDetailsSection
              control={form.control}
              formState={form.formState}
              isInfinite={isInfinite}
              location={location}
              handleLocationChange={handleLocationChange}
            />

            <TokenEconomicSection
              control={form.control}
              formState={form.formState}
              unitPrice={unitPrice.toString()}
              totalSupply={totalSupply.toString()}
              currenSymbol={currenSymbol}
              currencies={currencies}
              durationUnitMap={durationUnitMap}
              setValue={form.setValue}
            />

            <PropertyImageSection
              control={form.control}
              formState={form.formState}
            />

            {(isCurrentUserAdmin || isCurrentUserJustBroker) && (
              <UserInfoSection
                control={form.control}
                formState={form.formState}
                type="seller"
                searchWarning={searchSellerWarning}
                userInfo={sellerInfo}
                onChangeSearchUser={onChangeSearchUser}
              />
            )}

            {(isCurrentUserAdmin || isCurrentUserJustSeller) && (
              <UserInfoSection
                control={form.control}
                formState={form.formState}
                type="broker"
                searchWarning={searchBrokerWarning}
                userInfo={brokerInfo}
                onChangeSearchUser={onChangeSearchUser}
              />
            )}

            <PrimaryButton
              title={
                isVerified
                  ? t("Submit Tokenization Request")
                  : t("Not Verified")
              }
              onPress={handleSubmit}
              style={styles.submitButton}
              textStyle={styles.submitButtonText}
              isLoading={isLoading}
              enabled={isVerified}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    backgroundColor: Colors.blackNew,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 50,
  },
  screenTitle: {
    ...textStyles.XLBold,
    color: Colors.PalleteWhite,
    marginTop: 32,
    marginBottom: 12,
  },
  verifiedContainer: {
    width: "100%",
    borderRadius: 8,
    borderWidth: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    gap: 6,
    backgroundColor: Colors.Success900,
    borderColor: Colors.Success800,
    flexDirection: "row",
    alignItems: "center",
  },
  verifiedIcon: {
    ...viewStyles.size20Icon,
    tintColor: Colors.Success500,
  },
  verifiedText: {
    ...textStyles.SMedium,
    color: Colors.PalleteWhite,
    flex: 1,
  },
  notVerifiedContainer: {
    width: "100%",
    borderRadius: 8,
    borderWidth: 1.2,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: Colors.Primary900,
    borderColor: Colors.Warning800,
    gap: 8,
    flexDirection: "row",
  },
  notVerifiedIcon: {
    ...viewStyles.size20Icon,
    tintColor: Colors.Warning500,
  },
  notVerifiedTitle: {
    ...textStyles.LSemiBold,
    color: Colors.PalleteWhite,
  },
  notVerifiedText: {
    ...textStyles.SMedium,
    color: Colors.PalleteWhite,
    marginVertical: 8,
  },
  verifyButton: {
    borderRadius: 4,
  },
  verifyButtonText: {
    ...textStyles.MMedium,
    color: Colors.PalleteBlack,
  },
  contentContainer: {
    gap: 24,
    marginTop: 32,
    marginBottom: 24,
  },
  submitButton: {
    width: "100%",
    height: 32,
    borderRadius: 6,
  },
  submitButtonText: {
    ...textStyles.LMedium,
    color: Colors.PalleteBlack,
  },
})

export default CreateNFTScreen
