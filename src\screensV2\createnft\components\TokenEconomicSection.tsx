import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import { Controller, Control } from "react-hook-form"
import { Dropdown } from "react-native-element-dropdown"
import Colors from "src/config/colors"
import { formatMoney } from "utils"
import { InputField } from "src/componentsv2/InputField"
import { LabelView } from "src/componentsv2"
import { SelectCurrency } from "src/componentsv2/SelectCurrency"
import DropdownRenderItem from "src/componentsv2/DropdownRenderItem"

interface TokenEconomicSectionProps {
  control: Control<any>
  formState: any
  unitPrice: string
  totalSupply: string
  currenSymbol?: string
  currencies: any[]
  durationUnitMap: Record<string, any>
  setValue: any
}

const TokenEconomicSection: React.FC<TokenEconomicSectionProps> = ({
  control,
  formState,
  unitPrice,
  totalSupply,
  currenSymbol,
  currencies,
  durationUnitMap,
  setValue,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{t("Token Economic")}</Text>

      <LabelView label={t("Denominations")} require={true} />
      <View style={styles.rowNoTopMargin}>
        <Controller
          control={control}
          name="unitPrice"
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={styles.flex1}>
              <InputField
                require={true}
                value={value}
                inputMode={"numeric"}
                type={"number"}
                onChangeText={onChange}
                onBlur={onBlur}
                error={formState.errors.unitPrice?.message}
                placeholder="0"
              />
            </View>
          )}
        />
        <SelectCurrency
          currencies={currencies}
          control={control}
          isShowMinMaxPrice={true}
          setValue={setValue}
        />
      </View>

      <View style={styles.rowNoTopMargin}>
        <Controller
          control={control}
          name="totalSupply"
          render={({ field: { onChange, value } }) => (
            <View style={styles.flex1}>
              <InputField
                label={t("Total supply")}
                value={value}
                require={true}
                type={"number"}
                inputMode={"decimal"}
                onChangeText={onChange}
                style={styles.marginTopDefault}
                error={formState.errors.totalSupply?.message}
                placeholder="0"
              />
            </View>
          )}
        />
        <Controller
          control={control}
          name="decimals"
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={styles.flex1}>
              <InputField
                label={t("Decimal (maximum 18)")}
                require={true}
                value={value}
                type={"number"}
                inputMode={"numeric"}
                onChangeText={onChange}
                onBlur={onBlur}
                style={styles.marginTopDefault}
                error={formState.errors.decimals?.message}
                placeholder="0"
              />
            </View>
          )}
        />
      </View>

      {!isNaN(+unitPrice) && !isNaN(+totalSupply) && (
        <Text style={styles.propertyValue}>
          {t("Property value")}: {formatMoney(+unitPrice * +totalSupply)}{" "}
          {currenSymbol}
        </Text>
      )}

      <View style={styles.rowNoTopMargin}>
        <Controller
          control={control}
          name="sellingLimit.minSellingAmount"
          render={({ field: { onChange, value } }) => (
            <View style={styles.flex1}>
              <InputField
                value={value}
                require={true}
                label={t("Minimum selling amount")}
                type={"number"}
                inputMode={"decimal"}
                onChangeText={onChange}
                error={formState.errors.sellingLimit?.minSellingAmount?.message}
                placeholder="0"
              />
            </View>
          )}
        />
        <Controller
          control={control}
          name="sellingLimit.maxSellingAmount"
          render={({ field: { onChange, value } }) => (
            <View style={styles.flex1}>
              <InputField
                value={value}
                require={true}
                label={t("Maximum selling amount")}
                type={"number"}
                inputMode={"decimal"}
                onChangeText={onChange}
                error={formState.errors.sellingLimit?.maxSellingAmount?.message}
                placeholder="0"
              />
            </View>
          )}
        />
      </View>

      <LabelView label={t("Public Sale Duration")} require={true} />
      <View style={styles.rowNoTopMargin}>
        <Controller
          control={control}
          name="duration"
          render={({ field: { onChange, value } }) => (
            <View style={styles.flex1}>
              <InputField
                value={value}
                require={true}
                type={"number"}
                inputMode={"decimal"}
                onChangeText={onChange}
                error={formState.errors.duration?.message}
                placeholder="0"
              />
            </View>
          )}
        />
        <Controller
          control={control}
          name="durationUnit"
          render={({ field: { onChange, value } }) => (
            <Dropdown
              value={value}
              data={Object.keys(durationUnitMap).map((unit) => ({
                label: t(unit),
                value: unit,
              }))}
              labelField={"label"}
              valueField={"value"}
              onChange={({ value }) => onChange(value)}
              style={styles.durationDropdown}
              selectedTextStyle={styles.dropdownItem}
              placeholderStyle={styles.dropdownItem}
              itemTextStyle={styles.dropdownItem}
              renderItem={DropdownRenderItem}
            />
          )}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  section: {
    gap: 8,
  },
  sectionTitle: {
    ...textStyles.MBold,
    color: Colors.PalleteWhite,
  },
  marginTopDefault: {
    marginTop: 8,
  },
  marginTopSmall: {
    marginTop: 6,
  },
  row: {
    flexDirection: "row",
    gap: 8,
    marginTop: 8,
  },
  rowNoTopMargin: {
    flexDirection: "row",
    gap: 8,
  },
  flex1: {
    flex: 1,
  },
  durationDropdown: {
    height: 38,
    width: "30%",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignItems: "center",
  },
  dropdownItem: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
  propertyValue: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
})

export default TokenEconomicSection
