import React from "react"
import { StyleSheet, Text, TextInput, View } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import { Controller, Control } from "react-hook-form"
import Colors from "src/config/colors"
import { SearchView } from "src/componentsv2/SearchView"
import { LabelView } from "src/componentsv2"
import { BROKER_SEARCH_TYPE, SELLER_SEARCH_TYPE } from "../hooks"

interface UserInfoSectionProps {
  control: Control<any>
  formState: any
  type: "seller" | "broker"
  searchWarning?: string | null
  userInfo?: { alias: string } | null
  onChangeSearchUser: (
    onChange: (value: string | number) => void,
    type: string
  ) => (text: string | number) => void | Promise<void>
}

const UserInfoSection: React.FC<UserInfoSectionProps> = ({
  control,
  formState,
  type,
  searchWarning,
  userInfo,
  onChangeSearchUser,
}) => {
  const { t } = useTranslation()
  const isSeller = type === "seller"
  const fieldName = isSeller ? "requesterAddress" : "brokerAddress"
  const searchType = isSeller ? SELLER_SEARCH_TYPE : BROKER_SEARCH_TYPE

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>
        {isSeller ? t("Seller Information") : t("Broker Information")}
      </Text>

      <Controller
        control={control}
        name={fieldName}
        render={({ field: { onChange, value } }) => (
          <>
            <LabelView label={t("Wallet Address")} require={isSeller} />
            <SearchView
              value={value}
              onChangeText={onChangeSearchUser(onChange, searchType)}
              style={styles.marginTopSmall}
              multiline={false}
              error={
                formState.errors[fieldName]?.message &&
                String(formState.errors[fieldName]?.message)
              }
              placeholder={
                isSeller
                  ? t("Input wallet address of the seller to search")
                  : t("Input wallet address of the broker to search")
              }
            />
          </>
        )}
      />
      {searchWarning && (
        <Text style={styles.searchWarning}>{searchWarning}</Text>
      )}
      <LabelView label={t("Full name")} require={isSeller} />

      <TextInput
        value={userInfo ? userInfo.alias : ""}
        editable={false}
        style={styles.textInputStyle}
        placeholder={
          isSeller ? t("Name of the broker") : t("Name of the seller")
        }
        placeholderTextColor={Colors.Neutral700}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  section: {
    width: "100%",
    gap: 8,
  },
  sectionTitle: {
    ...textStyles.MBold,
    color: Colors.PalleteWhite,
  },
  marginTopDefault: {
    marginTop: 8,
  },
  marginTopSmall: {
    marginTop: 6,
  },
  searchWarning: {
    ...textStyles.MMedium,
    color: Colors.Warning500,
  },
  textInputStyle: {
    height: 38,
    width: "100%",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingHorizontal: 12,
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
  },
})

export default UserInfoSection
