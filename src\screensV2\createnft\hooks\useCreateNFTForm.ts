import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useTranslation } from "react-i18next"
import { EstateTokenAreaUnit, getCurrencies } from "src/api"
import {
  formatCurrency,
  isValidWalletAddress,
  formatNumericByDecimals,
} from "utils"
import { useSuspenseQuery } from "@tanstack/react-query"
import QueryKeys from "src/config/queryKeys"
import { useCallback, useMemo, useState } from "react"

const isVietnameseUppercaseCharacter = (char: string) =>
  /^[AĂÂBCDĐEÊGHIKLMNOÔƠPQRSTUƯVXY]$/.test(char)

const isValidSerialNumber = (serial: string) => {
  return (
    serial.length === 8 &&
    /^\d+$/.test(serial.slice(2)) &&
    isVietnameseUppercaseCharacter(serial[0]) &&
    isVietnameseUppercaseCharacter(serial[1])
  )
}

// Query options
const getCurrenciesQueryOptions = {
  queryKey: QueryKeys.CURRENCY.LIST,
  queryFn: () => getCurrencies(),
}

export interface CreateNFTFormOptions {
  isCurrentUserJustSeller: boolean
}

export const useCreateNFTForm = (options: CreateNFTFormOptions) => {
  const { isCurrentUserJustSeller } = options
  const { t } = useTranslation()
  const { data: currencies = [] } = useSuspenseQuery(getCurrenciesQueryOptions)

  // Định nghĩa schema ảnh
  const imageSchema = useMemo(
    () =>
      z.object({
        url: z.string(),
        fileName: z.string(),
        type: z.string(),
      }),
    []
  )

  // State để theo dõi các giá trị cần thiết cho validation
  const [validationParams, setValidationParams] = useState({
    totalSupply: 0,
    minUnitPrice: "0",
    maxUnitPrice: "0",
    decimals: 18,
    isInfinite: false,
    minSellingAmount: 0,
    maxSellingAmount: 0,
  })

  // Tạo schema cho selling limit
  const createSellingLimitSchema = useCallback(
    (
      totalSupply: number,
      minSellingAmount: number,
      maxSellingAmount: number
    ) => {
      return z.object({
        sellingLimit: z.object({
          minSellingAmount: z.number().superRefine((val, ctx) => {
            if (val <= 0) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t("Please input the min selling amount"),
              })
              return false
            }
            if (totalSupply > 0 && val > totalSupply) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t(
                  "Min selling amount must be less than or equal to total supply"
                ),
              })
              return false
            }
            if (maxSellingAmount > 0 && val > maxSellingAmount) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t(
                  "Min selling amount must be less than or equal to max selling amount"
                ),
              })
              return false
            }

            return true
          }),
          maxSellingAmount: z.number().superRefine((val, ctx) => {
            if (val <= 0) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t("Please input the max selling amount"),
              })
              return false
            }
            if (totalSupply > 0 && val > totalSupply) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t(
                  "Max selling amount must be less than or equal to total supply"
                ),
              })
              return false
            }
            if (minSellingAmount > 0 && val < minSellingAmount) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: t(
                  "Max selling amount must be greater than or equal to min selling amount"
                ),
              })
              return false
            }

            return true
          }),
        }),
      })
    },
    [t]
  )

  // Tạo schema cho form chính không bao gồm selling limit
  const createMainFormSchema = useCallback(
    (
      minUnitPrice: string,
      maxUnitPrice: string,
      decimals: number,
      isInfinite: boolean
    ) => {
      return z.object({
        name: z
          .string()
          .refine((val) => val.length >= 20, {
            message: t("Name must be at least 20 characters long"),
          })
          .refine((val) => val.length <= 100, {
            message: t("Name must be at most 100 characters long"),
          }),
        serial: z
          .string()
          .refine((val) => val.length > 0, {
            message: t("Not empty"),
          })
          .refine((val) => isValidSerialNumber(val), {
            message: t(
              "Serial number is not in the correct format, ex: AB123456"
            ),
          }),
        address: z
          .string()
          .refine((val) => val.length >= 20, {
            message: t("Address must be at least 20 characters long"),
          })
          .refine((val) => val.length <= 255, {
            message: t("Address must be at most 255 characters long"),
          }),
        addressCodeLevel1: z.string().refine((val) => val.length > 0, {
          message: t("Please select a city/province"),
        }),
        addressCodeLevel2: z.string().refine((val) => val.length > 0, {
          message: t("Please select a district"),
        }),
        addressCodeLevel3: z.string().refine((val) => val.length > 0, {
          message: t("Please select a ward"),
        }),
        area: z.number().refine((val) => val > 0, {
          message: t("Please input the area of the property"),
        }),
        areaUnit: z.string(),
        description: z.string(),
        unitPrice: z.number().superRefine((val, ctx) => {
          if (
            val < Number(formatNumericByDecimals(minUnitPrice, decimals)) ||
            val > Number(formatNumericByDecimals(maxUnitPrice, decimals))
          ) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t("Please input value in range from min to max", {
                min: formatCurrency(
                  formatNumericByDecimals(minUnitPrice, decimals)
                ),
                max: formatCurrency(
                  formatNumericByDecimals(maxUnitPrice, decimals)
                ),
              }),
            })
            return false
          }
          return true
        }),
        currencyId: z.string().min(1),
        decimals: z.number().refine((val) => val > 0, {
          message: t("Please input the decimals"),
        }),
        totalSupply: z.number().refine((val) => val > 0, {
          message: t("Please input the total supply"),
        }),
        landUseRights: z.array(imageSchema).refine((val) => val.length > 1, {
          message: t("Please upload at least 2 images of land use rights"),
        }),
        landMedia: z.array(imageSchema).refine((val) => val.length > 3, {
          message: t("Please upload at least 4 images of the property"),
        }),
        thumnail: z.array(imageSchema).refine((val) => val.length > 0, {
          message: t("Please upload thumbnail"),
        }),
        expiredAt: z.string().superRefine((val, ctx) => {
          if (isInfinite) {
            return true
          }
          if (isNaN(Date.parse(val))) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t("Please input property expiry date"),
            })
            return false
          }
          const currentDate = new Date()
          const next1Years = new Date(currentDate).setFullYear(
            currentDate.getFullYear() + 1
          )
          if (Date.parse(val) < next1Years) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t("The expiry date must be at least 1 year from now"),
            })
            return false
          }

          return true
        }),
        isInfinite: z.boolean(),
        duration: z.number().refine((val) => val > 0, {
          message: t("Please input the public sale duration"),
        }),
        durationUnit: z.string(),
        brokerAddress: z
          .string()
          .refine((val) => isValidWalletAddress(val) || val === "", {
            message: t("Invalid wallet address"),
          }),
        requesterAddress: z
          .string()
          .refine(
            (val) => {
              if (isCurrentUserJustSeller) return true
              return val.length > 0
            },
            {
              message: t("Please enter seller's wallet address"),
            }
          )
          .refine(
            (val) => {
              if (isCurrentUserJustSeller) return true
              return isValidWalletAddress(val)
            },
            {
              message: t("Invalid wallet address"),
            }
          ),
      })
    },
    [t, imageSchema, isCurrentUserJustSeller]
  )

  // Kết hợp các schema để tạo schema hoàn chỉnh
  const formSchema = useMemo(() => {
    const {
      totalSupply,
      minUnitPrice,
      maxUnitPrice,
      decimals,
      isInfinite,
      minSellingAmount,
      maxSellingAmount,
    } = validationParams
    const sellingLimitSchema = createSellingLimitSchema(
      totalSupply,
      minSellingAmount,
      maxSellingAmount
    )
    const mainFormSchema = createMainFormSchema(
      minUnitPrice,
      maxUnitPrice,
      decimals,
      isInfinite
    )

    return z.intersection(sellingLimitSchema, mainFormSchema)
  }, [validationParams, createSellingLimitSchema, createMainFormSchema])

  // Cấu hình form
  type Payload = z.infer<typeof formSchema>
  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      serial: "",
      name: "",
      address: "",
      area: 0,
      unitPrice: 0,
      areaUnit: EstateTokenAreaUnit.SQM,
      sellingLimit: {
        minSellingAmount: 0,
        maxSellingAmount: 0,
      },
      totalSupply: 0,
      landUseRights: [],
      landMedia: [],
      thumnail: [],
      decimals: 18,
      currencyId: currencies[0]?.currency,
      addressCodeLevel1: "",
      addressCodeLevel2: "",
      addressCodeLevel3: "",
      expiredAt: "",
      isInfinite: false,
      duration: 1,
      durationUnit: "day",
      description: "",
      brokerAddress: "",
      requesterAddress: "",
    },
    mode: "onChange",
  })

  // Xử lý thay đổi vị trí
  const handleLocationChange = useCallback(
    (level: string, value: string) => {
      if (level === "addressCodeLevel1") {
        form.setValue("addressCodeLevel1", value)
        form.clearErrors("addressCodeLevel1")
        form.setValue("addressCodeLevel2", "")
        form.setValue("addressCodeLevel3", "")
      } else if (level === "addressCodeLevel2") {
        form.clearErrors("addressCodeLevel2")
        form.setValue("addressCodeLevel2", value)
        form.setValue("addressCodeLevel3", "")
      } else {
        form.clearErrors("addressCodeLevel3")
        form.setValue("addressCodeLevel3", value)
      }
    },
    [form]
  )

  // Cập nhật validation của form
  const updateFormValidation = useCallback(
    (
      totalSupply: number,
      minUnitPrice: string,
      maxUnitPrice: string,
      decimals: number,
      isInfinite: boolean,
      minSellingAmount: number,
      maxSellingAmount: number
    ) => {
      setValidationParams({
        totalSupply,
        minUnitPrice,
        maxUnitPrice,
        decimals,
        isInfinite,
        minSellingAmount,
        maxSellingAmount,
      })

      if (isInfinite) {
        form.clearErrors("expiredAt")
      }
      form.clearErrors("sellingLimit")
    },
    [form]
  )

  return {
    form,
    currencies,
    handleLocationChange,
    updateFormValidation,
  }
}

export type CreateNFTFormType = ReturnType<typeof useCreateNFTForm>["form"]
