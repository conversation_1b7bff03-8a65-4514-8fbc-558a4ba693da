import React, { useCallback } from "react"
import { Image, ScrollView, StyleSheet, Text, View } from "react-native"
import {
  Background,
  TopBar,
  CustomPressable,
  DateTimeInput,
  InputField,
} from "src/componentsv2"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { ActivityIndicator } from "react-native-paper"
import { useTranslation } from "react-i18next"
import { Controller } from "react-hook-form"
import { PrimaryButton } from "src/componentsv2/Button"
import { useAccount } from "wagmi"
import {
  useEditProfileFormSchema,
  useEditProfile,
} from "./hooks/useEditProfile"
import { useChoosePhoto } from "utils/choosePhotoExt"
import { ImagePickerAsset } from "expo-image-picker/src/ImagePicker.types"
import { Dropdown } from "react-native-element-dropdown"
import { LabelView } from "src/componentsv2/LabelView"
import icCircleClose from "assets/imagesV2/ic_circle_close.png"
import icUpload from "assets/imagesV2/ic_upload.png"
import icCopy from "assets/imagesV2/ic_copy.png"
import { shortenAddress } from "utils/stringExt"
import * as Clipboard from "expo-clipboard"
import { showSuccess } from "utils/toast"
import { getDateFromTimestamp } from "src/utils/timeExt"

const ImageUploader: React.FC<{
  imageUri?: string
  onPress: () => void
  onClear: () => void
}> = ({ imageUri, onPress, onClear }) => {
  const { t } = useTranslation()

  return (
    <View style={styles.imageContainer}>
      <View>
        {imageUri ? (
          <CustomPressable style={{ alignItems: "center" }} onPress={onPress}>
            <Image source={{ uri: imageUri }} style={styles.image} />
            <CustomPressable onPress={onClear} style={styles.close}>
              <Image source={icCircleClose} style={viewStyles.size16Icon} />
            </CustomPressable>
          </CustomPressable>
        ) : (
          <CustomPressable style={styles.upload} onPress={onPress}>
            <Image source={icUpload} style={viewStyles.size16Icon} />
            <Text style={styles.uploadFile}>{t("Upload file")}</Text>
          </CustomPressable>
        )}
      </View>
    </View>
  )
}

const EditProfileView: React.FC = () => {
  const { t } = useTranslation()
  const { address } = useAccount()
  const { onSubmit, isLoading, profile, countriesData } = useEditProfile(
    address || ""
  )

  const { handleChoosePhoto } = useChoosePhoto()

  const onChoosePhoto = useCallback(
    (onChange: (value: ImagePickerAsset | undefined) => void) => {
      handleChoosePhoto((files) => {
        onChange(files[0])
      })
    },
    []
  )

  const handleCopyWalletAddress = async () => {
    if (!address) return
    await Clipboard.setStringAsync(address)
    showSuccess(t("Address copied"))
  }

  const { form } = useEditProfileFormSchema(t, profile || undefined)

  const dateOfBirth = getDateFromTimestamp(profile?.dob)

  return (
    <Background>
      <TopBar enableBack={true} title={t("Edit profile")} />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <LabelView label={t("Wallet")} style={styles.nationalityLabel} />
          <View style={styles.walletRow}>
            <Text style={{ color: Colors.Neutral300 }}>
              {shortenAddress(profile?.address || "")}
            </Text>
            <CustomPressable onPress={handleCopyWalletAddress}>
              <Image source={icCopy} style={viewStyles.size16Icon} />
            </CustomPressable>
          </View>
          <Controller
            control={form.control}
            name="fullName"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Display name")}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                style={styles.marginTop16}
              />
            )}
          />

          <DateTimeInput
            title={t("Date of birth")}
            value={dateOfBirth}
            disabled={true}
            onChangeDate={() => {}}
          />

          <LabelView label={t("Nationality")} style={styles.nationalityLabel} />
          <Dropdown
            value={profile?.nationality || countriesData[0].label}
            disable={true}
            data={countriesData}
            labelField="label"
            valueField="value"
            selectedTextStyle={styles.disableDropdownItem}
            placeholderStyle={styles.disableDropdownItem}
            itemTextStyle={styles.disableDropdownItem}
            onChange={() => {}}
            style={styles.dropdown}
          />
          <Controller
            control={form.control}
            name="email"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Email")}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                style={styles.marginTop16}
                error={
                  form.formState.errors.email?.message &&
                  String(form.formState.errors.email?.message)
                }
              />
            )}
          />
          <Controller
            control={form.control}
            name="phone"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Phone")}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                style={styles.marginTop16}
                error={
                  form.formState.errors.phone?.message &&
                  String(form.formState.errors.phone?.message)
                }
              />
            )}
          />
          <Controller
            control={form.control}
            name="avatar"
            render={({ field: { onChange, value } }) => {
              const imagePickerAsset = value as ImagePickerAsset | undefined
              return (
                <>
                  <LabelView label={t("Avatar")} style={styles.marginTop16} />
                  <ImageUploader
                    imageUri={imagePickerAsset?.uri}
                    onPress={() => onChoosePhoto(onChange)}
                    onClear={() => {
                      onChange(undefined)
                    }}
                  />
                </>
              )
            }}
          />

          <PrimaryButton
            title={t("Save")}
            onPress={form.handleSubmit(onSubmit)}
            color={Colors.Primary500}
            contentColor={textColors.textBlack}
            style={styles.editProfile}
            width={"100%"}
            height={38}
            isLoading={isLoading}
            icon={
              isLoading && (
                <ActivityIndicator size="small" color={Colors.white} />
              )
            }
          />
        </View>
      </ScrollView>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 12,
  },
  content: { marginBottom: 16 },
  upload: {
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadView: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 8,
  },
  close: {
    position: "absolute",
    right: 0,
    padding: 4,
  },
  nationalityLabel: {
    marginTop: 16,
    marginBottom: 4,
  },
  marginTop16: {
    marginTop: 16,
  },
  walletRow: {
    backgroundColor: Colors.Neutral900,
    borderRadius: 6,
    flexDirection: "row",
    paddingHorizontal: 12,
    justifyContent: "space-between",
    height: 38,
    alignItems: "center",
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  imageContainer: {
    width: "100%",
    aspectRatio: 20 / 9,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  image: {
    width: "100%",
    height: "100%",
    borderRadius: 8,
    aspectRatio: 1 / 1,
  },
  dropdown: {
    marginTop: 8,
    height: 38,
    width: "100%",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    backgroundColor: Colors.Neutral900,
    paddingEnd: 12,
    ...textStyles.MMedium,
  },
  dropdownItem: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
    backgroundColor: Colors.PalleteBlack,
    alignItems: "center",
    paddingVertical: 8,
    paddingStart: 12,
  },
  disableDropdownItem: {
    ...textStyles.MMedium,
    color: Colors.Neutral300,
    backgroundColor: Colors.Neutral900,
    alignItems: "center",
    paddingVertical: 8,
    paddingStart: 12,
  },
  editProfile: {
    width: "100%",
    marginTop: 20,
    marginBottom: 30,
    alignSelf: "center",
  },
  uploadFile: {
    ...textStyles.SMedium,
    color: Colors.PalleteWhite,
    marginTop: 8,
  },
})

export default EditProfileView
