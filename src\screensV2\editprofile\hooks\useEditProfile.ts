import { z } from "zod"
import { useMutation } from "@tanstack/react-query"
import { useAtom } from "jotai"
import { profile<PERSON>tom } from "src/context/AuthContext"
import { getMyProfile, updateProfile, User } from "src/api"
import { useHandleError } from "src/api/errors/handleError"
import { useTranslation } from "react-i18next"
import { showSuccess } from "utils/toast"
import { getImageType, getPhotoFileName } from "utils/choosePhotoExt"
import { useNavigation } from "@react-navigation/native"
import { NavigationProp, ParamListBase } from "@react-navigation/native"
import { useForm, UseFormReturn } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"

type EditProfileFormData = {
  fullName: string
  avatar?: {
    uri: string
    fileName: string
    width?: number
    height?: number
    type?: string
  }
  email: string
  phone: string
}

export const useEditProfileFormSchema = (
  t: (key: string) => string,
  profile?: User
): {
  formSchema: z.ZodType<EditProfileFormData>
  form: UseFormReturn<EditProfileFormData>
} => {
  const imagePickerAssetSchema = z
    .object({
      uri: z.string(),
      fileName: z.string(),
      width: z.number().optional(),
      height: z.number().optional(),
      type: z.string().optional(),
    })
    .optional()

  const schema = z.object({
    fullName: z
      .string()
      .min(2, t("Full name must be at least 2 characters"))
      .max(100, t("Full name must be at most 100 characters")),
    avatar: imagePickerAssetSchema,
    email: z.string().email(t("Invalid email")),
    phone: z
      .string()
      .min(10, t("Phone number must be at least 10 digits"))
      .max(12, t("Phone number must not exceed 12 digits")),
  })

  const form = useForm<EditProfileFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      fullName: profile?.alias || "",
      avatar: undefined,
      email: profile?.email || "",
      phone: profile?.phone || "",
    },
  })

  return {
    formSchema: schema,
    form,
  }
}

export const useEditProfile = (address: string) => {
  const { t } = useTranslation()
  const { handleError } = useHandleError()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const [profile, setProfile] = useAtom(profileAtom)

  const mutateEditProfile = useMutation({
    mutationFn: (body: FormData) => updateProfile(body),
    onSuccess: async (data) => {
      if (data) {
        showSuccess(t("Edit profile success"))
        mutationGetProfile.mutate()
      }
    },
    onError: (err) => {
      handleError(err, t("Edit profile fail"))
    },
  })

  const mutationGetProfile = useMutation({
    mutationFn: () => {
      return getMyProfile(address)
    },
    onSuccess: (userProfile: User) => {
      setProfile(userProfile)
    },
    onSettled() {
      onGoBack()
    },
  })

  const onGoBack = () => {
    navigation.goBack()
  }

  const onSubmit = (data: EditProfileFormData) => {
    const body = new FormData()
    body.append("phone", data.phone)
    body.append("email", data.email)
    body.append("alias", data.fullName)

    if (data.avatar) {
      const avatarFileName = getPhotoFileName(
        data.avatar.fileName || null,
        data.avatar.uri
      )
      const frontImageType = getImageType(avatarFileName)

      // @ts-ignore
      body.append("avatarImage", {
        uri: data.avatar.uri,
        name: avatarFileName,
        type: frontImageType,
      })
    }

    mutateEditProfile.mutate(body)
  }

  const countriesData = [{ label: t("VIETNAM"), value: "VIETNAM" }]

  return {
    countriesData,
    onSubmit,
    isLoading: mutateEditProfile.isPending || mutationGetProfile.isPending,
    profile,
  }
}

export type Country = {
  label: string
  value: string
}
