import React from "react"
import { EstateDetailSectionItem, EstateDetailSectionType } from "../types"
import {
  BasicSection,
  ProgressSection,
  TransferNFTSection,
  WithdrawSection,
  DescriptionSection,
  OnChainSection,
  TraitsSection,
  LegalSection,
  ActivitiesOffersSection,
} from "./sections"

interface SectionRendererProps {
  item: EstateDetailSectionItem
}

const SectionRenderer: React.FC<SectionRendererProps> = ({ item }) => {
  switch (item.type) {
    case EstateDetailSectionType.BASIC:
      return <BasicSection item={item} />
    case EstateDetailSectionType.PROGRESS:
      return <ProgressSection item={item} />
    case EstateDetailSectionType.TRANSFER_NFT:
      return <TransferNFTSection item={item} />
    case EstateDetailSectionType.WITHDRAW:
      return <WithdrawSection item={item} />
    case EstateDetailSectionType.DESCRIPTION:
      return <DescriptionSection item={item} />
    case EstateDetailSectionType.ONCHAIN:
      return <OnChainSection item={item} />
    case EstateDetailSectionType.TRAITS:
      return <TraitsSection item={item} />
    case EstateDetailSectionType.LEGAL:
      return <LegalSection item={item} />
    case EstateDetailSectionType.ACTIVITIES_OFFERS:
      return <ActivitiesOffersSection />

    default:
      return null
  }
}

export default SectionRenderer
