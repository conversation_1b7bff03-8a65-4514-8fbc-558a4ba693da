import React from "react"
import { StyleSheet, Text, View, Image } from "react-native"
import { useTranslation } from "react-i18next"
import { Divider } from "react-native-paper"

import { EstateActivity, EstateActivityCategory } from "src/api/types"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { AddressView } from "components"
import { formatNumericByDecimalsDisplay, shortenAddress } from "utils"
import { getElapsedTime } from "utils/timeExt"
import BoxInfoView from "../../../../shared/components/BoxInfoView"
import saleIcon from "assets/images/ic_sale.png"
import transferIcon from "assets/imagesV2/ic_arrow_left_right.png"
import mortgageIcon from "assets/images/ic_mortgage.png"
import retriveIcon from "assets/images/ic_retrieve.png"
import foreClosedIcon from "assets/images/ic_fore_closed.png"

interface ActivityItemProps {
  activity: EstateActivity
  decimals: number
}

const useActivityConfig = (category: EstateActivityCategory) => {
  const { t } = useTranslation()
  const configs = {
    [EstateActivityCategory.SALE]: { icon: saleIcon, title: t("Sale") },
    [EstateActivityCategory.TRANSFER]: {
      icon: transferIcon,
      title: t("Transfer"),
    },
    [EstateActivityCategory.MORTGAGE]: {
      icon: mortgageIcon,
      title: t("Mortgage"),
    },
    [EstateActivityCategory.RETRIEVE]: {
      icon: retriveIcon,
      title: t("Retrieve"),
    },
    [EstateActivityCategory.FORECLOSED]: {
      icon: foreClosedIcon,
      title: t("Foreclosed"),
    },
  }
  return configs[category]
}

const ActivityItem: React.FC<ActivityItemProps> = ({ activity, decimals }) => {
  const { t } = useTranslation()
  const config = useActivityConfig(activity.category)
  return (
    <View style={styles.itemContainer}>
      <View style={styles.infoContainer}>
        <View style={styles.infoRow}>
          <View style={styles.infoColumn}>
            <Text style={styles.label}>{t("From")}</Text>
            <AddressView
              address={activity.from.address}
              copy={true}
              style={{ marginStart: 4 }}
            />
          </View>
          <View style={styles.infoColumn}>
            <Text style={styles.label}>{t("To")}</Text>
            <AddressView
              address={activity.to.address}
              copy={true}
              style={{ marginStart: 4 }}
            />
          </View>
        </View>
      </View>
      <View style={{ flexDirection: "row" }}>
        <BoxInfoView>
          <Text style={styles.label}>{config.title}</Text>
          <Image
            source={config.icon}
            style={viewStyles.size12Icon}
            tintColor={Colors.PalleteWhite}
          />
        </BoxInfoView>
        <BoxInfoView style={{ marginLeft: 8 }}>
          <Text style={styles.label}>{t("Time")}</Text>
          <Text style={styles.value}>
            {getElapsedTime(activity.blockTimestamp, t)}
          </Text>
        </BoxInfoView>
        <BoxInfoView style={{ marginLeft: 8 }}>
          <Text style={styles.label}>{t("Quantity")}</Text>
          <Text style={styles.value}>
            {formatNumericByDecimalsDisplay(activity.amount, decimals)}
          </Text>
        </BoxInfoView>
      </View>

      <View style={styles.hashContainer}>
        <Text style={styles.label}>{t("Transaction Hash")}</Text>
        <Text style={[styles.value, { color: Colors.Secondary300 }]}>
          {shortenAddress(activity.txHash)}
        </Text>
      </View>
      <Divider
        style={{ marginTop: 12, backgroundColor: Colors.Neutral950, height: 1 }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    paddingTop: 12,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  infoContainer: {
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  infoColumn: {
    marginRight: 8,
    flexDirection: "row",
  },
  infoInput: {
    height: 40,
    borderWidth: 1,
    borderColor: Colors.opacityWhite15,
    borderRadius: 4,
    paddingHorizontal: 12,
    color: Colors.white,
    backgroundColor: "transparent",
  },
  addressContainer: {
    height: 40,
    borderWidth: 1,
    borderColor: Colors.opacityWhite15,
    borderRadius: 4,
    paddingHorizontal: 12,
    justifyContent: "center",
  },
  hashContainer: {
    marginTop: 4,
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    ...textStyles.SMedium,
    color: Colors.Neutral300,
  },
  value: {
    ...textStyles.SMedium,
    color: Colors.white,
    marginStart: 4,
  },
})

export default ActivityItem
