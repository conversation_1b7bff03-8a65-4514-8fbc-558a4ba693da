import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { Divider } from "react-native-paper"

import { MarketplaceOffer } from "src/api/types/marketplace"
import { AddressView } from "components"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { shortenAddress } from "utils"
import { formatCurrencyByDecimals } from "utils/format"
import { OfferActionButton } from "src/screensV2/shared/components/OfferActionButton"

interface OfferItemProps {
  offer: MarketplaceOffer
}

const OfferItem: React.FC<OfferItemProps> = ({ offer }) => {
  const { t } = useTranslation()
  const { sellingAmount, soldAmount, unitPrice, isDivisible, seller } = offer

  const formattedPrice = formatCurrencyByDecimals(unitPrice, 18)
  const formattedSellingAmount = formatCurrencyByDecimals(sellingAmount, 18)

  const formattedSoldAmount = formatCurrencyByDecimals(soldAmount, 18)
  const formattedIsDivisible = isDivisible ? t("Yes") : t("No")
  const formattedSellerAddress = shortenAddress(seller?.address)

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <View style={styles.labelContainer}>
          <Text style={styles.label}>{t("From")}</Text>
          <AddressView
            address={formattedSellerAddress}
            copy={true}
            style={{ marginStart: 4 }}
          />
        </View>
      </View>

      <View style={[styles.row, { marginTop: 6 }]}>
        <View style={styles.infoBox}>
          <Text style={styles.label}>{t("Quantity")}</Text>
          <Text style={styles.value}>{formattedSellingAmount}</Text>
        </View>

        <View style={[styles.infoBox, styles.divisibleBox]}>
          <Text style={styles.label}>{t("Divisible")}</Text>
          <Text style={styles.value}>{formattedIsDivisible}</Text>
        </View>

        <View style={[styles.infoBox, styles.soldBox]}>
          <Text style={styles.label}>{t("Sold")}</Text>
          <Text style={styles.value}>
            {formattedSoldAmount}/{formattedSellingAmount}
          </Text>
        </View>
      </View>

      <View style={styles.footer}>
        <View style={styles.priceContainer}>
          <Text style={styles.label}>{t("Price")}</Text>
          <Text style={[styles.value, { marginStart: 4 }]}>
            {formattedPrice}
          </Text>
        </View>
        <OfferActionButton offer={offer} buttonHeight={24} borderRadius={4} />
      </View>
      <Divider
        style={{ marginTop: 12, backgroundColor: Colors.Neutral950, height: 1 }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.blackNew,
    borderRadius: 8,
    paddingTop: 12,
  },
  row: {
    flexDirection: "row",
  },
  labelContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    ...textStyles.SMedium,
    color: Colors.Neutral300,
  },
  value: {
    ...textStyles.SMedium,
    color: Colors.white,
  },
  infoBox: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.Neutral950,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 6,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },

  divisibleBox: {
    marginLeft: 8,
  },
  soldBox: {
    marginLeft: 8,
  },
  footer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 12,
  },
  priceContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },

  buyButton: {
    height: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  buyButtonText: {
    ...textStyles.LMedium,
    color: Colors.blackNew,
  },
})

export default OfferItem
