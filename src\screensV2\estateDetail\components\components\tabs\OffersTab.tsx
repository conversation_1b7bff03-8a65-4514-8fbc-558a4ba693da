import React from "react"
import { useEstateDetailContext } from "src/screensV2/estateDetail/context"
import OfferItem from "./OfferItem"
import SimplePagingList from "src/componentsv2/simplepaginglist/SimplePagingList"
import { getPaginatedMarketPlaceOffers, MarketplaceOffer } from "src/api"
import QueryKeys from "src/config/queryKeys"

const OffersTab: React.FC = () => {
  const { estateId } = useEstateDetailContext()

  return (
    <SimplePagingList<MarketplaceOffer>
      getData={(params) => getPaginatedMarketPlaceOffers(estateId, params)}
      renderItem={(item) => <OfferItem offer={item} />}
      keyExtractor={(item) => item.id.toString()}
      scrollEnabled={false}
      queryKeys={QueryKeys.MARKETPLACE.OFFERS(estateId)}
    />
  )
}
export default OffersTab
