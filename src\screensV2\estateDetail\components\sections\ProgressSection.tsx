import React from "react"
import { StyleSheet, View, Text, Image } from "react-native"
import { ProgressSectionItem } from "../../types"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import circleCheckIcon from "assets/imagesV2/ic_circle_check.png"
import { viewStyles, textStyles } from "src/config/styles"
import { convertDateFromTimeStamp, DateTimeFormat } from "src/utils/timeExt"
interface ProgressSectionProps {
  item: ProgressSectionItem
}

const ProgressSection: React.FC<ProgressSectionProps> = ({ item }) => {
  const { t } = useTranslation()

  const { estate } = item
  const { createAtInSeconds } = estate

  const formattedDate = convertDateFromTimeStamp(
    createAtInSeconds * 1000,
    DateTimeFormat.HHMM
  )
  const displayDate = `${t("Successfully tokenized at")} ${formattedDate}`

  return (
    <View style={styles.container}>
      <Image source={circleCheckIcon} style={styles.icon} />
      <Text style={styles.text}>{displayDate}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    backgroundColor: Colors.Success900,
    borderRadius: 12,
    gap: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
  },
  icon: {
    ...viewStyles.size16Icon,
    tintColor: Colors.Success300,
  },
  text: {
    ...textStyles.LMedium,
    color: Colors.Success300,
  },
})

export default ProgressSection
