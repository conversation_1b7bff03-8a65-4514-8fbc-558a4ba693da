import React from "react"
import { WithdrawSectionItem } from "../../types"
import { View, StyleSheet } from "react-native"
import Colors from "src/config/colors"
import {
  EstateValueView,
  WithdrawSectionView,
} from "src/screensV2/shared/components"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"
interface WithdrawSectionProps {
  item: WithdrawSectionItem
}

const WithdrawSection: React.FC<WithdrawSectionProps> = ({ item }) => {
  const { estate } = item
  const {
    maxSellingAmount,
    soldAmount,
    unitPrice,
    totalSupply,
    decimals,
    currency,
    state,
    id,
  } = estate.tokenizationRequest

  const { tokenSymbol } = useCurrencies(currency)

  return (
    <View style={styles.container}>
      <EstateValueView
        unitPrice={unitPrice}
        totalSupply={totalSupply}
        soldAmount={soldAmount}
        maxSellingAmount={maxSellingAmount}
        decimals={decimals}
        tokenSymbol={tokenSymbol ?? ""}
        state={state}
      />
      <WithdrawSectionView
        requestId={id}
        state={state}
        currency={currency}
        decimals={decimals}
        unitPrice={unitPrice}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.Neutral950,
    borderRadius: 6,
    padding: 12,
    gap: 12,
  },
})

export default WithdrawSection
