import React, { createContext, useContext, ReactNode } from "react"
import { EstateDetailContextState } from "./types"
import { Estate } from "src/api/types"
import { useEstateDetailProvider } from "../hooks/useEstateDetailProvider"

const EstateDetailContext = createContext<EstateDetailContextState>({
  estateId: "",
  estateDetail: null,
  currencies: [],
  isLoadingEstateDetail: false,
  estateDetailError: null,
  refreshEstateDetail: async () => {},
})

// Hook to use the context
export const useEstateDetailContext = () => useContext(EstateDetailContext)

// Provider props
interface EstateDetailProviderProps {
  children: ReactNode
  estateId: string
  isFocused?: boolean
  initialEstateDetail?: Estate | null
}

// Provider component
export const EstateDetailProvider: React.FC<EstateDetailProviderProps> = ({
  children,
  estateId,
  isFocused = true,
}) => {
  // Use the custom hook to get the context value
  const contextValue = useEstateDetailProvider(estateId, isFocused)

  return (
    <EstateDetailContext.Provider value={contextValue}>
      {children}
    </EstateDetailContext.Provider>
  )
}

export default EstateDetailContext
