import { useQueryWithErrorHandling } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import { getCurrencies, Currency } from "src/api"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "useCurrencies" })

/**
 * Hook to fetch currencies
 * @param isFocused Whether the screen is currently focused
 */
export const useCurrencies = (isFocused: boolean = true) => {
  logger.debug("Fetching currencies", { isFocused })

  return useQueryWithErrorHandling<Currency[]>({
    queryKey: QueryKeys.CURRENCY.LIST,
    queryFn: () => getCurrencies(),
    enabled: isFocused,
  })
}
