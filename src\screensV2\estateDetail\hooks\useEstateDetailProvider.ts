import { EstateDetailContextState } from "../context/types"
import { useCurrencies } from "./useCurrencies"
import { useEstateDetail } from "./useEstateDetail"

/**
 * Hook that combines all data fetching hooks for the EstateDetail screen
 * and provides the context value
 * @param estateId The ID of the estate to fetch data for
 * @param isFocused Whether the screen is currently focused
 */
export const useEstateDetailProvider = (
  estateId: string,
  isFocused: boolean = true
): EstateDetailContextState => {
  // Fetch estate detail
  const {
    data: estateDetail,
    isLoading: isLoadingEstateDetail,
    error: estateDetailError,
    refetch: refetchEstateDetail,
  } = useEstateDetail(estateId, isFocused)

  const { data: currencies = [] } = useCurrencies(isFocused)

  return {
    // Core data
    estateId,
    estateDetail: estateDetail || null,
    currencies,
    isLoadingEstateDetail,

    // Error states
    estateDetailError: estateDetailError as Error | null,
    refreshEstateDetail: async () => {
      await refetchEstateDetail()
    },
  }
}
