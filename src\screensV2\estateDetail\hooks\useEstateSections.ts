import { useMemo } from "react"

import Logger from "src/utils/logger"
import { useEstateDetailContext } from "../context"
import {
  EstateDetailSectionItem,
  EstateDetailSectionType,
} from "../types/sections"

const logger = new Logger({ tag: "useEstateSections" })

/**
 * Hook to transform estate data into sections for FlatList
 */
export const useEstateSections = () => {
  const { estateId, estateDetail, isLoadingEstateDetail } =
    useEstateDetailContext()

  // Create sections based on the data
  const sections = useMemo(() => {
    if (!estateDetail) return []

    logger.debug("Creating sections for estate", { estateId })

    const result: EstateDetailSectionItem[] = []

    // Basic section
    result.push({
      type: EstateDetailSectionType.BASIC,
      id: `${EstateDetailSectionType.BASIC}-${estateId}`,
      estate: estateDetail,
    })

    // Progress section
    result.push({
      type: EstateDetailSectionType.PROGRESS,
      id: `${EstateDetailSectionType.PROGRESS}-${estateId}`,
      estate: estateDetail,
    })

    // Withdraw section
    result.push({
      type: EstateDetailSectionType.WITHDRAW,
      id: `${EstateDetailSectionType.WITHDRAW}-${estateId}`,
      estate: estateDetail,
    })

    // TransferNFT section
    result.push({
      type: EstateDetailSectionType.TRANSFER_NFT,
      id: `${EstateDetailSectionType.TRANSFER_NFT}-${estateId}`,
      estate: estateDetail,
    })

    // Description section
    result.push({
      type: EstateDetailSectionType.DESCRIPTION,
      id: `${EstateDetailSectionType.DESCRIPTION}-${estateId}`,
      description: estateDetail.metadata.metadata.description,
    })

    // OnChain section
    result.push({
      type: EstateDetailSectionType.ONCHAIN,
      id: `${EstateDetailSectionType.ONCHAIN}-${estateId}`,
      requestId: estateDetail.tokenizationRequest.id,
      uri: estateDetail.metadata.metadata.uri,
    })

    // Traits section
    result.push({
      type: EstateDetailSectionType.TRAITS,
      id: `${EstateDetailSectionType.TRAITS}-${estateId}`,
      estateTokenTraits: estateDetail.metadata.metadata.attributes,
    })

    // Legal section
    result.push({
      type: EstateDetailSectionType.LEGAL,
      id: `${EstateDetailSectionType.LEGAL}-${estateId}`,
      metadataId: estateDetail.metadata.metadata.id,
      tokenMintEventTxHash: estateDetail.tokenMintEventTxHash,
    })

    // Activities/Offers section
    result.push({
      type: EstateDetailSectionType.ACTIVITIES_OFFERS,
      id: `${EstateDetailSectionType.ACTIVITIES_OFFERS}-${estateId}`,
      estate: estateDetail,
    })

    return result
  }, [estateDetail, estateId])

  const isLoading = isLoadingEstateDetail

  return {
    sections,
    isLoading,
  }
}
