import React from "react"
import { RouteProp } from "@react-navigation/native"
import { RootStackParamList } from "src/navigatorV2"
import { EstateRequestDetailProvider } from "./context"
import Logger from "src/utils/logger"
import EstateRequestDetailView from "./EstateRequestDetailView"

const logger = new Logger({ tag: "EstateRequestDetailScreen" })

// Props for the screen
interface EstateRequestDetailScreenProps {
  route: RouteProp<RootStackParamList, "EstateRequestDetail">
}

// The main screen component that provides the context
const EstateRequestDetailScreen: React.FC<EstateRequestDetailScreenProps> = ({
  route,
}) => {
  const { estateRequestId } = route.params

  logger.debug("Rendering EstateRequestDetailScreen", { estateRequestId })

  return (
    <EstateRequestDetailProvider estateRequestId={estateRequestId}>
      <EstateRequestDetailView />
    </EstateRequestDetailProvider>
  )
}

export default EstateRequestDetailScreen
