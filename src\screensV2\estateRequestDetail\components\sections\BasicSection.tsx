import React from "react"
import { BasicSectionItem } from "../../types"
import { BasicSectionEstateView } from "src/screensV2/shared/components"

interface BasicSectionProps {
  item: BasicSectionItem
}

const BasicSection: React.FC<BasicSectionProps> = ({ item }) => {
  const tokenizationRequest = item.estateRequest

  const {
    metadata: {
      estatePhotoUrls: images,
      metadata: { name, address, area, created_at: createdAt, locale_detail },
    },
    requester: { avatarUrl: sellerAvatar, address: sellerAddress },
    totalSupply,
    decimals,
  } = tokenizationRequest

  return (
    <BasicSectionEstateView
      images={images}
      name={name}
      sellerAvatar={sellerAvatar}
      sellerAddress={sellerAddress}
      createdAt={createdAt}
      address={address}
      totalSupply={totalSupply}
      area={area}
      decimals={decimals}
      locale_detail={locale_detail}
    />
  )
}

export default BasicSection
