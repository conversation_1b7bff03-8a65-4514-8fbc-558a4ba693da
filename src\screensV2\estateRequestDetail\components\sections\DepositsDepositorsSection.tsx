import React, { useState } from "react"
import { StyleSheet, View } from "react-native"
import { useTranslation } from "react-i18next"

import { textStyles } from "src/config/styles"
import { TabSelector } from "src/screensV2/estate/components"
import { DepositsTab, DepositorsTab } from "../tabs"

const DepositsDepositorsSection: React.FC = () => {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState(0)
  const tabTitles = [t("Deposits"), t("Depositors")]

  return (
    <View style={styles.contentContainer}>
      {/* Tab Selector */}
      <TabSelector
        tabTitles={tabTitles}
        selectedIndex={activeTab}
        setTabIndex={setActiveTab}
      />

      {/* Tab Content */}
      <View style={styles.tabContent}>
        {activeTab === 0 ? <DepositsTab /> : <DepositorsTab />}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  contentContainer: {
    paddingVertical: 16,
  },
  sectionTitle: {
    ...textStyles.titleM,
    marginBottom: 16,
    textAlign: "center",
    textTransform: "uppercase",
  },
  tabContent: {
    minHeight: 200,
    marginTop: 16,
  },
})

export default DepositsDepositorsSection
