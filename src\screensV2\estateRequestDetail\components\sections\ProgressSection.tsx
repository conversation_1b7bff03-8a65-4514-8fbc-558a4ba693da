import React from "react"
import { ProgressSectionItem } from "../../types"
import { ProgressSectionView } from "src/screensV2/shared/components"

interface ProgressSectionProps {
  item: ProgressSectionItem
}

const ProgressSection: React.FC<ProgressSectionProps> = ({ item }) => {
  const { estateRequest } = item
  const {
    totalSupply,
    minSellingAmount,
    maxSellingAmount,
    soldAmount,
    publicSaleEndsAtInSeconds,
    state,
    decimals,
  } = estateRequest

  return (
    <ProgressSectionView
      isApplicationDetail={false}
      totalSupply={totalSupply}
      minSellingAmount={minSellingAmount}
      maxSellingAmount={maxSellingAmount}
      soldAmount={soldAmount}
      publicSaleEndsAtInSeconds={publicSaleEndsAtInSeconds}
      state={state}
      decimals={decimals}
    />
  )
}

export default ProgressSection
