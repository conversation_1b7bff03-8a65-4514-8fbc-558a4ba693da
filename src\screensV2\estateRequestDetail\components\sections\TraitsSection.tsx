import React from "react"
import { StyleSheet, View } from "react-native"
import { TraitsSectionItem } from "../../types"
import { TraitsView } from "src/screensV2/shared/components/traitsview/TraitsView"

interface TraitsSectionProps {
  item: TraitsSectionItem
}

const TraitsSection: React.FC<TraitsSectionProps> = ({ item }) => {
  const { estateTokenTraits } = item

  return (
    <View style={styles.container}>
      <TraitsView estateTokenTraits={estateTokenTraits} />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
})

export default TraitsSection
