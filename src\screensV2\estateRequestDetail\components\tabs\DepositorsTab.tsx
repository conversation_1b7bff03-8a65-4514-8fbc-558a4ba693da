import React from "react"
import { TokenizationDepositor } from "src/api/types"
import { fixedPointMultiply, formatCurrencyByDecimals } from "utils"
import { useEstateRequestDetailContext } from "src/screensV2/estateRequestDetail/context"
import HolderItem from "src/screensV2/shared/components/HolderItem"
import SimplePagingList from "src/componentsv2/simplepaginglist/SimplePagingList"
import QueryKeys from "src/config/queryKeys"
import { getPaginatedDepositorsByEstateRequestId } from "src/api"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"
const DepositorsTab: React.FC = () => {
  const { estateRequestDetail, estateRequestId } =
    useEstateRequestDetailContext()
  const decimals = estateRequestDetail?.decimals || 0
  const unitPrice = estateRequestDetail?.unitPrice
  const { tokenSymbol } = useCurrencies(estateRequestDetail?.currency || "")
  const renderItem = (item: TokenizationDepositor) => {
    const value = fixedPointMultiply(
      BigInt(unitPrice || "0"),
      BigInt(item.tokenAmount),
      decimals
    )

    const formattedAmount = formatCurrencyByDecimals(item.tokenAmount, decimals)
    const formattedValue = formatCurrencyByDecimals(value.toString(), decimals)
    return (
      <HolderItem
        fromAddress={item.depositor.address}
        amount={formattedAmount}
        value={formattedValue}
        currency={tokenSymbol || ""}
      />
    )
  }

  return (
    <SimplePagingList<TokenizationDepositor>
      getData={(params) =>
        getPaginatedDepositorsByEstateRequestId(estateRequestId, params)
      }
      renderItem={renderItem}
      keyExtractor={(item) => item.depositor.address}
      scrollEnabled={false}
      queryKeys={QueryKeys.ESTATE.REQUEST_DEPOSITORS(estateRequestId)}
    />
  )
}

export default DepositorsTab
