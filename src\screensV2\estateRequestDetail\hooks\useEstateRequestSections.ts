import { useMemo } from "react"

import Logger from "src/utils/logger"
import { useEstateRequestDetailContext } from "../context"
import {
  EstateRequestDetailSectionItem,
  EstateRequestDetailSectionType,
} from "../types"

const logger = new Logger({ tag: "useEstateRequestSections" })

/**
 * Hook to create sections for the EstateRequestDetail screen
 */
export const useEstateRequestSections = () => {
  const { estateRequestId, estateRequestDetail } =
    useEstateRequestDetailContext()

  // Create sections based on the data
  const sections = useMemo(() => {
    if (!estateRequestDetail) return []

    logger.debug("Creating sections for estate request", { estateRequestId })

    const result: EstateRequestDetailSectionItem[] = []

    // Basic section
    result.push({
      type: EstateRequestDetailSectionType.BASIC,
      id: `${EstateRequestDetailSectionType.BASIC}-${estateRequestId}`,
      estateRequest: estateRequestDetail,
    })

    // Progress section
    result.push({
      type: EstateRequestDetailSectionType.PROGRESS,
      id: `${EstateRequestDetailSectionType.PROGRESS}-${estateRequestId}`,
      estateRequest: estateRequestDetail,
    })

    // Deposit section
    result.push({
      type: EstateRequestDetailSectionType.DEPOSIT,
      id: `${EstateRequestDetailSectionType.DEPOSIT}-${estateRequestId}`,
      estateRequest: estateRequestDetail,
    })

    // Description section
    result.push({
      type: EstateRequestDetailSectionType.DESCRIPTION,
      id: `${EstateRequestDetailSectionType.DESCRIPTION}-${estateRequestId}`,
      description: estateRequestDetail.metadata.metadata.description,
    })

    result.push({
      type: EstateRequestDetailSectionType.ONCHAIN,
      id: `${EstateRequestDetailSectionType.ONCHAIN}-${estateRequestId}`,
      requestId: estateRequestId,
      uri: estateRequestDetail.metadata.metadata.uri,
    })

    result.push({
      type: EstateRequestDetailSectionType.TRAITS,
      id: `${EstateRequestDetailSectionType.TRAITS}-${estateRequestId}`,
      estateTokenTraits: estateRequestDetail.metadata.metadata.attributes,
    })

    // Legal section
    result.push({
      type: EstateRequestDetailSectionType.LEGAL,
      id: `${EstateRequestDetailSectionType.LEGAL}-${estateRequestId}`,
      metadataId: estateRequestDetail.metadata.metadata.id,
      tokenMintEventTxHash: estateRequestDetail.tokenMintEventTxHash,
    })

    // Deposits/Depositors section
    result.push({
      type: EstateRequestDetailSectionType.DEPOSITS_DEPOSITORS,
      id: `${EstateRequestDetailSectionType.DEPOSITS_DEPOSITORS}-${estateRequestId}`,
      estateRequest: estateRequestDetail,
    })

    return result
  }, [estateRequestId, estateRequestDetail])

  return sections
}
