import { TokenizationRequest, EstateTokenAttribute } from "src/api/types"

/**
 * Enum for section types in the EstateRequestDetail screen
 */
export enum EstateRequestDetailSectionType {
  BASIC = "basic",
  PROGRESS = "progress",
  DEPOSIT = "deposit",
  DESCRIPTION = "description",
  ONCHAIN = "onchain",
  LEGAL = "legal",
  TRAITS = "traits",
  DEPOSITS_DEPOSITORS = "depositsDepositors",
}

/**
 * Base interface for all section items
 */
export interface BaseSectionItem {
  type: EstateRequestDetailSectionType
  id: string
}

/**
 * Description section item
 */
export interface DescriptionSectionItem extends BaseSectionItem {
  type: EstateRequestDetailSectionType.DESCRIPTION
  description: string
}

export interface OnChainSectionItem extends BaseSectionItem {
  type: EstateRequestDetailSectionType.ONCHAIN
  requestId: string
  uri: string
}

export interface TraitsSectionItem extends BaseSectionItem {
  type: EstateRequestDetailSectionType.TRAITS
  estateTokenTraits: EstateTokenAttribute[]
}
/**
 * Basic section item
 */
export interface BasicSectionItem extends BaseSectionItem {
  type: EstateRequestDetailSectionType.BASIC
  estateRequest: TokenizationRequest
}

/**
 * Progress section item
 */
export interface ProgressSectionItem extends BaseSectionItem {
  type: EstateRequestDetailSectionType.PROGRESS
  estateRequest: TokenizationRequest
}

/**
 * Deposit section item
 */
export interface DepositSectionItem extends BaseSectionItem {
  type: EstateRequestDetailSectionType.DEPOSIT
  estateRequest: TokenizationRequest
}

/**
 * Legal section item
 */
export interface LegalSectionItem extends BaseSectionItem {
  type: EstateRequestDetailSectionType.LEGAL
  metadataId: number
  tokenMintEventTxHash: string
}

/**
 * Deposits/Depositors section item
 */
export interface DepositsDepositorsSectionItem extends BaseSectionItem {
  type: EstateRequestDetailSectionType.DEPOSITS_DEPOSITORS
  estateRequest: TokenizationRequest
}

/**
 * Union type for all section items
 */
export type EstateRequestDetailSectionItem =
  | BasicSectionItem
  | ProgressSectionItem
  | DepositSectionItem
  | DescriptionSectionItem
  | LegalSectionItem
  | OnChainSectionItem
  | TraitsSectionItem
  | DepositsDepositorsSectionItem
