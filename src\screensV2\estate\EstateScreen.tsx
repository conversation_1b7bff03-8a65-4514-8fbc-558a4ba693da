import React, { useState, useEffect } from "react"
import { StyleSheet, View } from "react-native"
import { useTranslation } from "react-i18next"
import { useRoute, RouteProp } from "@react-navigation/native"
import {
  SaleLiveTab,
  UpcomingTab,
} from "./features/tokenizationRequests/components"
import { EstatesTab } from "./features/estates/components"
import { OffersTab } from "./features/offers/components"
import { TabSelector } from "./components"
// import { CustomPressable } from "../../components"
// import chevronsUpDownIcon from "assets/images/ic_chevrons_up_down.png"
// import { viewStyles } from "../../config/styles"
import Colors from "../../config/colors"
import { RootStackParamList } from "src/navigatorV2/rootStackParamsList"

type EstateScreenRouteProp = RouteProp<RootStackParamList, "EstateTab">

const EstateScreen: React.FC = () => {
  const { t } = useTranslation()
  const [activeTabIndex, setActiveTabIndex] = useState(0)
  const route = useRoute<EstateScreenRouteProp>()

  // Get tabIndex from route params if available
  useEffect(() => {
    if (route.params?.params?.tabIndex !== undefined) {
      setActiveTabIndex(route.params.params.tabIndex)
    }
  }, [route.params])

  const tabTitles = [t("Sale Live"), t("Upcoming"), t("Tokenized"), t("Offers")]

  // const handleSelectFilter = () => {
  //   // TODO, handle later
  // }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <TabSelector
            tabTitles={tabTitles}
            selectedIndex={activeTabIndex}
            setTabIndex={setActiveTabIndex}
          />
          {/* <CustomPressable onPress={handleSelectFilter} enabled={false}>
            <View style={styles.filter}>
              <Text style={styles.recentFilter}>{t("Recent")}</Text>
              <Image
                source={chevronsUpDownIcon}
                style={[viewStyles.size14Icon]}
              />
            </View>
          </CustomPressable> */}
        </View>
        <View style={styles.tabContent}>
          {activeTabIndex === 0 && <SaleLiveTab />}
          {activeTabIndex === 1 && <UpcomingTab />}
          {activeTabIndex === 2 && <EstatesTab />}
          {activeTabIndex === 3 && <OffersTab />}
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
    backgroundColor: Colors.PalleteBlack,
  },
  tabContent: {
    flex: 1,
    marginTop: 16,
    backgroundColor: "transparent",
  },
  header: {
    marginBottom: 16,
    marginEnd: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  filter: {
    marginEnd: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 8,
    padding: 4,
    borderColor: Colors.Neutral900,
  },
  recentFilter: {
    color: Colors.white,
    marginEnd: 8,
  },
})

export default EstateScreen
