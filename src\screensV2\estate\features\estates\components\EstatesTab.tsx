import React, { useCallback } from "react"
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  View,
} from "react-native"
import { useEstates } from "../hooks"
import { Estate } from "src/api"
import Colors from "src/config/colors"
import { EmptyView } from "components"
import { ResultsHeader } from "src/screensV2/estate/components"
import { GridTokenizedRenderItem } from "src/screensV2/shared/components"
import { calculateGridItemDimensions } from "src/utils/layout"

const { itemWidth, itemSpacing } = calculateGridItemDimensions({
  numColumns: 2,
})

const EstatesTab: React.FC = () => {
  const { estates, isLoadingEstates, refreshEstates } = useEstates()
  // const [selectedLocation, setSelectedLocation] = useState(false)
  // const [selectedAcreage, setSelectedAcreage] = useState(false)
  // const [selectedPrice, setSelectedPrice] = useState(false)

  const handleRefresh = useCallback(() => {
    refreshEstates()
  }, [refreshEstates])

  const renderItem = useCallback(
    (item: Estate) => (
      <GridTokenizedRenderItem
        estate={item}
        key={item.id}
        style={styles.itemContainer}
      />
    ),
    []
  )

  // const handleLocationFilter = () => {
  //   setSelectedLocation(!selectedLocation)
  // }

  // const handleAcreageFilter = () => {
  //   setSelectedAcreage(!selectedAcreage)
  // }

  // const handlePriceFilter = () => {
  //   setSelectedPrice(!selectedPrice)
  // }

  return (
    <View style={styles.container}>
      {isLoadingEstates ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      ) : estates.length === 0 ? (
        <EmptyView />
      ) : (
        <>
          {/* <FilterButtons
            onPressLocation={handleLocationFilter}
            onPressAcreage={handleAcreageFilter}
            onPressPrice={handlePriceFilter}
            selectedLocation={selectedLocation}
            selectedAcreage={selectedAcreage}
            selectedPrice={selectedPrice}
          /> */}
          <ResultsHeader count={estates.length} />
          <FlatList
            data={estates}
            renderItem={({ item }) => renderItem(item)}
            keyExtractor={(item) => item.id.toString()}
            numColumns={2}
            contentContainerStyle={styles.flatListContent}
            columnWrapperStyle={styles.columnWrapper}
            refreshControl={
              <RefreshControl
                refreshing={isLoadingEstates}
                onRefresh={handleRefresh}
                colors={[Colors.primary]}
              />
            }
          />
        </>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  flatListContent: {
    paddingTop: 8,
  },
  columnWrapper: {
    justifyContent: "space-between",
    marginBottom: 8,
    gap: itemSpacing,
  },
  itemContainer: {
    width: itemWidth,
  },
})

export default EstatesTab
