import React, { useCallback } from "react"
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  View,
} from "react-native"
import { useOffers } from "../hooks"
import { MarketplaceOffer } from "src/api"
import Colors from "src/config/colors"
import { EmptyView } from "components"
import {
  // FilterButtons,
  MarketplaceOfferGridItem,
  ResultsHeader,
} from "src/screensV2/estate/components"
import { calculateGridItemDimensions } from "src/utils/layout"
import { MarketplaceOfferState } from "src/api"

const { itemWidth, itemSpacing } = calculateGridItemDimensions({
  numColumns: 2,
})

const OffersTab: React.FC = () => {
  const { offers, isLoadingOffers, refreshOffers } = useOffers(
    MarketplaceOfferState.SELLING
  )
  // const [selectedLocation, setSelectedLocation] = useState(false)
  // const [selectedAcreage, setSelectedAcreage] = useState(false)
  // const [selectedPrice, setSelectedPrice] = useState(false)

  const handleRefresh = useCallback(() => {
    refreshOffers()
  }, [refreshOffers])

  const renderItem = useCallback(
    (item: MarketplaceOffer) => (
      <MarketplaceOfferGridItem
        item={item}
        key={item.id}
        style={styles.itemContainer}
      />
    ),
    []
  )

  // const handleLocationFilter = () => {
  //   setSelectedLocation(!selectedLocation)
  // }

  // const handleAcreageFilter = () => {
  //   setSelectedAcreage(!selectedAcreage)
  // }

  // const handlePriceFilter = () => {
  //   setSelectedPrice(!selectedPrice)
  // }

  return (
    <View style={styles.container}>
      {isLoadingOffers ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      ) : offers.length === 0 ? (
        <EmptyView />
      ) : (
        <>
          {/* <FilterButtons
            onPressLocation={handleLocationFilter}
            onPressAcreage={handleAcreageFilter}
            onPressPrice={handlePriceFilter}
            selectedLocation={selectedLocation}
            selectedAcreage={selectedAcreage}
            selectedPrice={selectedPrice}
          /> */}
          <ResultsHeader count={offers.length} />
          <FlatList
            data={offers}
            renderItem={({ item }) => renderItem(item)}
            keyExtractor={(item) => item.id.toString()}
            numColumns={2}
            contentContainerStyle={styles.flatListContent}
            columnWrapperStyle={styles.columnWrapper}
            refreshControl={
              <RefreshControl
                refreshing={isLoadingOffers}
                onRefresh={handleRefresh}
                colors={[Colors.primary]}
              />
            }
          />
        </>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  flatListContent: {
    paddingTop: 8,
  },
  columnWrapper: {
    justifyContent: "space-between",
    marginBottom: 8,
    gap: itemSpacing,
  },
  itemContainer: {
    width: itemWidth,
  },
})

export default OffersTab
