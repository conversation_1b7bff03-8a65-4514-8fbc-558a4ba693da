import { useTokenizationRequests as useTokenizationRequestsQuery } from "../../../shared/hooks"

/**
 * Hook to access tokenization requests data
 */
export const useTokenizationRequests = () => {
  const query = useTokenizationRequestsQuery()

  return {
    tokenizationRequests: query.data || [],
    isLoadingTokenizationRequests: query.isLoading,
    tokenizationRequestsError: query.error ? String(query.error) : null,
    refreshTokenizationRequests: query.refetch,
  }
}
