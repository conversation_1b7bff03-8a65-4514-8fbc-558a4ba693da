import {
  Estate,
  MarketplaceOffer,
  MortgageTokenLoan,
  TokenizationRequest,
} from "src/api"

/**
 * Common types used across the estate features
 */

export interface EstateStoreState {
  estates: Estate[]
  isLoadingEstates: boolean
  estatesError: string | null
  refreshEstates: () => void
}

export interface TokenizationRequestsStoreState {
  tokenizationRequests: TokenizationRequest[]
  isLoadingTokenizationRequests: boolean
  tokenizationRequestsError: string | null
  refreshTokenizationRequests: () => void
}

export interface OffersStoreState {
  offers: MarketplaceOffer[]
  isLoadingOffers: boolean
  offersError: string | null
  refreshOffers: () => void
}

export interface LoansStoreState {
  loans: MortgageTokenLoan[]
  isLoadingLoans: boolean
  loansError: string | null
  refreshLoans: () => void
}
