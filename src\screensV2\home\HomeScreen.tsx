import React from "react"
import HomeView from "./HomeView"
import { useHomeData } from "./hooks/useHomeData"
import { Background, SimpleLoadingView } from "src/componentsv2"
import { HomeContext } from "./context/HomeContext"
import { EmptyView } from "../../components"

const HomeScreen: React.FC = () => {
  const { sections, isLoading, isAllHasNoData, isAllError } = useHomeData()

  const renderContent = () => {
    if (isLoading) {
      return <SimpleLoadingView visible={true} />
    }
    if (isAllError || isAllHasNoData) {
      return <EmptyView />
    }
    return <HomeView />
  }

  return (
    <HomeContext.Provider value={{ sections }}>
      <Background>{renderContent()}</Background>
    </HomeContext.Provider>
  )
}

export default HomeScreen
