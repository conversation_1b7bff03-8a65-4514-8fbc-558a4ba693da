import React from "react"
import { Animated, StyleSheet, View } from "react-native"
import Colors from "src/config/colors"

interface AutoProgressBarProps {
  progress: Animated.Value
}

const AutoProgressBar: React.FC<AutoProgressBarProps> = ({ progress }) => {
  return (
    <View style={styles.progressSegment}>
      <Animated.View
        style={[
          styles.progressFill,
          {
            transform: [
              {
                scaleX: progress.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 1],
                }),
              },
            ],
          },
        ]}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  progressSegment: {
    flex: 1,
    height: 2,
    backgroundColor: Colors.opacityWhite15,
    marginHorizontal: 2,
    borderRadius: 999,
    overflow: "hidden",
  },
  progressFill: {
    position: "absolute",
    left: 0,
    top: 0,
    width: "100%",
    height: "100%",
    backgroundColor: Colors.white,
    transformOrigin: "left",
  },
})

export default AutoProgressBar
