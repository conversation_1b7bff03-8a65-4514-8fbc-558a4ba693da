import React from "react"
import { ScrollView, StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { MarketplaceOffer } from "src/api/types/marketplace"
import RecentOffersRenderItem from "./RecentOffersRenderItem"

interface OffersTableProps {
  offers: MarketplaceOffer[]
}

const TableHeader: React.FC = () => {
  const { t } = useTranslation()

  return (
    <View style={styles.tableHeader}>
      <Text style={[styles.headerText, { width: 140 }]}>
        {t("Real Estate")}
      </Text>
      <Text style={styles.headerText}>{t("Price per NFT")}</Text>
      <Text style={styles.headerText}>{t("Total Price")}</Text>
      <Text style={styles.headerText}>{t("Sold")}</Text>
      <Text style={styles.headerText}>{t("Divisible")}</Text>
      <Text style={[styles.headerText, { textAlign: "center" }]}>
        {t("Status")}
      </Text>
      <Text style={[styles.headerText, { textAlign: "center" }]}>
        {t("Created by")}
      </Text>
      <View style={styles.actionColumn} />
    </View>
  )
}

const OffersTable: React.FC<OffersTableProps> = ({ offers }) => {
  return (
    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
      <View>
        <TableHeader />
        {offers.map((offer) => (
          <RecentOffersRenderItem key={offer.id} offer={offer} />
        ))}
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  tableHeader: {
    flexDirection: "row",
    paddingHorizontal: 8,
    marginBottom: 8,
  },
  headerText: {
    ...textStyles.SMedium,
    color: Colors.Neutral500,
    width: 80,
    marginRight: 8,
  },
  actionColumn: {
    width: 80,
  },
})

export default OffersTable
