import React from "react"
import { View } from "react-native"
import { MarketplaceOffer } from "src/api/types/marketplace"
import HeaderBar from "./HeaderBar"
import { useRecentOffers } from "./hooks/useRecentOffers"
import OffersTable from "./OffersTable"

interface RecentOffersSectionProps {
  recentOffers: MarketplaceOffer[]
  title: string
}

const RecentOffersSection: React.FC<RecentOffersSectionProps> = ({
  recentOffers,
  title,
}) => {
  if (recentOffers.length === 0) {
    return null
  }
  const { handleExplore } = useRecentOffers()

  return (
    <View>
      <HeaderBar
        title={title}
        isShowExplore={true}
        onPressExplore={handleExplore}
      />
      <OffersTable offers={recentOffers} />
    </View>
  )
}

export default RecentOffersSection
