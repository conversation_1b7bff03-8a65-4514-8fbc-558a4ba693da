import React from "react"
import { StyleSheet, View, ViewStyle } from "react-native"
import { TokenizationRequest } from "src/api/types/estate"
import { useUpcoming } from "./hooks/useUpcoming"
import HeaderBar from "./HeaderBar"
import { GridUpcomingRenderItem } from "src/screensV2/shared/components"
import { calculateGridItemDimensions } from "src/utils/layout"

const { itemWidth, itemSpacing } = calculateGridItemDimensions({
  numColumns: 2,
})

interface UpcomingSectionProps {
  upcomingRequests: TokenizationRequest[]
  title: string
}

const UpcomingSection: React.FC<UpcomingSectionProps> = ({
  upcomingRequests,
  title,
}) => {
  if (upcomingRequests.length === 0) {
    return null
  }
  const { handleExplore } = useUpcoming()

  return (
    <View>
      <HeaderBar
        title={title}
        isShowExplore={true}
        onPressExplore={handleExplore}
      />

      <GridUpcomingView upcomingRequests={upcomingRequests} />
    </View>
  )
}

interface GridUpcomingViewProps {
  upcomingRequests: TokenizationRequest[]
}

const GridUpcomingView: React.FC<GridUpcomingViewProps> = ({
  upcomingRequests,
}) => {
  return (
    <View style={styles.container}>
      {upcomingRequests.map((item, index) => {
        const itemStyle: ViewStyle = {
          ...styles.itemContainer,
          ...(index % 2 === 0 ? styles.itemMarginEnd : {}),
        }
        return (
          <GridUpcomingRenderItem
            key={item.id}
            tokenizationRequest={item}
            style={itemStyle}
          />
        )
      })}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  itemContainer: {
    width: itemWidth,
  },
  itemMarginEnd: {
    width: itemWidth,
    marginEnd: itemSpacing,
  },
})

export default UpcomingSection
