import { useRef, useState } from "react"

export function useFeatured(viewWidth: number) {
  const flatListRef = useRef<any>(null)
  const [activeIndex, setActiveIndex] = useState(0)

  const handleScroll = (event: any) => {
    const scrollPosition = event.nativeEvent.contentOffset.x
    const index = Math.round(scrollPosition / viewWidth)
    setActiveIndex(index)
  }

  return {
    flatListRef,
    activeIndex,
    handleScroll,
  }
}
