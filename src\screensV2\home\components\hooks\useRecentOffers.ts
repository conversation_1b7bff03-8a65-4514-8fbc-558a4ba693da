import { NavigationProp, useNavigation } from "@react-navigation/native"
import { RootStackParamList } from "src/navigatorV2/rootStackParamsList"
import { ESTATE_TAB, LIST_LANDS } from "src/navigatorV2/routes/RoutesV2"

interface UseRecentOffersReturn {
  handleExplore: () => void
}

export function useRecentOffers(): UseRecentOffersReturn {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>()

  const handleExplore = () => {
    //Index 3 (Offers tab)
    navigation.navigate(ESTATE_TAB, {
      screen: LIST_LANDS,
      params: { tabIndex: 3 },
    })
  }

  return {
    handleExplore,
  }
}
