import { NavigationProp, useNavigation } from "@react-navigation/native"
import { RootStackParamList } from "src/navigatorV2/rootStackParamsList"
import { ESTATE_TAB, LIST_LANDS } from "src/navigatorV2/routes/RoutesV2"
interface UseSaleLiveReturn {
  handleExplore: () => void
}

export function useSaleLive(): UseSaleLiveReturn {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>()

  const handleExplore = () => {
    //Index 0 (Sale Live tab)
    navigation.navigate(ESTATE_TAB, {
      screen: LIST_LANDS,
      params: { tabIndex: 0 },
    })
  }

  return {
    handleExplore,
  }
}
