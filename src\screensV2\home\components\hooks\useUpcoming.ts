import { NavigationProp, useNavigation } from "@react-navigation/native"
import { RootStackParamList } from "src/navigatorV2/rootStackParamsList"
import { ESTATE_TAB, LIST_LANDS } from "src/navigatorV2/routes/RoutesV2"

interface UseUpcomingReturn {
  handleExplore: () => void
}

export function useUpcoming(): UseUpcomingReturn {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>()

  const handleExplore = () => {
    //Index 1 (Upcoming tab)
    navigation.navigate(ESTATE_TAB, {
      screen: LIST_LANDS,
      params: { tabIndex: 1 },
    })
  }

  return {
    handleExplore,
  }
}
