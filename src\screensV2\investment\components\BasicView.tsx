import React from "react"
import {
  View,
  Text,
  StyleSheet,
  Image,
  ImageBackground,
  Linking,
} from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useInvestmentContext } from "../context/InvestmentContext"
import icBrik from "assets/imagesV2/ic_brik.png"
import icX from "assets/imagesV2/ic_x.png"
import icBookText from "assets/imagesV2/ic_book_text.png"
import bgInvestment from "assets/imagesV2/bg_investment.png"
import { IconButton } from "src/componentsv2/Button"

const BasicView: React.FC = () => {
  const { t } = useTranslation()
  const { description } = useInvestmentContext()

  return (
    <ImageBackground source={bgInvestment} style={styles.background}>
      <View style={styles.container}>
        <Image source={icBrik} style={styles.icon} />
        <Text style={styles.title}>{t("Briky Land Tokens")}</Text>
        <Text style={styles.description}>{description}</Text>
        <View style={styles.row}>
          <IconButton
            style={styles.bookText}
            onPress={() => {
              Linking.openURL("https://briky-capital.gitbook.io/briky-capital")
            }}
            icon={<Image source={icBookText} style={viewStyles.size12Icon} />}
          />
          <IconButton
            style={styles.x}
            onPress={() => {
              Linking.openURL("https://x.com/BrikyFinance")
            }}
            icon={<Image source={icX} style={viewStyles.size12Icon} />}
          />
        </View>
      </View>
    </ImageBackground>
  )
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
    resizeMode: "cover",
    justifyContent: "center",
  },
  container: {
    paddingHorizontal: 16,
    paddingTop: 24,
    marginBottom: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
  },
  title: {
    ...textStyles.LSemiBold,
    marginBottom: 6,
  },
  content: {
    gap: 8,
  },
  description: {
    ...textStyles.SRegular,
    color: Colors.Neutral400,
  },
  viewMore: {
    marginTop: 4,
    ...textStyles.SSemiBold,
    color: Colors.Secondary300,
  },
  icon: {
    ...viewStyles.size32Icon,
    marginBottom: 10,
  },
  bookText: {
    padding: 6,
  },
  x: {
    marginStart: 4,
    padding: 6,
  },
})

export default BasicView
