import React, { useEffect } from "react"
import { View, StyleSheet, FlatList } from "react-native"
import { useInvestmentContext } from "../context/InvestmentContext"
import InvestmentRoundItem from "./InvestmentRoundItemView"
import { InvestmentRoundState } from "src/api/types/oracle"

const InvestmentRoundsView: React.FC = () => {
  const { investmentRounds, setSelectedRound, selectedRound } =
    useInvestmentContext()

  useEffect(() => {
    if (selectedRound === undefined) {
      const selectedInitRound = investmentRounds.find(
        (round) =>
          round.state === InvestmentRoundState.FINISHED ||
          InvestmentRoundState.PROGRESSING
      )?.round
      if (selectedInitRound !== undefined) {
        setSelectedRound(selectedInitRound)
      }
    }
  }, [selectedRound, investmentRounds, setSelectedRound])

  return (
    <View style={styles.container}>
      <FlatList
        data={investmentRounds}
        renderItem={({ item }) => (
          <InvestmentRoundItem investmentRound={item} />
        )}
        keyExtractor={(item, index) => `${item.round}-${index}`}
        scrollEnabled={false}
        contentContainerStyle={styles.listContent}
        numColumns={2}
        columnWrapperStyle={styles.columnWrapper}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  listContent: {
    gap: 12,
  },
  columnWrapper: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignItems: "flex-start",
    marginBottom: 12,
  },
})

export default InvestmentRoundsView
