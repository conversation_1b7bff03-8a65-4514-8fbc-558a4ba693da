import React from "react"
import {
  View,
  Text,
  StyleSheet,
  ImageRequireSource,
  Image,
  ViewStyle,
} from "react-native"
import { formatCurrency, shortenAddress } from "src/utils"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { useInvestmentContext } from "src/screensV2/investment/context/InvestmentContext"
import { useTranslation } from "react-i18next"
import { Investment } from "src/api"
import icCommonAvar from "assets/imagesV2/ic_common_avatar.png"
import icBrik from "assets/imagesV2/ic_brik.png"

interface ItemViewProps {
  icon: ImageRequireSource
  value: string
  style?: ViewStyle
  valueColor?: string
}

interface InvestorViewProps {
  investment: Investment
}

const ItemView: React.FC<ItemViewProps> = ({
  icon,
  value,
  style,
  valueColor = Colors.PalleteWhite,
}) => {
  return (
    <View style={[styles.holder, style]}>
      <Image source={icon} style={viewStyles.size10Icon} />
      <Text style={[styles.value, { color: valueColor }]}>{value}</Text>
    </View>
  )
}

const InvestorBasicItemView: React.FC<InvestorViewProps> = ({ investment }) => {
  const { t } = useTranslation()
  const { selectedRound, investmentRounds } = useInvestmentContext()
  const selectedInvestmentRound = investmentRounds.find(
    (round) => round.round === selectedRound
  )
  const tokenForSale = selectedInvestmentRound?.tokenAllocation || 0
  const { totalAmount, unlockedAmount } = investment
  const percentHolding = tokenForSale
    ? ((totalAmount * 100) / tokenForSale).toFixed(2) + "%"
    : "- %"

  return (
    <View style={styles.basicContainer}>
      <ItemView
        icon={icCommonAvar}
        value={shortenAddress(investment.address)}
        valueColor={Colors.Secondary300}
      />
      <ItemView
        icon={icBrik}
        value={`${formatCurrency(totalAmount)} ${t("BRIK")}`}
      />
      <ItemView
        icon={icBrik}
        value={`${formatCurrency(unlockedAmount)} ${t("BRIK")}`}
      />
      <Text style={[styles.percentHolding, styles.textRight]}>
        {percentHolding}
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  holder: {
    flexDirection: "row",
    alignItems: "center",
    flex: 2,
  },
  percentHolding: {
    ...textStyles.SSemiBold,
    flex: 1,
  },
  textCenter: {
    textAlign: "center",
  },
  value: {
    ...textStyles.SSemiBold,
    marginStart: 2,
  },
  textRight: {
    textAlign: "right",
  },
  basicContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
})

export default InvestorBasicItemView
