import React from "react"
import { View, Text, StyleSheet, Image, ViewStyle } from "react-native"
import { useTranslation } from "react-i18next"
import { Investment } from "src/api"
import { useInvestmentContext } from "src/screensV2/investment/context/InvestmentContext"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { formatCurrency, shortenAddress } from "src/utils"
import icBrik from "assets/imagesV2/ic_brik.png"
import icSeedAvatar from "assets/imagesV2/ic_seed_avatar.png"

interface InvestorViewProps {
  investment: Investment
}

interface RowItemViewProps {
  style?: ViewStyle
  label: string
  value: string
  showBrikIcon?: boolean
}

const RowItemView: React.FC<RowItemViewProps> = ({
  style,
  label,
  value,
  showBrikIcon = true,
}) => {
  return (
    <View style={[styles.rowContainer, style]}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.row}>
        <Text style={textStyles.SMedium}>{value}</Text>
        {showBrikIcon && <Image source={icBrik} style={styles.icon} />}
      </View>
    </View>
  )
}

const InvestorSeedRoundItemView: React.FC<InvestorViewProps> = ({
  investment,
}) => {
  const { t } = useTranslation()
  const { selectedRound, investmentRounds } = useInvestmentContext()
  const selectedInvestmentRound = investmentRounds.find(
    (round) => round.round === selectedRound
  )
  const tokenForSale = selectedInvestmentRound?.tokenAllocation || 0
  const { totalAmount, investorName, address, officeName, withdrawnAmount } =
    investment
  const percentHolding = tokenForSale
    ? ((totalAmount * 100) / tokenForSale).toFixed(2) + "%"
    : "0%"

  return (
    <View style={styles.container}>
      <View style={styles.rowSpaceBetween}>
        <View style={styles.row}>
          <Image source={icSeedAvatar} style={viewStyles.size10Icon} />
          <Text style={styles.address}>{shortenAddress(address)}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>{t("Percent Holding")}</Text>
          <Text style={styles.percentHolding}>{percentHolding}</Text>
        </View>
      </View>
      <RowItemView
        label={t("Representative")}
        value={investorName}
        showBrikIcon={false}
      />
      <RowItemView
        label={t("Office")}
        value={officeName}
        showBrikIcon={false}
      />
      <View style={styles.row}>
        <RowItemView
          style={{ marginEnd: 6 }}
          label={t("Total Allocation")}
          value={formatCurrency(totalAmount)}
        />
        <RowItemView
          label={t("Withdrawn")}
          value={formatCurrency(withdrawnAmount)}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
  },
  address: {
    ...textStyles.SSemiBold,
    color: Colors.Secondary300,
    marginStart: 4,
  },
  amount: {
    ...textStyles.MSemiBold,
    flex: 1,
    textAlign: "center",
    color: Colors.PalleteBlack,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  rowContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    paddingHorizontal: 8,
    borderRadius: 4,
    paddingVertical: 6,
    marginBottom: 6,
    flex: 1,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  label: {
    ...textStyles.SMedium,
    color: Colors.Neutral500,
  },
  icon: {
    ...viewStyles.size10Icon,
    marginStart: 2,
  },
  percentHolding: {
    ...textStyles.SMedium,
    marginStart: 4,
  },
})

export default InvestorSeedRoundItemView
