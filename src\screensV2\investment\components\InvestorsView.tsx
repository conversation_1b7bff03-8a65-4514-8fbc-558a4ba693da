import React from "react"
import { View, Text, StyleSheet } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import { Investment, InvestmentRoundType } from "src/api"
import SimplePagingLocalList from "src/componentsv2/simplepaginglist/SimplePagingLocalList"
import { useInvestmentContext } from "src/screensV2/investment/context/InvestmentContext"
import Colors from "src/config/colors"
import InvestorBasicItemView from "./InvestorBasicItemView"
import InvestorSeedRoundItemView from "./InvestorSeedRoundItemView"

interface InvestorsViewProps {
  investments: Investment[]
}

const InvestorsBasicHeaderView: React.FC = () => {
  const { t } = useTranslation()
  return (
    <View style={styles.labelRow}>
      <Text style={styles.headerLabel}>{t("Wallet Address")}</Text>
      <Text style={[styles.headerLabel, styles.textCenter]}>
        {t("Total Allocation")}
      </Text>
      <Text style={[styles.headerLabel, styles.textCenter]}>
        {t("Unlocked")}
      </Text>
      <Text style={[styles.headerLabel, styles.flex1, styles.textRight]}>
        {t("Percent Holding")}
      </Text>
    </View>
  )
}

const InvestorsBasicView: React.FC<InvestorsViewProps> = ({ investments }) => {
  const renderItem = (item: Investment) => {
    return <InvestorBasicItemView investment={item} />
  }

  return (
    <View style={styles.container}>
      <InvestorsBasicHeaderView />
      <SimplePagingLocalList<Investment>
        data={investments}
        renderItem={renderItem}
        keyExtractor={(_, index) => index.toString()}
        scrollEnabled={false}
      />
    </View>
  )
}

const InvestorsSeedRoundView: React.FC<InvestorsViewProps> = ({
  investments,
}) => {
  const renderItem = (item: Investment) => {
    return <InvestorSeedRoundItemView investment={item} />
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <SimplePagingLocalList<Investment>
          data={investments}
          renderItem={renderItem}
          keyExtractor={(_, index) => index.toString()}
          scrollEnabled={false}
        />
      </View>
    </View>
  )
}

const InvestorsView: React.FC = () => {
  const { selectedRound, investmentRounds } = useInvestmentContext()
  const investments =
    investmentRounds.find((round) => round.round === selectedRound)
      ?.investments || []
  const { t } = useTranslation()
  return (
    <View>
      <Text style={styles.title}>{t("Investor List")}</Text>
      {selectedRound === InvestmentRoundType.SEED ? (
        <InvestorsSeedRoundView investments={investments} />
      ) : (
        <InvestorsBasicView investments={investments} />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  title: {
    ...textStyles.LSemiBold,
    marginBottom: 16,
  },
  content: {
    gap: 8,
  },
  labelRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  headerLabel: {
    ...textStyles.XSMedium,
    flex: 2,
    color: Colors.Neutral500,
  },
  flex1: {
    flex: 1,
  },
  textRight: {
    textAlign: "right",
  },
  textCenter: {
    textAlign: "center",
  },
})

export default InvestorsView
