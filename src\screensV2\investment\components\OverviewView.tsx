import React from "react"
import { View, Text, StyleSheet, Image, ViewStyle } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import StatItemView from "./StatItemView"
import { useInvestmentContext } from "src/screensV2/investment/context/InvestmentContext"
import { InvestmentRoundType } from "src/api"
import InvestorsView from "./InvestorsView"
import { Divider } from "react-native-paper"
import { PROVIDER_CHAIN_ID } from "src/config/env"
import { formatCurrency } from "src/utils"
import icCirleCheck from "assets/imagesV2/ic_circle_check.png"

const MAX_TOKEN_SUPPLY = 2e10

interface VestingInfoViewProps {
  vestingPercentage: string
  vestingTime: string
  cliff: string
}

interface VestingItemViewProps {
  style?: ViewStyle
  label: string
  value: string
}

const VestingItemView: React.FC<VestingItemViewProps> = ({
  style,
  label,
  value,
}) => {
  return (
    <View style={[styles.vestingBox, style]}>
      <Text style={styles.vestingLabel}>{label}</Text>
      <Text style={textStyles.MSemiBold}>{value}</Text>
    </View>
  )
}

const VestingInfoView: React.FC<VestingInfoViewProps> = ({
  vestingPercentage,
  vestingTime,
  cliff,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.vestingContainer}>
      <Text style={styles.vestingTitle}>{t("Vesting Information")}</Text>
      <View style={styles.vestingRow}>
        <VestingItemView
          label={t("Vesting percentage")}
          value={`${vestingPercentage}%`}
        />
        <VestingItemView
          style={styles.marginHorizontal6}
          label={t("Cliff")}
          value={`${vestingTime} ${Number(vestingTime) > 1 ? t("months") : t("month")}`}
        />
        <VestingItemView
          label={t("Vesting percentage")}
          value={`${cliff} ${Number(cliff) > 1 ? t("months") : t("month")}`}
        />
      </View>
    </View>
  )
}

const OverviewView: React.FC = () => {
  const { t } = useTranslation()
  const { selectedRound, roundNameMap, investmentRounds } =
    useInvestmentContext()
  const selectedRoundName = selectedRound && roundNameMap[selectedRound]
  const selectedInvestmentRound = investmentRounds.find(
    (round) => round.round === selectedRound
  )

  const chainNamesMap: Record<string, string> = {
    "56": t("BNB Chain"),
    "97": t("BNB Chain Testnet"),
  }

  const {
    tokenAllocation,
    vestingPercentage,
    investments,
    sold,
    unlocked,
    cliffDurationInMonths,
    vestingDurationInMonths,
  } = selectedInvestmentRound || {}

  const chainName = chainNamesMap[PROVIDER_CHAIN_ID] || t("Unknown Chain")
  const tokenForSale = tokenAllocation || 0
  const percentageSupply = ((tokenForSale / MAX_TOKEN_SUPPLY) * 100).toFixed(2)
  const totalFundRaised = selectedInvestmentRound?.totalFundRaised || 0
  const totalContributors = investments?.length || 0
  const raiseProgressPercent = (((sold || 0) / tokenForSale) * 100).toFixed(2)

  const unlockedPercentage = (((unlocked || 0) / tokenForSale) * 100).toFixed(2)

  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Text style={textStyles.MSemiBold}>
          {t("Round Overview", { round: selectedRoundName })}
        </Text>
        <View style={styles.badge}>
          <Image source={icCirleCheck} style={viewStyles.size8Icon} />
          <Text style={styles.badgeText}>{t("Sale Completed")}</Text>
        </View>
      </View>
      <View style={styles.statsGrid}>
        <StatItemView
          label={t("Network")}
          isShowNetworkIcon={true}
          value={chainName}
        />
        <StatItemView
          label={t("Tokens for Sale")}
          value={`${formatCurrency(tokenForSale)} BRIK`}
        />
        <StatItemView
          label={t("Percentage Supply")}
          value={`${percentageSupply}%`}
          showBorder={false}
        />
      </View>
      <View style={styles.statsGrid}>
        <StatItemView
          label={t("Total Fund Raised")}
          value={`${formatCurrency(totalFundRaised)} USDT`}
        />
        <StatItemView label={t("Unlocked")} value={`${unlockedPercentage}%`} />
        <StatItemView
          label={t("Total Contributor")}
          value={totalContributors.toString()}
          showBorder={false}
        />
      </View>
      <View style={styles.rowSpaceBetween}>
        <Text style={styles.progressLabel}>{t("Raise Process")}</Text>
        <Text style={textStyles.XSBold}>{raiseProgressPercent}%</Text>
      </View>
      <View style={styles.progressBarBg}>
        <View
          style={[
            styles.progressBarFill,
            { width: `${Number(raiseProgressPercent)}%` },
          ]}
        />
      </View>
      {selectedRound === InvestmentRoundType.SEED && (
        <VestingInfoView
          vestingPercentage={(vestingPercentage || 0).toString()}
          vestingTime={(vestingDurationInMonths || 0).toString()}
          cliff={(cliffDurationInMonths || 0).toString()}
        />
      )}
      <Divider style={styles.divider} />
      <InvestorsView />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    marginHorizontal: 16,
    padding: 8,
    backgroundColor: Colors.Neutral950,
    marginBottom: 16,
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  badge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.Success500,
    borderRadius: 999,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  badgeText: {
    ...textStyles.XSSemiBold,
    marginStart: 4,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 12,
    paddingVertical: 2,
  },
  networkRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  progressSection: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  progressLabel: {
    ...textStyles.XSMedium,
    color: Colors.Neutral500,
  },
  progressBarBg: {
    flex: 1,
    height: 10,
    borderRadius: 8,
    padding: 2,
    backgroundColor: Colors.PalleteBlack,
    overflow: "hidden",
  },
  progressBarFill: {
    height: "100%",
    backgroundColor: Colors.Success300,
    borderRadius: 8,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  vestingContainer: {
    marginTop: 12,
  },
  vestingTitle: {
    ...textStyles.MSemiBold,
    marginBottom: 12,
  },
  vestingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  vestingBox: {
    flex: 1,
    backgroundColor: Colors.Neutral900,
    borderRadius: 4,
    padding: 6,
    alignItems: "flex-start",
  },
  vestingLabel: {
    ...textStyles.XSMedium,
    color: Colors.Neutral400,
    marginBottom: 4,
  },
  divider: {
    height: 1,
    width: "100%",
    backgroundColor: Colors.Neutral900,
    marginVertical: 16,
  },
  marginHorizontal6: {
    marginHorizontal: 6,
  },
})

export default OverviewView
