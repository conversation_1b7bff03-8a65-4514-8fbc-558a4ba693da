import React from "react"
import {
  InvestmentSectionItem,
  InvestmentSectionType,
} from "src/screensV2/investment/types"
import BasicSection from "./sections/BasicSection"
import OverviewSection from "./sections/OverviewSection"
import InvestmentRoundsSection from "./sections/InvestmentRoundsSection"
import YourInvestments from "./YourInvestment"

interface SectionRendererProps {
  item: InvestmentSectionItem
}

const SectionRenderer: React.FC<SectionRendererProps> = ({ item }) => {
  switch (item.type) {
    case InvestmentSectionType.BASIC:
      return <BasicSection />
    case InvestmentSectionType.OVERVIEW:
      return <OverviewSection />
    case InvestmentSectionType.INVESTMENT_ROUNDS:
      return <InvestmentRoundsSection />
    case InvestmentSectionType.YOUR_INVESTMENTS:
      return <YourInvestments />
    default:
      return null
  }
}

export default SectionRenderer
