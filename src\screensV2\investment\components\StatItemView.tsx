import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import icBnb from "assets/images/ic_bnb.png"

interface StatItemViewProps {
  isShowNetworkIcon?: boolean
  label: string
  value: string
  showBorder?: boolean
}

const StatItemView: React.FC<StatItemViewProps> = ({
  isShowNetworkIcon = false,
  label,
  value,
  showBorder = true,
}) => {
  return (
    <View style={[styles.statBox, showBorder && styles.statBoxBorder]}>
      <Text style={styles.statLabel}>{label}</Text>
      <View style={styles.row}>
        {isShowNetworkIcon && (
          <Image source={icBnb} style={styles.networkIcon} />
        )}
        <Text style={styles.statValue}>{value}</Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  statBox: {
    flex: 1,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  statBoxBorder: {
    borderRightWidth: 1,
    marginEnd: 8,
    borderRightColor: Colors.Neutral900,
  },
  statLabel: {
    ...textStyles.XSMedium,
    color: Colors.Neutral500,
    marginBottom: 4,
  },
  statValue: {
    ...textStyles.MSemiBold,
    flexShrink: 1,
  },
  networkIcon: {
    ...viewStyles.size16Icon,
    marginEnd: 4,
  },
})

export default StatItemView
