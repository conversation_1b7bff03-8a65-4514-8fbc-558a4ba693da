import React from "react"
import { View, Text, StyleSheet } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useInvestmentContext } from "../context/InvestmentContext"
import { useAccount } from "wagmi"
import InvestmentDistributionView from "./InvestmentDistributionView"
import CurrentUnlockBalance from "./currentunlockbalance"

const YourInvestments: React.FC = () => {
  const { t } = useTranslation()

  const { address } = useAccount()
  const { selectedRound, investmentRounds } = useInvestmentContext()
  const selectedInvestmentRound = investmentRounds.find(
    (round) => round.round === selectedRound
  )
  const yourInvestment = selectedInvestmentRound?.investments.find(
    (investment) => investment.address.toLowerCase() === address?.toLowerCase()
  )

  return (
    <View style={styles.cardContainer}>
      <Text style={[textStyles.MSemiBold, styles.headerTitle]}>
        {t("Your Investments")}
      </Text>

      {yourInvestment?.distributions?.map((distribution, index) => (
        <InvestmentDistributionView
          distribution={distribution}
          index={index}
          key={distribution.distributionId}
        />
      ))}

      <CurrentUnlockBalance
        initialAmount={yourInvestment?.unlockedAmount || 0}
        distributions={yourInvestment?.distributions || []}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: Colors.Neutral950,
    borderRadius: 8,
    padding: 8,
    marginHorizontal: 16,
  },
  headerTitle: {
    color: Colors.PalleteWhite,
    marginBottom: 12,
  },
  investmentsListContainer: {
    marginBottom: 16,
  },
  investmentItemContainer: {
    borderColor: Colors.Neutral900,
    borderWidth: 1,
    borderRadius: 6,
    padding: 8,
    justifyContent: "center",
  },
  investmentItemContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  investmentDetails: {
    gap: 8,
  },
  investmentOrderLabel: {
    color: Colors.Neutral500,
  },
  brikAmountContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  brikAmountText: {
    color: Colors.PalleteWhite,
  },
  unlockedTag: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.Success900,
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 8,
    gap: 4,
    height: 32,
  },
  unlockedTagText: {
    color: Colors.Success300,
  },
})

export default YourInvestments
