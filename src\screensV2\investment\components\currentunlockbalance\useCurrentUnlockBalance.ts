import { useEffect, useRef, useState } from "react"
import { Distribution } from "src/api/types/oracle"

interface UseCurrentUnlockBalanceProps {
  initialAmount: number
  distributions: Distribution[]
}

export const useCurrentUnlockBalance = ({
  initialAmount,
  distributions,
}: UseCurrentUnlockBalanceProps) => {
  const [currentUnlocked, setCurrentUnlocked] = useState(initialAmount || 0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const calculateIncrementPerSecond = () => {
    if (!distributions) return 0

    const currentTimeInSeconds = Math.floor(Date.now() / 1000)

    return distributions.reduce((totalIncrement, distribution) => {
      const { distributeAt = 0, vestingEndsAtInSeconds, amount } = distribution
      // If current time >= vestingEndsAtInSeconds, increment is 0
      if (currentTimeInSeconds >= vestingEndsAtInSeconds) {
        return totalIncrement
      }

      const vestingTime = vestingEndsAtInSeconds - distributeAt

      const distributionIncrement = amount / vestingTime
      return totalIncrement + distributionIncrement
    }, 0)
  }

  // Reset currentUnlocked when initialAmount or distributions change
  useEffect(() => {
    const currentTimeInSeconds = Math.floor(Date.now() / 1000)
    const currentUnlockedAmount = distributions.reduce(
      (total, distribution) => {
        const {
          distributeAt = 0,
          vestingEndsAtInSeconds,
          amount,
        } = distribution
        const currentDistributionUnlocked =
          (amount * (currentTimeInSeconds - distributeAt)) /
          (vestingEndsAtInSeconds - distributeAt)
        return total + Math.max(currentDistributionUnlocked, 0)
      },
      0
    )
    setCurrentUnlocked(currentUnlockedAmount)
  }, [initialAmount, distributions])

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      // Recalculate incrementPerSecond each time since current time changes
      const currentIncrementPerSecond = calculateIncrementPerSecond()
      setCurrentUnlocked((prev) => prev + currentIncrementPerSecond)
    }, 1000)

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
    }
  }, [distributions])

  return {
    currentUnlocked,
    distributionIds: distributions.map((d) => d.distributionId),
  }
}
