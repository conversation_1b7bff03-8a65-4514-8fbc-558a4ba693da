import { useWriteContract } from "wagmi"
import { useEthersProvider } from "src/hooks/useEthersProvider"
import { useTranslation } from "react-i18next"
import { useState } from "react"
import { CONTRACT_ADDRESS_ESTATE_FORGER } from "src/config/env"
import { estateForgerAbi } from "src/api/contracts/estate-forger"
import { showSuccessWhenCallContract, showError } from "src/utils/toast"
// import Logger from "src/utils/logger"
import { driptributorAbi } from "../../../../api/contracts/driptributor"

// const logger = new Logger().createLogger("WithdrawButton")

const useWithdrawAll = () => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()
  const onWithdrawAll = async (ids: number[]) => {
    console.info("Bắt đầu thực hiện withdraw với ids:", ids)
    const displayIds = ids.map((id) => BigInt(id))
    console.info("Chuyển đổi ids sang BigInt:", displayIds)
    if (!ethersProvider) {
      console.error("ethersProvider không tồn tại")
      return
    }
    try {
      setIsLoading(true)
      console.info("Gọi writeContractAsync với các tham số:", {
        address: "******************************************",
        abi: driptributorAbi,
        functionName: "withdraw",
        args: [displayIds],
      })

      const txHash = await writeContractAsync({
        address: "******************************************",
        abi: driptributorAbi,
        functionName: "withdraw",
        args: [displayIds],
      })
      console.info("Gửi transaction thành công, txHash:", txHash)
      console.info("Đợi xác nhận transaction...")

      const receipt = await ethersProvider.waitForTransaction(txHash)
      console.log(receipt.transactionHash)
      if (receipt.status === 1) {
        console.info("Withdraw thành công")
        showSuccessWhenCallContract(
          t("Distribution withdraw success") +
            ". " +
            t("Data will be updated in few seconds")
        )
      } else {
        console.log(receipt.transactionHash)
        console.error("Distribution withdraw failed1" + receipt.toString())
        throw new Error(t("Distribution e" + receipt.transactionHash))
      }
    } catch (e: any) {
      console.error("Có lỗi xảy ra khi withdraw:", e)
      console.error("Withdraw error:", e)
      showError(e.message)
    } finally {
      setIsLoading(false)
      console.info("Kết thúc quá trình withdraw")
    }
  }

  return { onWithdrawAll, isLoading }
}

export default useWithdrawAll
