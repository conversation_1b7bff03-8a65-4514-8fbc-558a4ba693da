import { InvestmentRoundType, InvestmentRound } from "src/api"
import { InvestmentSectionItem } from "../types"

export interface InvestmentContextState {
  sections: InvestmentSectionItem[]
  isInvestmentLoading: boolean
  investmentRounds: InvestmentRound[]
  investmentError: Error | null
  description: string
  selectedRound: InvestmentRoundType | undefined
  roundNameMap: Record<string, string>
  setSelectedRound: (round: InvestmentRoundType) => void
}
