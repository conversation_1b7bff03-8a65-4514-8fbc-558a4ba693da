import { useState } from "react"
import { InvestmentContextState } from "src/screensV2/investment/context/types"
import useOracle from "./useInvestment"
import { InvestmentRoundType } from "src/api"
import { useInvestmentSections } from "./useInvestmentSections"
import { useTranslation } from "react-i18next"

export const useInvestmentProvider = (): InvestmentContextState => {
  const { t } = useTranslation()
  // TODO: fake
  const description = t(
    "Briky Token is the core asset of the Briky Capital ecosystem—representing ownership, utility, and participation in on-chain real estate investment activities."
  )
  const roundNameMap: Record<string, string> = {
    BACKER: t("Backer Round"),
    SEED: t("Seed Round"),
    PRIVATE_SALE_1: t("Private Sale 1"),
    PRIVATE_SALE_2: t("Private Sale 2"),
    PUBLIC_SALE: t("Public Sale"),
  }

  const { oracle, isInvestmentLoading, oracleError } = useOracle()
  const investmentRounds = oracle?.investment.rounds || []
  const [selectedRound, setSelectedRound] = useState<
    InvestmentRoundType | undefined
  >(undefined)
  const { sections } = useInvestmentSections()

  return {
    sections,
    isInvestmentLoading,
    investmentError: oracleError,
    investmentRounds,
    description,
    selectedRound,
    roundNameMap,
    setSelectedRound,
  }
}
