import React, { useContext, useMemo } from "react"
import { StyleSheet, View, SectionList, ScrollView } from "react-native"
import { useTranslation } from "react-i18next"
import { Background } from "src/components"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import ActivitiesTab from "./components/ActivitiesTab"
import ApplicationsTab from "./components/ApplicationsTab"
import MyEstatesTab from "./components/MyEstatesTab"
import OffersTab from "./components/OffersTab"
import ProfileHeader from "./components/ProfileHeader"
import TokenizationRequestsTab from "./components/TokenizationRequestsTab"
import { useProfileContext } from "./context/ProfileContext"
import { TabSelector } from "../estate/components"
import ConnectWalletView from "./components/ConnectWalletView"
import { AuthContext } from "../../context/AuthContext"

// Define item types for our SectionList
interface SectionItem {
  type: string
  id?: string
  componentType?: string
}

// Define our section types
interface ProfileSection {
  type: string
  data: SectionItem[]
}

const ProfileView: React.FC = () => {
  const { t } = useTranslation()

  // Sử dụng tab index từ context
  const { activeTabIndex, setActiveTabIndex } = useProfileContext()

  // Danh sách các tab - order must match ProfileTabIndex enum
  const tabTitles = [
    t("Estate"),
    t("Application"),
    t("Tokenization Requests"),
    t("Activities"),
    t("Offer"),
  ]

  const { isAuthenticated } = useContext(AuthContext)

  const tabsData = useMemo(() => {
    return [
      { type: "tabContent", id: "estates", componentType: "MyEstatesTab" },
      {
        type: "tabContent",
        id: "applications",
        componentType: "ApplicationsTab",
      },
      {
        type: "tabContent",
        id: "tokenization",
        componentType: "TokenizationRequestsTab",
      },
      { type: "tabContent", id: "activities", componentType: "ActivitiesTab" },
      { type: "tabContent", id: "offers", componentType: "OffersTab" },
    ]
  }, [])

  // Generate sections for our SectionList
  const sections = useMemo(() => {
    return [
      {
        type: "header",
        data: [{ type: "header" }],
      },
      {
        type: "tabContent",
        data: [tabsData[activeTabIndex]],
      },
    ] as ProfileSection[]
  }, [activeTabIndex, tabsData])

  // UI khi chưa đăng nhập
  if (!isAuthenticated) {
    return (
      <Background>
        <ConnectWalletView />
      </Background>
    )
  }

  // Render different content based on section type
  const renderSectionHeader = ({ section }: { section: ProfileSection }) => {
    if (section.type === "tabContent") {
      return (
        <ScrollView style={styles.tabSelectorContainer} horizontal>
          <TabSelector
            tabTitles={tabTitles}
            selectedIndex={activeTabIndex}
            setTabIndex={setActiveTabIndex}
          />
        </ScrollView>
      )
    }
    return null
  }

  const renderItem = ({
    item,
    section,
  }: {
    item: SectionItem
    section: ProfileSection
  }) => {
    if (section.type === "header") {
      return <ProfileHeader />
    }

    // For tab content items - render the component based on componentType
    if (section.type === "tabContent") {
      return (
        <View style={styles.tabContent}>
          {item.componentType === "MyEstatesTab" && <MyEstatesTab />}
          {item.componentType === "ApplicationsTab" && <ApplicationsTab />}
          {item.componentType === "TokenizationRequestsTab" && (
            <TokenizationRequestsTab />
          )}
          {item.componentType === "ActivitiesTab" && <ActivitiesTab />}
          {item.componentType === "OffersTab" && <OffersTab />}
        </View>
      )
    }

    return null
  }

  // UI khi đã đăng nhập with SectionList
  return (
    <SectionList
      style={styles.container}
      sections={sections}
      renderItem={renderItem}
      renderSectionHeader={renderSectionHeader}
      stickySectionHeadersEnabled={false}
      keyExtractor={(item, index) =>
        item.type === "header" ? `header-${index}` : `tab-${item.id || index}`
      }
    />
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.PalleteBlack,
  },
  content: {
    flex: 1,
  },
  headerContainer: {
    backgroundColor: Colors.PalleteBlack,
  },
  tabSelectorContainer: {
    paddingHorizontal: 16,
    backgroundColor: Colors.PalleteBlack,
  },
  tabContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: Colors.Neutral950,
  },
  tabButton: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    position: "relative",
  },
  tabText: {
    ...textStyles.MSemiBold,
    color: Colors.Neutral500,
    textAlign: "center",
  },
  selectedTabText: {
    color: Colors.Primary500,
  },
  activeIndicator: {
    position: "absolute",
    bottom: 0,
    left: 4,
    right: 4,
    height: 1,
    backgroundColor: Colors.Primary500,
  },
  tabContent: {
    flex: 1,
    backgroundColor: Colors.PalleteBlack,
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
  },
  // Styles cho UI chưa đăng nhập
  notLoggedInContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  notLoggedInTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.white,
    marginBottom: 12,
    textAlign: "center",
  },
  notLoggedInDescription: {
    fontSize: 16,
    color: Colors.Neutral500,
    marginBottom: 40,
    textAlign: "center",
  },
  loginImage: {
    width: 200,
    height: 200,
    marginBottom: 40,
  },
  loginButton: {
    width: "80%",
    marginTop: 20,
  },
})

export default ProfileView
