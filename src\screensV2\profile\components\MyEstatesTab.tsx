import React from "react"
import { StyleSheet, View, ActivityIndicator } from "react-native"
import Colors from "src/config/colors"
import { useProfileContext } from "../context/ProfileContext"
import ListItemCard from "src/screensV2/shared/components/ListItemCard"
import { estateToListItem } from "src/screensV2/shared/adapters/itemAdapters"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { getMyEstates, MyEstate } from "src/api"
import SimplePagingList from "src/componentsv2/simplepaginglist/SimplePagingList"

const MyEstatesTab: React.FC = () => {
  const { isLoading, address } = useProfileContext()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.Primary500} />
      </View>
    )
  }

  // Navigate to detail screen
  const handleNavigate = (route: string, params: any) => {
    navigation.navigate(route, params)
  }

  const renderItem = (item: MyEstate) => {
    const listItemProps = estateToListItem(item, handleNavigate)
    return <ListItemCard {...listItemProps} />
  }

  return (
    <View style={styles.container}>
      <SimplePagingList<MyEstate>
        getData={(params) => getMyEstates({ ...params, account: address })}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
        queryKeys={["myEstates"]}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  resultCount: {
    color: Colors.white,
    marginVertical: 16,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 20,
    backgroundColor: Colors.PalleteBlack,
  },
  columnWrapper: {
    justifyContent: "space-between",
  },
  gridItem: {
    width: "48%",
    marginBottom: 16,
  },
})

export default MyEstatesTab
