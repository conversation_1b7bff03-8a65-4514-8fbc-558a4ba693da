import React from "react"
import { StyleSheet, View, ActivityIndicator } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useProfileContext } from "../context/ProfileContext"
import OfferItem from "../../estateDetail/components/components/tabs/OfferItem"
import { getMortgageOffers, MarketplaceOffer } from "src/api"
import SimplePagingList from "src/componentsv2/simplepaginglist/SimplePagingList"

const OffersTab: React.FC = () => {
  const { isLoading } = useProfileContext()

  // Hiển thị loading khi đang tải dữ liệu
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.Primary500} />
      </View>
    )
  }

  const renderItem = ({ item }: { item: MarketplaceOffer }) => (
    <OfferItem offer={item} />
  )

  return (
    <View style={styles.container}>
      <SimplePagingList<MarketplaceOffer>
        getData={(params) => getMortgageOffers(params)}
        renderItem={(item) => renderItem({ item })}
        keyExtractor={(item) => item.id}
        scrollEnabled={false}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  resultCount: {
    ...textStyles.bodyM,
    color: Colors.white,
    marginVertical: 16,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingBottom: 20,
    backgroundColor: Colors.PalleteBlack,
  },
  columnWrapper: {
    justifyContent: "space-between",
  },
  gridItem: {
    width: "48%",
    marginBottom: 16,
  },
})

export default OffersTab
