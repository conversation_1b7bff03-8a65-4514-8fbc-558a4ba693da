import { createContext, useContext } from "react"
import { User } from "src/api"

// Context để chia sẻ dữ liệu profile giữa các component
export interface ProfileContextType {
  address?: string
  profile: User | null
  isLoading: boolean
  // Tab hiện tại
  activeTabIndex: number
  setActiveTabIndex: (index: number) => void
  isGuest: boolean
}

export const ProfileContext = createContext<ProfileContextType>({
  address: undefined,
  profile: null,
  isLoading: false,
  activeTabIndex: 0,
  setActiveTabIndex: () => {},
  isGuest: false,
})

export const useProfileContext = () => useContext(ProfileContext)
