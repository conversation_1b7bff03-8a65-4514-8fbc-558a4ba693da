import React, { useCallback } from "react"
import {
  Image,
  Linking,
  StyleSheet,
  View,
  ImageSourcePropType,
  FlatList,
} from "react-native"
import { Background, TopBar } from "src/componentsv2"
import { SelectItemView } from "src/screensV2/shared/components"
import { useTranslation } from "react-i18next"
import twitterIcon from "assets/imagesV2/ic_x.png"
// import telegramIcon from "assets/imagesV2/ic_telegram.png"
// import zaloIcon from "assets/imagesV2/ic_zalo.png"
// import facebookIcon from "assets/imagesV2/ic_facebook.png"
// import discordIcon from "assets/imagesV2/ic_discord.png"
import { viewStyles } from "src/config/styles"

interface ContactItemProps {
  title: string
  icon: ImageSourcePropType
  onPress: () => void
}

const ContactView: React.FC = () => {
  const { t } = useTranslation()
  // const isVietnamese = i18n.language === "vi"

  const onSelectX = () => {
    void Linking.openURL("https://x.com/brikyfinance?s=21")
  }

  // const onSelectTelegram = () => {
  //   void Linking.openURL("https://t.me/briky_land_community")
  // }

  // const onSelectDiscord = () => {
  //   void Linking.openURL("https://discord.gg/adnxzg3A")
  // }

  // const onSelectZalo = () => {
  //   if (isVietnamese) {
  //     void Linking.openURL("https://zalo.me/g/aflavt390")
  //   } else {
  //     void Linking.openURL("https://zalo.me/g/gysvxx420")
  //   }
  // }

  // const onSelectFacebook = () => {
  //   if (isVietnamese) {
  //     void Linking.openURL(
  //       "https://www.facebook.com/profile.php?id=61571968620170"
  //     )
  //   } else {
  //     void Linking.openURL("https://www.facebook.com/brikyland")
  //   }
  // }

  const contactItems: ContactItemProps[] = [
    // {
    //   title: t("Facebook"),
    //   icon: facebookIcon,
    //   onPress: onSelectFacebook,
    // },
    // {
    //   title: t("Telegram"),
    //   icon: telegramIcon,
    //   onPress: onSelectTelegram,
    // },
    // {
    //   title: t("Discord"),
    //   icon: discordIcon,
    //   onPress: onSelectDiscord,
    // },
    {
      title: t("X"),
      icon: twitterIcon,
      onPress: onSelectX,
    },
    // {
    //   title: t("Zalo"),
    //   icon: zaloIcon,
    //   onPress: onSelectZalo,
    // },
  ]

  const renderItem = useCallback(
    ({ item }: { item: ContactItemProps }) => (
      <SelectItemView
        title={item.title}
        icon={<Image source={item.icon} style={viewStyles.size18Icon} />}
        onPress={item.onPress}
        showNextIcon={false}
      />
    ),
    []
  )

  return (
    <Background>
      <TopBar enableBack={true} title={t("Contact")} />
      <View style={styles.container}>
        <FlatList
          style={styles.list}
          data={contactItems}
          renderItem={renderItem}
          keyExtractor={(item) => item.title}
        />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  list: {
    marginTop: 16,
  },
})

export { ContactView }
