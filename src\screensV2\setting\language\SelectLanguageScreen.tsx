import React from "react"
import { RouteProp } from "@react-navigation/native"
import { RootStackParamList } from "navigation"
import { SelectLanguageView } from "./SelectLanguageView"

interface SelectLanguageScreenProps {
  route: RouteProp<RootStackParamList, "SelectLanguage">
}

const SelectLanguageScreen: React.FC<SelectLanguageScreenProps> = ({
  route,
}) => {
  const { selectedLanguage } = route.params
  return <SelectLanguageView selectedLanguage={selectedLanguage} />
}

export default SelectLanguageScreen
