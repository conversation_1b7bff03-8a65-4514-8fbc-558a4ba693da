import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { TopBar, Background } from "src/componentsv2"
import { useTranslation } from "react-i18next"
import { Branch, Office } from "../types"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { ScrollView } from "react-native-gesture-handler"
import { Divider } from "react-native-paper"
import { useOfficesData } from "../hooks/useOfficesData"

interface BranchViewProps {
  branch: Branch
}

const BranchView: React.FC<BranchViewProps> = ({ branch }) => {
  return (
    <Text style={styles.branchItem}>
      {branch.headOfficeName && (
        <Text style={styles.branchName}>{`${branch.headOfficeName}: `}</Text>
      )}
      {branch.address && (
        <Text
          style={
            branch.headOfficeName ? styles.branchAddress : styles.branchName
          }
        >
          {branch.address}
        </Text>
      )}
    </Text>
  )
}

interface OfficeViewProps {
  office: Office
}

const OfficeView: React.FC<OfficeViewProps> = ({ office }) => {
  const { nationalName, companyName, branches } = office

  return (
    <View>
      <Text style={styles.title}>{nationalName}</Text>
      <Text style={styles.officeName}>{companyName.toUpperCase()}</Text>
      <View style={styles.row}>
        <View style={styles.listBranch}>
          {(branches || []).map((branch, index) => (
            <BranchView key={index} branch={branch} />
          ))}
        </View>
      </View>
    </View>
  )
}

const OfficesView: React.FC = () => {
  const { t } = useTranslation()

  const officesData = useOfficesData()

  return (
    <Background>
      <View style={styles.container}>
        <TopBar enableBack={true} title={t("Offices")} />
        <ScrollView style={styles.scrollViewContent}>
          {officesData.map((office, index) => (
            <View key={index}>
              <OfficeView office={office} key={index} />
              {index < officesData.length - 1 && (
                <Divider
                  style={[styles.divider, { marginTop: 16, marginBottom: 24 }]}
                />
              )}
            </View>
          ))}
        </ScrollView>
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  scrollViewContent: {
    flexGrow: 1,
    marginTop: 16,
    paddingBottom: 50,
  },
  title: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
    marginBottom: 6,
  },
  row: {
    flexDirection: "row",
  },
  officeName: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
    marginBottom: 12,
  },
  branchName: {
    ...textStyles.MMedium,
    color: Colors.Neutral300,
  },
  listBranch: {
    flex: 1,
  },
  divider: {
    height: 1,
    width: "100%",
    backgroundColor: Colors.Neutral900,
  },
  branchItem: {
    marginBottom: 8,
  },
  branchAddress: {
    ...textStyles.MMedium,
    color: Colors.Neutral300,
  },
})

export { OfficesView }
