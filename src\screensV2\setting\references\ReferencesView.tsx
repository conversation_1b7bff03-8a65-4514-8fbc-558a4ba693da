import React, { useCallback } from "react"
import {
  Image,
  Linking,
  StyleSheet,
  View,
  FlatList,
  ImageSourcePropType,
} from "react-native"
import { Background, TopBar } from "src/componentsv2"
import { SelectItemView } from "src/screensV2/shared/components"
import { BRIKY_URLS } from "src/config/consts"
import { useTranslation } from "react-i18next"
import documentIcon from "assets/imagesV2/ic_file_text.png"
import whitePaperIcon from "assets/imagesV2/ic_white_paper.png"
import verifiedIcon from "assets/imagesV2/ic_pocket.png"
import { viewStyles } from "src/config/styles"

interface ReferenceItemProps {
  title: string
  icon: ImageSourcePropType
  onPress: () => void
}

const ReferencesView: React.FC = () => {
  const openUrl = useCallback((url: string) => {
    void Linking.openURL(url)
  }, [])
  const { t } = useTranslation()

  const onSelectDocument = useCallback(() => {
    openUrl(BRIKY_URLS.documentation)
  }, [openUrl])

  const onSelectWhitePaper = useCallback(() => {
    openUrl(BRIKY_URLS.whitepaper)
  }, [openUrl])

  const onSelectSecurityReport = useCallback(() => {
    openUrl(BRIKY_URLS.auditReport)
  }, [openUrl])

  const referenceItems: ReferenceItemProps[] = [
    {
      title: t("Documentation"),
      icon: documentIcon,
      onPress: onSelectDocument,
    },
    {
      title: t("White paper"),
      icon: whitePaperIcon,
      onPress: onSelectWhitePaper,
    },
    {
      title: t("Vertichain Security Report"),
      icon: verifiedIcon,
      onPress: onSelectSecurityReport,
    },
  ]

  const renderItem = useCallback(
    ({ item }: { item: ReferenceItemProps }) => (
      <SelectItemView
        title={item.title}
        icon={<Image source={item.icon} style={viewStyles.size18Icon} />}
        onPress={item.onPress}
        showNextIcon={false}
      />
    ),
    []
  )

  return (
    <Background>
      <TopBar enableBack={true} title={t("References")} />
      <View style={styles.container}>
        <FlatList
          style={styles.list}
          data={referenceItems}
          renderItem={renderItem}
          keyExtractor={(item) => item.title}
        />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  list: {
    marginTop: 16,
  },
})

export { ReferencesView }
