import React, { useState } from "react"
import { StyleSheet } from "react-native"
import { MarketplaceOffer } from "src/api"
import { useTranslation } from "react-i18next"
import { useAccount } from "wagmi"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import BuyNftsModal from "../modal/BuyNftsModal"
import { PrimaryButton } from "src/componentsv2/Button"

interface Props {
  offer: Omit<MarketplaceOffer, "seller">
  title?: string
  buttonWidth?: number
  buttonHeight?: number
  borderRadius?: number
}

const BuyNftsButton: React.FC<Props> = ({
  offer,
  buttonWidth,
  buttonHeight,
  borderRadius,
  title,
}) => {
  const { address } = useAccount()
  const { t } = useTranslation()
  const [isShow, setIsShow] = useState(false)

  if (!address) return null

  return (
    <>
      <PrimaryButton
        title={title ? title : t("Buy")}
        onPress={() => setIsShow(true)}
        width={buttonWidth}
        height={buttonHeight}
        borderRadius={borderRadius}
        color={Colors.Primary500}
        textStyle={styles.takeOfferButton}
        enabled={Boolean(address)}
      />

      <BuyNftsModal
        visible={isShow}
        offer={offer}
        onClose={() => {
          setIsShow(false)
        }}
      />
    </>
  )
}

const styles = StyleSheet.create({
  modalContent: {
    padding: 12,
    backgroundColor: Colors.PalleteWhite,
  },
  takeOfferButton: {
    ...textStyles.SMedium,
    textAlign: "center",
  },
})

export default BuyNftsButton
