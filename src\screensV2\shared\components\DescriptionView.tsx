import React from "react"
import { View, Text, StyleSheet } from "react-native"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { CollapseWithHeaderView } from "src/componentsv2/CollapseWithHeaderView"
import { useTranslation } from "react-i18next"
import icAlignLeft from "assets/imagesV2/ic_align_left.png"

interface DescriptionViewProps {
  description: string
}

const DescriptionView: React.FC<DescriptionViewProps> = ({ description }) => {
  const { t } = useTranslation()
  const isEmpty = !description || description.trim() === ""

  return (
    <View style={styles.container}>
      <CollapseWithHeaderView
        title={t("Description")}
        emptyTitle={t("No description")}
        headerIconUri={icAlignLeft}
        showEmpty={isEmpty}
      >
        {!isEmpty && <Text style={styles.description}>{description}</Text>}
      </CollapseWithHeaderView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    width: "100%",
  },
  description: {
    marginTop: 16,
    width: "100%",
    ...textStyles.MRegular,
    color: Colors.Neutral300,
  },
})

export default DescriptionView
