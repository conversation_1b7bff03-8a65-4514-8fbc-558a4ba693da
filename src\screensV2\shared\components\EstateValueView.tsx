import React from "react"
import { View, Text, StyleSheet } from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { TokenizationRequestState } from "src/api"
import { formatCurrencyByDecimals, fixedPointMultiply } from "src/utils/format"

interface EstateValueViewProps {
  unitPrice: string
  totalSupply: string
  soldAmount?: string
  maxSellingAmount: string
  decimals: number
  tokenSymbol: string
  state: TokenizationRequestState
}

const EstateValueView: React.FC<EstateValueViewProps> = ({
  unitPrice,
  totalSupply,
  soldAmount = "0",
  maxSellingAmount,
  decimals,
  tokenSymbol,
  state,
}) => {
  const { t } = useTranslation()

  const estateValue = fixedPointMultiply(
    BigInt(unitPrice),
    BigInt(totalSupply),
    decimals
  )
  const displayEstateValue = `${formatCurrencyByDecimals(
    estateValue.toString(),
    decimals
  )} ${tokenSymbol}`

  const formattedUnitPrice = formatCurrencyByDecimals(unitPrice, decimals)
  const formattedMaxSellingAmount = formatCurrencyByDecimals(
    maxSellingAmount,
    decimals
  )
  const availableAmount = formatCurrencyByDecimals(
    (BigInt(maxSellingAmount) - BigInt(soldAmount)).toString(),
    decimals
  )

  const displayAvailableNFTs =
    state !== TokenizationRequestState.CONFIRMED
      ? `${t("Available")} ${availableAmount}/${formattedMaxSellingAmount} ${t("NFTs")} ${t("at price")} ${formattedUnitPrice} ${tokenSymbol}`
      : `${formattedUnitPrice} ${tokenSymbol} ${t("per")} ${t("NFT")}`

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t("Estate Value")}</Text>
      <View style={styles.valueContainer}>
        <Text style={styles.value}>{displayEstateValue}</Text>
        <Text style={styles.availableText}>{displayAvailableNFTs}</Text>
      </View>
    </View>
  )
}
const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.Neutral950,
    gap: 12,
  },
  title: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
  },
  valueContainer: {
    gap: 6,
    width: "100%",
  },
  value: {
    ...textStyles.size3XLMedium,
    color: Colors.PalleteWhite,
    width: "100%",
  },
  availableText: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
    width: "100%",
  },
})

export default EstateValueView
