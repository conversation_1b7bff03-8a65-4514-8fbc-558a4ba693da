import React, { useMemo } from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { EstateTokenAreaUnit, TokenizationRequest } from "src/api/types/estate"
import BaseGridViewItem from "./BaseGridViewItem"
import { formatCurrencyByDecimals } from "src/utils/format"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"
import { useNavigateRequestDetail } from "src/screensV2/shared/hooks/useNavigateRequestDetail"

interface GridSaleLiveRenderItemProps {
  tokenizationRequest: TokenizationRequest
  style?: ViewStyle
}

const GridSaleLiveRenderItem: React.FC<GridSaleLiveRenderItemProps> = ({
  tokenizationRequest,
  style,
}) => {
  const { t } = useTranslation()

  const metadata = tokenizationRequest.metadata

  const {
    metadata: { name, locale_detail, area } = {
      name: "",
      locale_detail: { zone: "" },
      area: { area: 0, unit: EstateTokenAreaUnit.SQM },
    },
    imageUrl = "",
  } = metadata || {}

  const {
    unitPrice = "0",
    soldAmount = "0",
    maxSellingAmount = "0",
    totalSupply = "0",
    decimals = 0,
    currency,
  } = tokenizationRequest

  const availableNFTs = useMemo(() => {
    return formatCurrencyByDecimals(
      (BigInt(maxSellingAmount) - BigInt(soldAmount)).toString(),
      decimals
    )
  }, [maxSellingAmount, soldAmount, decimals])

  const formattedPrice = useMemo(() => {
    return formatCurrencyByDecimals(unitPrice, decimals)
  }, [unitPrice, decimals])

  const { tokenSymbol } = useCurrencies(currency)
  const buttonText = useMemo(() => {
    return `${t("Available")} ${availableNFTs} ${t("NFTs")} ${t("at price")} ${formattedPrice} ${tokenSymbol}`
  }, [t, availableNFTs, formattedPrice, tokenSymbol])

  const handleNavigate = useNavigateRequestDetail({ tokenizationRequest })

  const renderButton = () => (
    <View style={[styles.actionButton, { backgroundColor: Colors.Primary500 }]}>
      <Text style={styles.saleLiveButtonText}>{buttonText}</Text>
    </View>
  )

  return (
    <View style={style}>
      <BaseGridViewItem
        onPress={handleNavigate}
        name={name}
        imageUrl={imageUrl}
        zone={locale_detail?.zone || ""}
        area={area}
        totalSupply={totalSupply}
        decimals={decimals}
        buttonView={renderButton()}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  actionButton: {
    borderRadius: 4,
    padding: 6,
    gap: 6,
    alignItems: "center",
  },
  saleLiveButtonText: {
    ...textStyles.SBold,
    color: Colors.PalleteBlack,
    textAlign: "center",
  },
})

export default GridSaleLiveRenderItem
