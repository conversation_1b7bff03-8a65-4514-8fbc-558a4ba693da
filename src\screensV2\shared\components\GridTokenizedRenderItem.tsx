import React, { useMemo } from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { Estate, EstateTokenAreaUnit } from "src/api/types/estate"
import BaseGridViewItem from "./BaseGridViewItem"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import * as Routes from "src/navigatorV2/routes/RoutesV2"

interface GridTokenizedRenderItemProps {
  estate: Estate
  style?: ViewStyle
}

const GridTokenizedRenderItem: React.FC<GridTokenizedRenderItemProps> = ({
  estate,
  style,
}) => {
  const { t } = useTranslation()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const metadata = estate.metadata

  const {
    metadata: { name, locale_detail, area } = {
      name: "",
      locale_detail: { zone: "" },
      area: { area: 0, unit: EstateTokenAreaUnit.SQM },
    },
    imageUrl = "",
  } = metadata || {}

  const { totalSupply = "0", decimals = 0 } = estate.tokenizationRequest || {}

  const formattedDate = useMemo(() => {
    return new Date((estate.createAtInSeconds || 0) * 1000).toLocaleDateString()
  }, [estate.createAtInSeconds])

  const buttonText = useMemo(() => {
    return `${t("Tokenized")} · ${formattedDate}`
  }, [t, formattedDate])

  const handlePress = () => {
    navigation.navigate(Routes.ESTATE_DETAIL, { estateId: estate.id })
  }

  const renderButton = () => (
    <View style={[styles.actionButton, { backgroundColor: Colors.Success900 }]}>
      <Text style={styles.tokenizedText}>{buttonText}</Text>
    </View>
  )

  return (
    <View style={style}>
      <BaseGridViewItem
        onPress={handlePress}
        name={name}
        imageUrl={imageUrl}
        zone={locale_detail?.zone || ""}
        area={area}
        totalSupply={totalSupply}
        decimals={decimals}
        buttonView={renderButton()}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  actionButton: {
    borderRadius: 4,
    padding: 6,
    gap: 6,
    alignItems: "center",
  },
  tokenizedText: {
    ...textStyles.SBold,
    color: Colors.Success500,
    textAlign: "center",
  },
})

export default GridTokenizedRenderItem
