import React, { useMemo } from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { EstateTokenAreaUnit, TokenizationRequest } from "src/api/types/estate"
import BaseGridViewItem from "./BaseGridViewItem"
import { formatCurrencyByDecimals } from "src/utils/format"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"
import { useNavigateRequestDetail } from "src/screensV2/shared/hooks/useNavigateRequestDetail"
import { getTimeUpcomingToBeListed } from "src/utils/timeExt"
interface GridUpcomingRenderItemProps {
  tokenizationRequest: TokenizationRequest
  style?: ViewStyle
}

const GridUpcomingRenderItem: React.FC<GridUpcomingRenderItemProps> = ({
  tokenizationRequest,
  style,
}) => {
  const { t } = useTranslation()

  const metadata = tokenizationRequest.metadata

  const {
    metadata: { name, locale_detail, area } = {
      name: "",
      locale_detail: { zone: "" },
      area: { area: 0, unit: EstateTokenAreaUnit.SQM },
    },
    imageUrl = "",
  } = metadata || {}

  const {
    unitPrice = "0",
    decimals = 0,
    currency,
    publicSaleEndsAtInSeconds,
  } = tokenizationRequest

  const formattedPrice = useMemo(() => {
    return formatCurrencyByDecimals(unitPrice, decimals)
  }, [unitPrice, decimals])

  const { tokenSymbol } = useCurrencies(currency)
  const daysLeft = getTimeUpcomingToBeListed(publicSaleEndsAtInSeconds)

  const buttonText = useMemo(() => {
    return `${formattedPrice} ${tokenSymbol} ${t("per")} ${t("NFT")}\n${t("List in")} ${daysLeft} ${t("days")}`
  }, [t, formattedPrice, tokenSymbol, daysLeft])

  const handleNavigate = useNavigateRequestDetail({ tokenizationRequest })

  const renderButton = () => (
    <View style={[styles.actionButton, { backgroundColor: Colors.Neutral900 }]}>
      <Text style={styles.upcomingButtonText}>{buttonText}</Text>
    </View>
  )

  return (
    <View style={style}>
      <BaseGridViewItem
        onPress={handleNavigate}
        name={name}
        imageUrl={imageUrl}
        zone={locale_detail?.zone || ""}
        area={area}
        totalSupply={tokenizationRequest.totalSupply}
        decimals={decimals}
        buttonView={renderButton()}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  actionButton: {
    borderRadius: 4,
    padding: 6,
    gap: 6,
    alignItems: "center",
  },
  upcomingButtonText: {
    ...textStyles.SBold,
    color: Colors.Neutral500,
    textAlign: "center",
  },
})

export default GridUpcomingRenderItem
