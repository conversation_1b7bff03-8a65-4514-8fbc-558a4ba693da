import React from "react"
import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  View,
} from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { StyleProp, ImageStyle, TextStyle, ViewStyle } from "react-native"

interface InfoItemProps {
  style?: StyleProp<ViewStyle>
  icon: ImageSourcePropType
  text: string
  iconStyle?: StyleProp<ImageStyle>
  textStyle?: StyleProp<TextStyle>
}

const InfoItem: React.FC<InfoItemProps> = ({
  style,
  icon,
  text,
  iconStyle,
  textStyle,
}) => {
  return (
    <View style={[styles.infoItem, style]}>
      <Image source={icon} style={[viewStyles.size8Icon, iconStyle]} />
      <Text style={[styles.infoText, textStyle]}>{text}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  infoItem: {
    flexDirection: "row",
  },
  infoText: {
    ...textStyles.XSMedium,
    color: Colors.PalleteWhite,
    marginStart: 4,
  },
})

export default InfoItem
