import React from "react"
import { MarketplaceOffer, MarketplaceOfferState } from "src/api"
import { useAccount } from "wagmi"
import BuyNftsButton from "./BuyNftsButton"
import { CancelOfferButton } from "./CancelOfferButton"

interface OfferActionButtonProps {
  offer: MarketplaceOffer
  title?: string
  buttonWidth?: number
  buttonHeight?: number
  borderRadius?: number
}

export const OfferActionButton: React.FC<OfferActionButtonProps> = ({
  offer,
  title,
  buttonWidth,
  buttonHeight,
  borderRadius,
}) => {
  const { address } = useAccount()

  if (offer.state === MarketplaceOfferState.CANCELLED) return null
  return (
    <>
      {address &&
        address.toLowerCase() === offer.seller.address.toLowerCase() &&
        offer.soldAmount !== offer.sellingAmount && (
          <CancelOfferButton
            offer={offer}
            buttonWidth={buttonWidth}
            buttonHeight={buttonHeight}
            borderRadius={borderRadius}
          />
        )}
      {address?.toLowerCase() !== offer.seller.address.toLowerCase() &&
        offer.soldAmount !== offer.sellingAmount && (
          <BuyNftsButton
            offer={offer}
            title={title}
            buttonWidth={buttonWidth}
            buttonHeight={buttonHeight}
            borderRadius={borderRadius}
          />
        )}
    </>
  )
}
