import React from "react"
import {
  StyleSheet,
  Linking,
  View,
  Text,
  Image,
  StyleProp,
  ViewStyle,
} from "react-native"
import { useTranslation } from "react-i18next"
import { CollapseWithHeaderView } from "src/componentsv2/CollapseWithHeaderView"
import icGlobe from "assets/imagesV2/ic_globe.png"
import { shortenAddress } from "src/utils"
import { BSCSCAN_URL } from "src/config/env"
import { useChainId } from "wagmi"
import { ExpandView, CustomPressable } from "../../../components"
import icNetwork from "assets/imagesV2/ic_bsc_network.png"
import { textStyles, viewStyles } from "../../../config/styles"
import Colors from "../../../config/colors"
import icOpen from "assets/imagesV2/ic_open.png"

interface OnChainViewProps {
  requestId: string
  uri: string
}

interface RowItemViewProps {
  label: string
  value: string
  style?: StyleProp<ViewStyle>
  onPress?: () => void
}

const RowItemView: React.FC<RowItemViewProps> = ({
  label,
  value,
  onPress,
  style,
}) => {
  return (
    <View style={[styles.detailItem, style]}>
      <Text style={styles.labelItem}>{label}</Text>
      {onPress ? (
        <CustomPressable style={styles.pressable} onPress={onPress}>
          <Text style={styles.link}>{value}</Text>
          <Image source={icOpen} style={styles.open} />
        </CustomPressable>
      ) : (
        <Text style={styles.valueItem}>{value}</Text>
      )}
    </View>
  )
}

const OnChainView: React.FC<OnChainViewProps> = ({ requestId, uri }) => {
  const { t } = useTranslation()
  const chainId = useChainId()

  const navigateToRequestId = () => {
    void Linking.openURL(uri)
  }
  const navigateToContractAddress = () => {
    const url = `${BSCSCAN_URL}/address/0x9919F4cc203badF636cc7fd6A7823004441e8F0f`
    void Linking.openURL(url)
  }

  const chainNamesMap: Record<number, string> = {
    56: t("Binance Smart Chain"),
    97: t("Binance Smart Chain Testnet"),
  }

  const chainName = chainNamesMap[chainId] || t("Unknown Chain")

  return (
    <View style={styles.container}>
      <CollapseWithHeaderView
        title={t("Onchain")}
        headerIconUri={icGlobe}
        emptyTitle={t("No legal requirement")}
      >
        <View style={styles.content}>
          <RowItemView
            label={t("Request ID")}
            value={requestId}
            style={styles.marginBottom8}
            onPress={navigateToRequestId}
          />
          <RowItemView
            label={t("Contract address")}
            style={styles.marginBottom8}
            value={shortenAddress("0x9919F4cc203badF636cc7fd6A7823004441e8F0f")}
            onPress={navigateToContractAddress}
          />
          <RowItemView
            style={styles.marginBottom8}
            label={t("Token standard")}
            value={t("ERC_1155")}
          />
          <View style={styles.row}>
            <Text style={styles.label}>{t("Network")}</Text>
            <ExpandView />
            <Image source={icNetwork} style={viewStyles.size14Icon} />
            <Text style={styles.value}>{chainName}</Text>
          </View>
        </View>
      </CollapseWithHeaderView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingVertical: 16,
  },
  marginBottom8: {
    marginBottom: 8,
  },
  content: {
    marginTop: 16,
    width: "100%",
  },
  row: {
    flexDirection: "row",
    width: "100%",
    alignItems: "center",
  },
  label: {
    ...textStyles.MRegular,
    color: Colors.Neutral300,
  },
  value: {
    ...textStyles.MRegular,
    color: Colors.PalleteWhite,
    marginStart: 4,
  },
  detailItem: {
    flexDirection: "row",
  },
  labelItem: {
    ...textStyles.MRegular,
    color: Colors.Neutral300,
    flex: 1,
  },
  pressable: {
    flexDirection: "row",
  },
  link: {
    ...textStyles.MRegular,
    color: Colors.Secondary300,
    marginHorizontal: 4,
  },
  valueItem: {
    ...textStyles.MRegular,
    color: Colors.PalleteWhite,
  },
  open: {
    ...viewStyles.size14Icon,
    marginStart: 4,
  },
})

export default OnChainView
