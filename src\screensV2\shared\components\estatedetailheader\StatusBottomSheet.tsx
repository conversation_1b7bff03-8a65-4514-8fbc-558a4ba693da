import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import { BaseBottomSheet } from "src/componentsv2"
import { useStatusBottomSheet } from "./hooks/useStatusBottomSheet"

interface StatusBottomSheetProps {
  visible: boolean
  onClose: () => void
  currentStatus: string
}

const StatusBottomSheet: React.FC<StatusBottomSheetProps> = ({
  visible,
  onClose,
  currentStatus,
}) => {
  const { t } = useTranslation()
  const { getStepConfigs } = useStatusBottomSheet()

  // Get step configurations from the hook
  const stepConfigs = getStepConfigs(currentStatus)

  return (
    <BaseBottomSheet visible={visible} onClose={onClose} title={t("Status")}>
      <View style={{ padding: 12 }}>
        {stepConfigs.map((step, index) => (
          <View key={`step-${index}`}>
            <View
              style={[
                styles.stepItem,
                step.isActive
                  ? {
                      backgroundColor:
                        step.backgroundColor || Colors.Neutral900,
                      borderColor: step.borderColor,
                      borderWidth: 1,
                    }
                  : {
                      backgroundColor: "transparent",
                      borderColor: Colors.Neutral800,
                      borderWidth: 1,
                    },
              ]}
            >
              {step.icon}
              <Text
                style={[
                  styles.stepLabel,
                  step.isActive
                    ? { color: step.textColor || Colors.white }
                    : { color: Colors.Neutral800 },
                ]}
              >
                {step.label}
              </Text>
            </View>
            {index < 3 && (
              <View
                style={{
                  width: 1,
                  height: 16,
                  backgroundColor: Colors.Neutral800,
                  marginStart: 12,
                }}
              />
            )}
          </View>
        ))}
      </View>
    </BaseBottomSheet>
  )
}

const styles = StyleSheet.create({
  stepItem: {
    alignSelf: "flex-start",
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12,
    height: 24,
    paddingHorizontal: 12,
  },
  stepLabel: {
    ...textStyles.SSemiBold,
    marginLeft: 6,
  },
})

export default StatusBottomSheet
