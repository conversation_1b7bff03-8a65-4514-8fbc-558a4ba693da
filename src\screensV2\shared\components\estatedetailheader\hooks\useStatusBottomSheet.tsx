import React from "react"
import { useTranslation } from "react-i18next"
import { Image, View } from "react-native"
import Colors from "src/config/colors"
import iconConfirmed from "assets/imagesV2/ic_confirmed.png"
import iconPending from "assets/imagesV2/ic_pending.png"
import iconSquareUser from "assets/imagesV2/ic_square_user.png"
import iconCircleCheck from "assets/images/ic_circle_check.png"
import iconFileBox from "assets/images/ic_file_box.png"

interface StepConfig {
  label: string
  isActive: boolean
  icon: React.ReactNode
  textColor?: string
  backgroundColor?: string
  borderColor?: string
}

// Hàm xác định bước hiện tại dựa trên trạng thái
const getCurrentStep = (status: string): number => {
  switch (status) {
    case "APPLICATION_VALIDATING":
      return 0
    case "REQUEST_SELLING":
      return 1
    case "REQUEST_TRANSFERRING_OWNERSHIP":
      return 2
    case "REQUEST_CONFIRMED":
      return 3
    case "APPLICATION_CANCELLED":
      return 0
    case "REQUEST_INSUFFICIENT_SOLD_AMOUNT":
      return 1
    case "REQUEST_CANCELLED":
      return 2
    case "REQUEST_EXPIRED":
      return 2
    default:
      return -1
  }
}

// Hàm kiểm tra xem bước có phải là bước đã hoàn thành hay không
const isCompletedStep = (
  stepIndex: number,
  currentStepIndex: number
): boolean => {
  return stepIndex < currentStepIndex
}

const useConfigs = (status: string): StepConfig[] => {
  const { t } = useTranslation()
  const currentStepIndex = getCurrentStep(status)

  const step1 =
    status === "APPLICATION_CANCELLED"
      ? {
          label: t("Cancelled"),
          isActive: true,
          icon: (
            <View
              style={{
                width: 12,
                height: 12,
                backgroundColor: Colors.Danger500,
                borderRadius: 6,
              }}
            />
          ),
          textColor: Colors.white,
          backgroundColor: Colors.Danger800,
          borderColor: Colors.Danger900,
        }
      : {
          label: t("Pending"),
          isActive: status === "APPLICATION_VALIDATING",
          icon: isCompletedStep(0, currentStepIndex) ? (
            <Image
              style={{
                width: 12,
                height: 12,
              }}
              source={iconCircleCheck}
            />
          ) : status === "APPLICATION_VALIDATING" ? (
            <Image
              style={{
                width: 12,
                height: 12,
              }}
              source={iconPending}
            />
          ) : (
            <Image
              style={{
                width: 12,
                height: 12,
              }}
              source={iconPending}
              tintColor={Colors.Neutral800}
            />
          ),
          textColor: Colors.PalleteBlack,
          backgroundColor: Colors.PalleteWhite,
          borderColor: Colors.PalleteWhite,
        }

  const step2 =
    status === "REQUEST_INSUFFICIENT_SOLD_AMOUNT"
      ? {
          label: t("Cancelled"),
          isActive: true,
          icon: (
            <View
              style={{
                width: 12,
                height: 12,
                backgroundColor: Colors.Danger500,
                borderRadius: 6,
              }}
            />
          ),
          textColor: Colors.white,
          backgroundColor: Colors.Danger800,
          borderColor: Colors.Danger900,
        }
      : {
          label: t("Public Sale"),
          isActive: status === "REQUEST_SELLING",
          icon: isCompletedStep(1, currentStepIndex) ? (
            <Image
              style={{
                width: 12,
                height: 12,
              }}
              source={iconCircleCheck}
            />
          ) : status === "REQUEST_SELLING" ? (
            <View
              style={{
                width: 12,
                height: 12,
                backgroundColor: Colors.Success500,
                borderRadius: 6,
              }}
            />
          ) : (
            <View
              style={{
                width: 12,
                height: 12,
                backgroundColor: Colors.Neutral800,
                borderRadius: 6,
              }}
            />
          ),
          textColor: Colors.white,
          backgroundColor: Colors.Success900,
          borderColor: Colors.Success800,
        }

  const step3 =
    status === "REQUEST_EXPIRED"
      ? {
          label: t("Expired"),
          isActive: true,
          icon: (
            <View
              style={{
                width: 12,
                height: 12,
                backgroundColor: Colors.Danger500,
                borderRadius: 6,
              }}
            />
          ),
          textColor: Colors.white,
          backgroundColor: Colors.Danger800,
          borderColor: Colors.Danger900,
        }
      : status === "REQUEST_CANCELLED"
        ? {
            label: t("Cancelled"),
            isActive: true,
            icon: (
              <View
                style={{
                  width: 12,
                  height: 12,
                  backgroundColor: Colors.Danger500,
                  borderRadius: 6,
                }}
              />
            ),
            textColor: Colors.white,
            backgroundColor: Colors.Danger800,
            borderColor: Colors.Danger900,
          }
        : {
            label: t("Transferring Ownership"),
            isActive: status === "REQUEST_TRANSFERRING_OWNERSHIP",
            icon: isCompletedStep(2, currentStepIndex) ? (
              <Image
                style={{
                  width: 12,
                  height: 12,
                }}
                source={iconCircleCheck}
              />
            ) : status === "REQUEST_TRANSFERRING_OWNERSHIP" ? (
              <Image
                style={{
                  width: 12,
                  height: 12,
                }}
                source={iconSquareUser}
              />
            ) : (
              <Image
                style={{
                  width: 12,
                  height: 12,
                }}
                source={iconSquareUser}
                tintColor={Colors.Neutral800}
              />
            ),
            textColor: Colors.white,
            backgroundColor: Colors.Warning900,
            borderColor: Colors.Warning800,
          }

  const step4 = {
    label: t("Tokenized"),
    isActive: status === "REQUEST_CONFIRMED",
    icon:
      status === "REQUEST_CONFIRMED" ? (
        <Image
          style={{
            width: 12,
            height: 12,
          }}
          source={iconConfirmed}
        />
      ) : (
        <Image
          style={{
            width: 12,
            height: 12,
          }}
          source={iconFileBox}
          tintColor={Colors.Neutral800}
        />
      ),
    textColor: Colors.white,
    backgroundColor: Colors.Success600,
    borderColor: Colors.Success300,
  }

  return [step1, step2, step3, step4]
}

const useStatusBottomSheet = () => {
  const getStepConfigs = (status: string) => {
    return useConfigs(status)
  }

  return {
    getStepConfigs,
  }
}

export { useStatusBottomSheet }
