import React, { useCallback } from "react"
import { View, Text, StyleSheet, FlatList, Linking, Image } from "react-native"
import { DocumentFile } from "./useLegalRequirement"
import Colors from "src/config/colors"
import { CustomPressable } from "src/componentsv2"
import icFile from "assets/imagesV2/ic_file.png"
import { textStyles, viewStyles } from "src/config/styles"

interface DocumentsBottomSheetProps {
  documentFiles: DocumentFile[]
}

interface DocumentFileViewProps {
  documentFile: DocumentFile
}

const DocumentFileView: React.FC<DocumentFileViewProps> = ({
  documentFile,
}) => {
  const { fileName, fileUrl } = documentFile
  const handleOpenDocument = () => {
    Linking.openURL(fileUrl)
  }

  return (
    <CustomPressable style={styles.row} onPress={handleOpenDocument}>
      <Image source={icFile} style={viewStyles.size12Icon} />
      <Text style={styles.fileName}>{fileName}</Text>
    </CustomPressable>
  )
}

const DocumentsBottomSheet: React.FC<DocumentsBottomSheetProps> = ({
  documentFiles,
}) => {
  const renderItem = useCallback(
    ({ item }: { item: DocumentFile }) => (
      <DocumentFileView documentFile={item} />
    ),
    []
  )

  return (
    <View style={styles.container}>
      <FlatList
        style={styles.flatList}
        data={documentFiles}
        keyExtractor={(_, index) => index.toString()}
        renderItem={renderItem}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    minHeight: 500,
    margin: 12,
  },
  row: {
    flexDirection: "row",
    paddingVertical: 8,
    paddingStart: 8,
    backgroundColor: Colors.Neutral900,
    alignItems: "center",
    marginBottom: 8,
    gap: 4,
    borderRadius: 4,
  },
  fileName: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
    marginStart: 4,
  },
  flatList: {
    width: "100%",
  },
})

export default DocumentsBottomSheet
