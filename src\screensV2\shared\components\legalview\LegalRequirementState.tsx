import React from "react"
import { Image } from "react-native"
import Colors from "src/config/colors"
import icCircleCheck from "assets/imagesV2/ic_circle_check.png"
import icCircleX from "assets/imagesV2/ic_circle_x.png"
import { viewStyles } from "src/config/styles"

interface LegalRequirementStateProps {
  isCompleted: boolean
}

const LegalRequirementState: React.FC<LegalRequirementStateProps> = ({
  isCompleted,
}) => {
  return (
    <Image
      source={isCompleted ? icCircleCheck : icCircleX}
      style={[
        viewStyles.size12Icon,
        { tintColor: isCompleted ? Colors.Success500 : Colors.Danger500 },
      ]}
    />
  )
}

export default LegalRequirementState
