import { useState } from "react"
import { useTranslation } from "react-i18next"
import {
  getLegalRequirements,
  LandRegistryOfficeType,
  LegalRequirement,
  RequirementType,
} from "src/api"
import { useQuery } from "@tanstack/react-query"
import QueryKeys from "src/config/queryKeys"

export type DisplayLegalRequirement = {
  groupTypeTitle: string
  requirementType: RequirementType
  requirementTypeName: string
  fileUrl: string
  fileName: string
  id: number
  metadataID: number
  issuer: {
    id: number
    address: string
    name: string
    type: LandRegistryOfficeType
  }
}

const initialLegalRequirement = (
  requirementType: RequirementType,
  requirementTypeName: string,
  groupTypeTitle: string
): DisplayLegalRequirement => ({
  groupTypeTitle,
  requirementType,
  requirementTypeName,
  fileUrl: "",
  fileName: "",
  id: 0,
  metadataID: 0,
  issuer: {
    id: 0,
    address: "",
    name: "",
    type: LandRegistryOfficeType.LAWYER,
  },
})

export type LegalRequirementGroup = {
  title: string
  legalRequirements: LegalRequirement[]
}

export type DocumentFile = {
  fileName: string
  fileUrl: string
}

export const useLegalRequirement = (
  metadataId: number,
  tokenMintEventTxHash: string
) => {
  const { t } = useTranslation()

  const [isOpenBottomSheet, setIsOpenBottomSheet] = useState(false)
  const [showingIssuerName, setShowingIssuerName] = useState<string>("")
  const [showingDocumentFiles, setShowingDocumentFiles] = useState<
    DocumentFile[]
  >([])
  const [selectedLegalRequirement, setSelectedLegalRequirement] = useState<
    LegalRequirement | undefined
  >(undefined)

  const initialLegalRequirements = [
    initialLegalRequirement(
      RequirementType.OWNERSHIP_CERTIFICATE,
      t("Certificate of Real Estate Ownership/Use Rights"),
      t("Real Estate Documents")
    ),
    initialLegalRequirement(
      RequirementType.COMPANY_IDENTITY_VERIFICATION,
      t("Company Identity Verification"),
      t("Company Documents")
    ),
    initialLegalRequirement(
      RequirementType.LEGAL_REPRESENTATIVE_VERIFICATION,
      t("Legal Representative Identity Verification in Vietnam"),
      t("Company Documents")
    ),
    initialLegalRequirement(
      RequirementType.PARENT_COMPANY_RELATIONSHIP_VERIFICATION,
      t("Verification of Relationship with Parent Company"),
      t("Company Documents")
    ),
    initialLegalRequirement(
      RequirementType.OWNER_IDENTITY_VERIFICATION,
      t("Property Owner Identity Verification"),
      t("Property Owner Documents")
    ),
    initialLegalRequirement(
      RequirementType.TRANSFER_FROM_OWNER,
      t("Property Transfer from Owner to Company"),
      t("Transfer")
    ),
    initialLegalRequirement(
      RequirementType.PROPERTY_SEALING,
      t("Property Sealing"),
      t("Transfer")
    ),
    initialLegalRequirement(
      RequirementType.LEGAL_ASSESSMENT,
      t("Legal Due Diligence of Property"),
      t("Legal Evaluation")
    ),
    initialLegalRequirement(
      RequirementType.MARITAL_STATUS,
      t("Marital Status of Property Owner"),
      t("Legal Evaluation")
    ),
    initialLegalRequirement(
      RequirementType.ESTATE_VALUATION,
      t("Property Valuation"),
      t("Valuation")
    ),
    initialLegalRequirement(
      RequirementType.INCOME_TAX,
      t("Personal Income Tax"),
      t("Tax Filing")
    ),
    initialLegalRequirement(
      RequirementType.VAT,
      t("Value-Added Tax (VAT)"),
      t("Tax Filing")
    ),
    initialLegalRequirement(
      RequirementType.REGISTRATION_FEE,
      t("Registration Fee"),
      t("Tax Filing")
    ),
    initialLegalRequirement(
      RequirementType.RESOURCE_TAX,
      t("Resource Tax"),
      t("Tax Filing")
    ),
    initialLegalRequirement(
      RequirementType.CERTIFICATE_UPDATE,
      t("Updated Certificate of Land Use Rights"),
      t("Transfer Completion")
    ),
    initialLegalRequirement(
      RequirementType.MORTGAGE_STATUS,
      t("Enable digitization, token minting, payments"),
      t("Digitization Process")
    ),
  ]
  const { data } = useQuery({
    queryKey: [QueryKeys.ESTATE.LEGAL_REQUIREMENTS, metadataId.toString()],
    queryFn: () => getLegalRequirements(`${metadataId}`),
  })
  const realLegalRequirements = data?.list || []

  const legalRequirements = initialLegalRequirements.map((initialItem) => {
    const matchingRealItem = realLegalRequirements.find(
      (realItem) => realItem.requirementType === initialItem.requirementType
    )
    if (initialItem.requirementType === RequirementType.MORTGAGE_STATUS) {
      return { ...initialItem, fileUrl: tokenMintEventTxHash }
    }
    return matchingRealItem && matchingRealItem.fileUrl
      ? { ...initialItem, fileUrl: matchingRealItem.fileUrl }
      : initialItem
  })

  const selectLegalRequirement = (requirementType: RequirementType) => {
    setIsOpenBottomSheet(true)
    const issuerName =
      realLegalRequirements.find(
        (item) => item.requirementType === requirementType
      )?.issuer?.name || ""
    setShowingIssuerName(issuerName)
    const documentFiles = realLegalRequirements
      .filter(
        (item) => item.requirementType === requirementType && item.fileUrl
      )
      .map((item) => {
        return { fileName: item.fileName, fileUrl: item.fileUrl }
      })
    setShowingDocumentFiles(documentFiles)
  }

  return {
    showingIssuerName,
    showingDocumentFiles,
    selectLegalRequirement,
    realLegalRequirements,
    legalRequirements,
    isOpenBottomSheet,
    setIsOpenBottomSheet,
    selectedLegalRequirement,
    setSelectedLegalRequirement,
  }
}

export const updateDocumentFiles = (
  requirementType: RequirementType,
  legalRequirements: LegalRequirement[]
) => {
  const documentFiles = legalRequirements
    .filter((item) => item.requirementType === requirementType && item.fileUrl)
    .map((item) => {
      return { fileName: item.fileName, fileUrl: item.fileUrl }
    })
  return documentFiles
}
