import { useTranslation } from "react-i18next"
import { ImageSourcePropType } from "react-native"
import { TraitType } from "src/api"

import icTag from "assets/imagesV2/ic_tag.png"
import icFloor from "assets/imagesV2/ic_floor.png"
import icDoorClosed from "assets/imagesV2/ic_door_closed.png"
import icBedDouble from "assets/imagesV2/ic_bed_double.png"
import icFire from "assets/imagesV2/ic_fire.png"
import icSofa from "assets/imagesV2/ic_sofa.png"
import icBath from "assets/imagesV2/ic_bath.png"
import icHouses from "assets/imagesV2/ic_houses.png"
import icMountainSnow from "assets/imagesV2/ic_mountain_snow.png"
import icScaling from "assets/imagesV2/ic_scaling.png"
import icMapPin from "assets/imagesV2/ic_map_pin.png"
import icMap from "assets/imagesV2/ic_map.png"
import icCalendarRange from "assets/imagesV2/ic_calendar_range.png"
import icLayers from "assets/imagesV2/ic_layers.png"
import icFileBox from "assets/imagesV2/ic_file_box.png"

interface TraitConfig {
  icon: ImageSourcePropType
  title: string
}

const useTraitConfig = (traitType: string): TraitConfig => {
  const { t } = useTranslation()

  const traitMap: Record<string, { icon: ImageSourcePropType; title: string }> =
    {
      [TraitType.CREDENTIAL_TYPE]: {
        icon: icFileBox,
        title: t("Credential Type"),
      },
      [TraitType.CITY_PROVINCE]: {
        icon: icMapPin,
        title: t("City/Province"),
      },
      [TraitType.DISTRICT]: { icon: icMapPin, title: t("District") },
      [TraitType.WARD]: { icon: icMapPin, title: t("Ward") },
      [TraitType.STREET]: { icon: icMapPin, title: t("Street") },
      [TraitType.ROAD_ACCESS]: { icon: icHouses, title: t("Road Access") },
      [TraitType.BEDROOMS]: { icon: icBedDouble, title: t("Bedrooms") },
      [TraitType.AREA_201_300]: { icon: icCalendarRange, title: t("Area") },
      [TraitType.BUILT_SQM]: { icon: icScaling, title: t("Built SQM") },
      [TraitType.ROOMS]: { icon: icDoorClosed, title: t("Rooms") },
      [TraitType.LIVING_ROOMS]: { icon: icSofa, title: t("Living Rooms") },
      [TraitType.LANDSCAPE]: { icon: icMountainSnow, title: t("Landscape") },
      [TraitType.FLOORS]: { icon: icFloor, title: t("Floors") },
      [TraitType.KITCHENS]: { icon: icFire, title: t("Kitchens") },
      [TraitType.CATEGORY]: { icon: icLayers, title: t("Category") },
      [TraitType.BATHROOMS]: { icon: icBath, title: t("Bathrooms") },
      [TraitType.Nation]: { icon: icMap, title: t("Nation") },
    }

  const trait = traitMap[traitType] || {
    icon: icTag,
    title: t("Other"),
  }

  return trait
}

export { useTraitConfig }
