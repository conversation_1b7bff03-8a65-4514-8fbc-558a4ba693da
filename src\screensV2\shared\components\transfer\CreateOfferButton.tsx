import React from "react"
import { DimensionValue, ViewStyle } from "react-native"
import { PrimaryButton } from "src/componentsv2/Button"
import NewOfferModal from "./NewOfferModal"
import { useTranslation } from "react-i18next"
import { useAccount } from "wagmi"
import { Estate } from "src/api"
import { useEstateTokenBalance } from "src/api/contracts"
import { textStyles } from "src/config/styles"

interface CreateOfferButtonProps {
  estate: Estate
  style?: ViewStyle
  width?: DimensionValue
}

export const CreateOfferButton: React.FC<CreateOfferButtonProps> = ({
  estate,
  style,
  width,
}) => {
  const { t } = useTranslation()
  const { address } = useAccount()
  const [isShow, setIsShow] = React.useState<boolean>(false)

  const { value: nftBalance } = useEstateTokenBalance(
    address as `0x${string}`,
    estate.id
  )
  const isBalancePositive = Number(nftBalance) > 0

  if (!estate || !address) return null
  return (
    <>
      <PrimaryButton
        title={t("Create Offer")}
        onPress={() => setIsShow(true)}
        width={width}
        borderRadius={8}
        height={38}
        textStyle={textStyles.LMedium}
        style={style}
        enabled={isBalancePositive}
      />
      {estate && (
        <NewOfferModal
          estate={estate}
          isShow={isShow}
          onClose={() => setIsShow(false)}
        />
      )}
    </>
  )
}
