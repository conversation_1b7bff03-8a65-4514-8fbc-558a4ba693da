import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import { ethers } from "ethers"
import {
  estateTokenAbi,
  useEstateTokenBalance,
  useEstateTokenData,
} from "src/api/contracts"
import { useEthersProvider } from "hooks"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import { CONTRACT_ADDRESS_ESTATE_TOKEN } from "src/config/env"
import { queryClient } from "src/api/query"
import { isValidWalletAddress } from "utils"
import Logger from "src/utils/logger"
import { useChainId } from "wagmi"

const logger = new Logger({ tag: "TransferNFTsModal" })

export interface UseTransferProps {
  estateId: string
  onClose: () => void
}

export function useTransfer({ estateId, onClose }: UseTransferProps) {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const { address } = useAccount()
  const { value: nftBalance, queryKey } = useEstateTokenBalance(
    address as `0x${string}`,
    estateId
  )
  const { decimals: estateDecimals } = useEstateTokenData(estateId)
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const formSchema = z.object({
    toAddress: z
      .string()
      .refine((val) => isValidWalletAddress(val), {
        message: t("Invalid wallet address"),
      })
      .refine((val) => val.toLowerCase() !== address?.toLowerCase(), {
        message: t("Cannot transfer to your own address"),
      }),
    amount: z.coerce
      .number()
      .min(0.000000000000000001, t("Amount must be greater than 0"))
      .refine((val) => nftBalance && val <= Number(nftBalance), {
        message: nftBalance
          ? t("Amount must not exceed your balance")
          : t("Please wait for balance to load"),
      }),
  })

  type FormValues = z.infer<typeof formSchema>

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      toAddress: "",
      amount: 0,
    },
  })

  const chainId = useChainId()
  const chainNamesMap: Record<number, string> = {
    56: t("Binance Smart Chain"),
    97: t("Binance Smart Chain Testnet"),
  }

  const chainName = chainNamesMap[chainId] || t("Unknown Chain")

  const closeAndReset = () => {
    onClose()
    form.reset()
  }

  const handleSubmit = async (values: FormValues) => {
    if (isLoading) return
    if (!ethersProvider) return
    setIsLoading(true)

    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_ESTATE_TOKEN,
        abi: estateTokenAbi,
        functionName: "safeTransferFrom",
        args: [
          address,
          values.toAddress as `0x${string}`,
          BigInt(estateId),
          BigInt(Math.round(values.amount * Math.pow(10, estateDecimals))),
          ethers.utils.hexlify(
            ethers.utils.toUtf8Bytes(
              "Transfer via Briky Finance Decentralized Application"
            )
          ),
        ],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status !== 1) {
        showError(t("Fail to transfer NFT"))
      } else {
        await queryClient.invalidateQueries({ queryKey })
        showSuccessWhenCallContract(t("Transfer successfully"))
        onClose()
      }
    } catch (error) {
      logger.error("Failed to transfer NFT", error)
      showError(t("Fail to transfer NFT"))
    } finally {
      setIsLoading(false)
    }
  }

  const handleSetMaxBalance = () => {
    form.setValue("amount", Number(nftBalance))
  }

  return {
    form,
    isLoading,
    nftBalance,
    handleSubmit,
    handleSetMaxBalance,
    closeAndReset,
    chainName,
    t,
    estateDecimals,
  }
}
