import React, { useCallback } from "react"
import { Image, ScrollView, StyleSheet, Text, View } from "react-native"
import {
  Background,
  TopBar,
  CustomPressable,
  DateTimeInput,
  ErrorLabel,
  InputField,
} from "src/componentsv2"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { ActivityIndicator } from "react-native-paper"
import { useTranslation } from "react-i18next"
import { Controller } from "react-hook-form"
import { PrimaryButton } from "src/componentsv2/Button"
import { useAccount } from "wagmi"
import {
  useVerifyAccountFormSchema,
  useVerifyAccount,
  Country,
} from "./hooks/useVerifyAccount"
import { useChoosePhoto } from "utils/choosePhotoExt"
import { ImagePickerAsset } from "expo-image-picker/src/ImagePicker.types"
import { Dropdown } from "react-native-element-dropdown"
import { LabelView } from "src/componentsv2/LabelView"
import icCircleClose from "assets/imagesV2/ic_circle_close.png"
import icUpload from "assets/imagesV2/ic_upload.png"

const renderCountryDropdownItem = (item: Country) => (
  <Text style={[styles.dropdownItemContainer]}>{item.label}</Text>
)

const ImageUploader: React.FC<{
  imageUri?: string
  onPress: () => void
  onClear: () => void
}> = ({ imageUri, onPress, onClear }) => {
  const { t } = useTranslation()

  return (
    <View style={styles.imageContainer}>
      <View>
        {imageUri ? (
          <View style={{ paddingHorizontal: 40 }}>
            <Image source={{ uri: imageUri }} style={styles.image} />
            <CustomPressable onPress={onClear} style={styles.close}>
              <Image source={icCircleClose} style={viewStyles.size16Icon} />
            </CustomPressable>
          </View>
        ) : (
          <CustomPressable style={styles.upload} onPress={onPress}>
            <Image source={icUpload} style={viewStyles.size16Icon} />
            <Text style={styles.uploadFile}>{t("Upload file")}</Text>
          </CustomPressable>
        )}
      </View>
    </View>
  )
}

const VerifyAccountView: React.FC = () => {
  const { t } = useTranslation()
  const { address } = useAccount()
  const { onSubmit, isLoading, profile, countriesData } = useVerifyAccount(
    address || ""
  )

  const { handleChoosePhoto } = useChoosePhoto()

  const onChoosePhoto = useCallback(
    (onChange: (value: ImagePickerAsset | undefined) => void) => {
      handleChoosePhoto((files) => {
        onChange(files[0])
      })
    },
    []
  )

  const { form } = useVerifyAccountFormSchema(t, profile || undefined)

  return (
    <Background>
      <TopBar enableBack={true} title={t("Verify my account")} />
      <ScrollView style={styles.container}>
        <View style={styles.content}>
          <Controller
            control={form.control}
            name="fullName"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Display name")}
                value={value}
                require={true}
                onChangeText={onChange}
                onBlur={onBlur}
                style={styles.marginTop16}
                error={
                  form.formState.errors.fullName?.message &&
                  String(form.formState.errors.fullName?.message)
                }
              />
            )}
          />

          <Controller
            control={form.control}
            name="dateOfBirthInSeconds"
            render={({ field: { onChange, value } }) => (
              <>
                <DateTimeInput
                  title={t("Date of birth")}
                  value={value}
                  require={true}
                  onChangeDate={(date) => {
                    onChange(date?.toDateString() || "")
                  }}
                />
                <ErrorLabel
                  error={form.formState.errors.dateOfBirthInSeconds?.message}
                />
              </>
            )}
          />
          <Controller
            control={form.control}
            name="nationalId"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Nationality ID")}
                value={value}
                onChangeText={onChange}
                require={true}
                onBlur={onBlur}
                style={styles.marginTop16}
                error={
                  form.formState.errors.nationalId?.message &&
                  String(form.formState.errors.nationalId?.message)
                }
              />
            )}
          />
          <LabelView
            label={t("Nationality")}
            require={true}
            style={styles.nationalityLabel}
          />
          <Controller
            control={form.control}
            render={({ field: { onChange, value } }) => (
              <Dropdown
                value={value}
                data={countriesData}
                labelField="label"
                valueField="value"
                selectedTextStyle={styles.dropdownItem}
                placeholderStyle={styles.dropdownItem}
                itemTextStyle={styles.dropdownItem}
                onChange={(item) => onChange(item.value)}
                style={[viewStyles.input, styles.dropdown]}
                renderItem={renderCountryDropdownItem}
              />
            )}
            name="country"
            rules={{ required: true }}
          />
          <Controller
            control={form.control}
            name="nationalIDCardFrontImage"
            render={({ field: { onChange, value } }) => {
              const imagePickerAsset = value as ImagePickerAsset | undefined
              return (
                <>
                  <LabelView
                    label={t("Front of ID Card")}
                    require={true}
                    style={styles.labelIDCard}
                  />
                  <ImageUploader
                    imageUri={imagePickerAsset?.uri}
                    onPress={() => onChoosePhoto(onChange)}
                    onClear={() => {
                      onChange(undefined)
                    }}
                  />
                </>
              )
            }}
          />
          <Controller
            control={form.control}
            name="nationalIDCardBackImage"
            render={({ field: { onChange, value } }) => {
              const imagePickerAsset = value as ImagePickerAsset | undefined
              return (
                <>
                  <LabelView
                    label={t("Back of ID Card")}
                    require={true}
                    style={styles.labelIDCard}
                  />
                  <ImageUploader
                    imageUri={imagePickerAsset?.uri}
                    onPress={() => onChoosePhoto(onChange)}
                    onClear={() => {
                      onChange(undefined)
                    }}
                  />
                </>
              )
            }}
          />

          <PrimaryButton
            title={t("Submit")}
            onPress={form.handleSubmit(onSubmit)}
            color={Colors.Primary500}
            contentColor={textColors.textBlack}
            style={styles.verifyAccount}
            width={"100%"}
            height={38}
            isLoading={isLoading}
            icon={
              isLoading && (
                <ActivityIndicator size="small" color={Colors.white} />
              )
            }
          />
        </View>
      </ScrollView>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 12,
  },
  content: { marginBottom: 16 },
  upload: {
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
  uploadView: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 8,
  },
  close: {
    position: "absolute",
    right: 0,
    padding: 4,
  },
  nationalityLabel: {
    marginTop: 16,
    marginBottom: 4,
  },
  marginTop16: {
    marginTop: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  imageContainer: {
    width: "100%",
    aspectRatio: 20 / 9,
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  image: {
    width: "100%",
    height: "100%",
    borderRadius: 8,
    aspectRatio: 16 / 9,
  },
  dropdown: {
    marginTop: 8,
  },
  dropdownItem: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
    backgroundColor: Colors.PalleteBlack,
    alignItems: "center",
    paddingVertical: 8,
    paddingStart: 12,
  },
  dropdownItemContainer: {
    ...textStyles.MMedium,
    color: Colors.PalleteWhite,
    backgroundColor: Colors.PalleteBlack,
    alignItems: "center",
    paddingVertical: 8,
    paddingStart: 24,
  },
  verifyAccount: {
    width: "100%",
    marginTop: 20,
    marginBottom: 30,
    alignSelf: "center",
  },
  labelIDCard: {
    marginTop: 16,
  },
  uploadFile: {
    ...textStyles.SMedium,
    color: Colors.PalleteWhite,
    marginTop: 8,
  },
})

export default VerifyAccountView
