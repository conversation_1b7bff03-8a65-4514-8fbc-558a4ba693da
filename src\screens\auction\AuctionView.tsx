import React from "react"
import { StyleSheet, View } from "react-native"
import { Background } from "components"
import { AccountStat, AuctionRounds } from "./components"
import { ScrollView } from "react-native-gesture-handler"

const AuctionView: React.FC = () => {
  return (
    <ScrollView>
      <View style={{ flex: 1 }}>
        <Background>
          <View style={styles.container}>
            <AccountStat />
            <AuctionRounds />
          </View>
        </Background>
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    paddingTop: 30,
    alignItems: "center",
    paddingHorizontal: 16,
  },
  tabContainer: {
    flexDirection: "row",
    borderRadius: 6,
    alignSelf: "stretch",
  },
  tabButton: {
    flex: 1,
    borderRadius: 0,
    paddingVertical: 4,
  },
  tabText: {
    textAlign: "center",
    fontWeight: "500",
  },
  cardContainer: {
    marginTop: 16,
  },
  cardRow: {
    flexDirection: "row",
    width: "100%",
    marginTop: 12,
  },
  card: {
    flex: 1,
    padding: 12,
  },
  cardMargin: {
    marginStart: 12,
  },
  cardContent: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  icon: {
    width: 16,
    height: 16,
    marginStart: 8,
  },
})

export { AuctionView }
