import React from "react"
import { useTranslation } from "react-i18next"
import {
  useAuctionLiquidityPercentage,
  useAuctionTotalDeposit,
  useAuctionTotalIssuance,
} from "src/api/contracts"
import { useErc20Formatter } from "src/api/contracts/erc20"
import {
  CONTRACT_ADDRESS_AUCTION_CURRENCY,
  CONTRACT_ADDRESS_PRIMARY_TOKEN,
} from "src/config/env"
import { ImageSourcePropType } from "react-native/Libraries/Image/Image"
import { Image, StyleSheet, Text, View, ViewStyle } from "react-native"
import { CardView, GradientText } from "components"
import { textStyles, viewStyles } from "src/config/styles"
import { parseCurrency } from "utils"
import usdtIcon from "assets/images/ic_usdt.png"
import brikIcon from "assets/images/ic_brik.png"
import Colors from "src/config/colors"

export const AuctionFloatingBoxes: React.FC<{
  contractAddress: `0x${string}`
}> = ({ contractAddress }) => {
  const { t } = useTranslation()
  const totalDepositWei = useAuctionTotalDeposit(contractAddress)
  const currencyFormatter = useErc20Formatter(CONTRACT_ADDRESS_AUCTION_CURRENCY)
  const primaryTokenFormatter = useErc20Formatter(
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )
  const totalIssuanceWei = useAuctionTotalIssuance(contractAddress)
  const totalIssuance = primaryTokenFormatter.format(totalIssuanceWei)
  const liquidityPercentage = useAuctionLiquidityPercentage(contractAddress)
  const developmentFundPercentage = 100 - (liquidityPercentage || 100)

  const renderCard = (
    title: string,
    value: string,
    imageSource: ImageSourcePropType,
    style?: ViewStyle
  ) => (
    <CardView style={{ ...styles.card, ...style }}>
      <View>
        <GradientText text={title} />
        <View style={styles.cardContent}>
          <Text style={textStyles.bodyM}>{value}</Text>
          <Image source={imageSource} style={styles.icon} />
        </View>
      </View>
    </CardView>
  )

  return (
    <View>
      <View style={styles.cardRow}>
        {renderCard(t("Selling BRIK"), parseCurrency(totalIssuance), brikIcon)}
        {renderCard(
          t("auction-TotalDeposit"),
          parseCurrency(currencyFormatter.format(totalDepositWei)),
          usdtIcon,
          { marginStart: 12 }
        )}
      </View>
      <View style={styles.cardRow}>
        {renderCard(
          t("Treasury"),
          parseCurrency(
            (currencyFormatter.format(totalDepositWei) *
              (liquidityPercentage || 0)) /
              100
          ),
          usdtIcon
        )}
        {renderCard(
          t("Development Fund"),
          parseCurrency(
            (currencyFormatter.format(totalDepositWei) *
              developmentFundPercentage) /
              100
          ),
          usdtIcon,
          { marginStart: 12 }
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    width: "100%",
    paddingTop: 30,
    alignItems: "center",
    paddingHorizontal: 16,
  },
  tabContainer: {
    flexDirection: "row",
    overflow: "hidden",
    borderRadius: 8,
    alignSelf: "stretch",
  },
  tabButton: {
    flex: 1,
    borderRadius: 0,
    paddingVertical: 4,
  },
  tabText: {
    textAlign: "center",
    ...textStyles.labelL,
  },
  cardRow: {
    flexDirection: "row",
    width: "100%",
    marginTop: 12,
  },
  card: {
    flex: 1,
    padding: 12,
  },
  cardMargin: {
    marginStart: 12,
  },
  cardContent: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  icon: {
    ...viewStyles.tinyIcon,
    marginStart: 8,
  },
})
