import { textColors } from "src/config/colors"
import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import usdtIcon from "assets/images/ic_usdt.png"
import { viewStyles } from "src/config/styles"

type Props = {
  name: string
  value: string
  isShowIcon: boolean
}

export const AuctionInfoRow: React.FunctionComponent<Props> = ({
  name,
  value,
  isShowIcon,
}) => {
  return (
    <View style={styles.infoRow}>
      <Text style={styles.text}>{name}</Text>
      <Text style={styles.value}>{value}</Text>
      {isShowIcon && <Image source={usdtIcon} style={styles.icon} />}
    </View>
  )
}

const styles = StyleSheet.create({
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  text: {
    flex: 1,
    color: textColors.textGray600,
  },
  value: {
    color: textColors.textGray600,
  },
  icon: {
    ...viewStyles.tinyIcon,
    marginLeft: 8,
  },
})
