import React, { useState } from "react"
import { StyleSheet, Text, View } from "react-native"
import { CustomPressable } from "components"
import Colors from "src/config/colors"
import { textStyles } from "src/config/styles"
import {
  CONTRACT_ADDRESS_AUCTION_BACKER_ROUND,
  CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_1,
  CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_2,
  CONTRACT_ADDRESS_AUCTION_PUBLIC_SALE,
  CONTRACT_ADDRESS_AUCTION_SEED_ROUND,
} from "src/config/env"
import { useTranslation } from "react-i18next"
import { AuctionFloatingBoxes } from "screens/auction/components/AuctionFloatingBoxes"
import { AuctionTabView } from "screens/auction/components/AuctionTabView"

const AuctionRounds: React.FC = () => {
  const { t } = useTranslation()

  const [tabIndex, setTabIndex] = useState<number>(0)
  const tabs = [
    {
      label: t("Backer Round"),
      value: CONTRACT_ADDRESS_AUCTION_BACKER_ROUND,
    },
    {
      label: t("Seed Round"),
      value: CONTRACT_ADDRESS_AUCTION_SEED_ROUND,
    },
    {
      label: t("Private Sale 1"),
      value: CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_1,
    },
    {
      label: t("Private Sale 2"),
      value: CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_2,
    },
    {
      label: t("Public Sale"),
      value: CONTRACT_ADDRESS_AUCTION_PUBLIC_SALE,
    },
  ].slice(0, 2)

  const round = tabs[tabIndex]

  return (
    <View
      style={{
        width: "100%",
      }}
    >
      <TabSelector tabIndex={tabIndex} setTabIndex={setTabIndex} />
      <View>
        <AuctionFloatingBoxes contractAddress={round.value as `0x${string}`} />
        <AuctionTabView contractAddress={round.value as `0x${string}`} />
      </View>
    </View>
  )
}

interface TabSelectorProps {
  tabIndex: number
  setTabIndex: React.Dispatch<React.SetStateAction<number>>
}

const TabSelector: React.FC<TabSelectorProps> = ({ tabIndex, setTabIndex }) => {
  const { t } = useTranslation()
  const tabs = [t("Backer Round"), t("Seed Round")]
  return (
    <View style={styles.tabContainer}>
      {tabs.map((title, index) => (
        <CustomPressable
          key={index}
          style={{
            ...styles.tabButton,
            backgroundColor: tabIndex === index ? Colors.black4 : "transparent",
          }}
          onPress={() => setTabIndex(index)}
        >
          <Text
            style={[
              styles.tabText,
              {
                color: tabIndex === index ? Colors.black12 : "gray",
              },
            ]}
          >
            {title}
          </Text>
        </CustomPressable>
      ))}
    </View>
  )
}

export { AuctionRounds }
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#ffffff",
    width: "100%",
    paddingTop: 30,
    alignItems: "center",
    paddingHorizontal: 16,
  },
  tabContainer: {
    flexDirection: "row",
    overflow: "hidden",
    borderRadius: 8,
    alignSelf: "stretch",
  },
  tabButton: {
    flex: 1,
    borderRadius: 0,
    paddingVertical: 4,
  },
  tabText: {
    textAlign: "center",
    ...textStyles.labelL,
  },
  cardRow: {
    flexDirection: "row",
    width: "100%",
    marginTop: 12,
  },
  card: {
    flex: 1,
    padding: 12,
  },
  cardMargin: {
    marginStart: 12,
  },
  cardContent: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
  },
  icon: {
    width: 16,
    height: 16,
    marginStart: 8,
  },
})
