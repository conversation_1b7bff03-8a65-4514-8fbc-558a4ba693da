import React, { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { useAuctionEndAt } from "src/api/contracts"
import { NoAuction } from "screens/auction/components/NoAuction"
import { NextRewardCountDown } from "screens/auction/components/NextRewardCountDown"
import { FormAuctionWithdraw } from "screens/auction/components/FormAuctionWithdraw"
import { FormAuctionDeposit } from "screens/auction/components/FormAuctionDeposit"

export const AuctionTabView: React.FC<{
  contractAddress: `0x${string}`
}> = ({ contractAddress }) => {
  const { t } = useTranslation()

  const [remainingTime, setRemainingTime] = useState(0)
  const auctionEndAt = useAuctionEndAt(contractAddress)

  const auctionStarted = !!auctionEndAt && Number(auctionEndAt) > 0
  const auctionEnded = auctionStarted && remainingTime <= 0
  const auctionInProgress = auctionStarted && !auctionEnded

  useEffect(() => {
    if (auctionEndAt && Number(auctionEndAt) > 0) {
      const interval = setInterval(() => {
        const currentTimestamp = Math.floor(Date.now() / 1000)
        const timeDiff = Number(auctionEndAt) - currentTimestamp

        if (timeDiff <= 0) {
          clearInterval(interval)
          setRemainingTime(0)
        } else {
          setRemainingTime(timeDiff)
        }
      }, 1000)

      return () => clearInterval(interval)
    }
  }, [auctionEndAt])

  return (
    <>
      {!auctionStarted && <NoAuction contractAddress={contractAddress} />}
      {auctionInProgress && (
        <NextRewardCountDown
          remainingTime={remainingTime}
          title={t("auction-TimeOut")}
        />
      )}
      {!auctionEnded && (
        <FormAuctionDeposit
          remainingTime={remainingTime}
          contractAddress={contractAddress}
        />
      )}
      {auctionEnded && (
        <FormAuctionWithdraw
          remainingTime={remainingTime}
          contractAddress={contractAddress}
        />
      )}
    </>
  )
}
