import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { CardView, PrimaryButton } from "components"
import { convertHexToRGBA } from "utils"
import Colors from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import { useAccount } from "wagmi"
import usdtIcon from "assets/images/ic_usdt.png"
import brikIcon from "assets/images/ic_brik.png"

const AuctionWithDraw: React.FC = () => {
  const { t } = useTranslation()
  const { address } = useAccount()

  return (
    <CardView style={styles.card}>
      <View>
        <Text style={textStyles.titleL}>{t("Backer Round")}</Text>
        {!address ? (
          <Text style={styles.loginPrompt}>
            {t("Please login to withdraw your BRIK.")}
          </Text>
        ) : (
          <AccountInfo />
        )}
        <PrimaryButton
          style={styles.button}
          height={36}
          borderRadius={8}
          onPress={() => {}}
          title={t("auction-Withdraw")}
          enabled={!!address}
        />
      </View>
    </CardView>
  )
}

const AccountInfo: React.FC = () => {
  const { t } = useTranslation()
  return (
    <View>
      <View style={styles.infoRow}>
        <Text style={styles.infoText}>{t("You have deposited")}</Text>
        <Text style={textStyles.body1}>0</Text>
        <Image source={usdtIcon} style={styles.icon} />
      </View>
      <View style={[styles.infoRow, styles.infoRowMargin]}>
        <Text style={styles.withdrawableText}>
          {t("Withdrawable amount").toUpperCase()}
        </Text>
        <Text style={textStyles.body1}>0</Text>
        <Image source={brikIcon} style={styles.icon} />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  card: {
    margin: 16,
    width: "100%",
    padding: 12,
  },
  loginPrompt: {
    marginTop: 20,
    ...textStyles.bodyM,
    color: convertHexToRGBA(Colors.redLight, 0.5),
  },
  button: {
    marginTop: 15,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoRowMargin: {
    marginTop: 8,
  },
  infoText: {
    flex: 1,
    ...textStyles.body1,
  },
  withdrawableText: {
    flex: 1,
    ...textStyles.titleL,
    color: convertHexToRGBA(Colors.primary, 0.7),
  },
  icon: {
    ...viewStyles.tinyIcon,
    marginStart: 8,
  },
})

export { AuctionWithDraw }
