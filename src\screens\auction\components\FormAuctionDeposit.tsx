import React, { useState } from "react"
import { Image, StyleSheet, Text, TextInput, View } from "react-native"
import { useTranslation } from "react-i18next"
import { parseCurrency } from "utils"
import { useSuspenseQuery } from "@tanstack/react-query"
import { ActivityIndicator } from "react-native-paper"
import { CardView, PrimaryButton, SimpleLoadingView } from "components"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { PerCentSlider } from "components/PerCentSlider"
import { AuctionInfoRow } from "./AuctionInfoRow"
import {
  auctionAbi,
  erc20Abi,
  useAuctionEndAt,
  useAuctionGetDepositor,
  useAuctionLiquidityPercentage,
  useErc20Allowance,
  useErc20Balance,
  useErc20Formatter,
} from "src/api/contracts"
import {
  CONTRACT_ADDRESS_AUCTION_BACKER_ROUND,
  CONTRACT_ADDRESS_AUCTION_CURRENCY,
  CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_1,
  CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_2,
  CONTRACT_ADDRESS_AUCTION_PUBLIC_SALE,
  CONTRACT_ADDRESS_AUCTION_SEED_ROUND,
  MAX_UINT256,
} from "src/config/env"
import { fetchMyReferralsQueryOptions } from "src/api"
import { RequestForWhiteListButton } from "./RequestForWhiteListButton"
import { ContractFunctionExecutionError } from "viem"
import { useAccount, useWriteContract } from "wagmi"
import { useEthersProvider } from "hooks"
import { getFloatValueFromDecimalString } from "utils/numberExt"
import usdtIcon from "assets/images/ic_usdt.png"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import Logger from "src/utils/logger"

type Props = {
  remainingTime: number
  contractAddress: `0x${string}`
}

const logger = new Logger({ tag: "FormAuctionDeposit" })

export const FormAuctionDeposit: React.FunctionComponent<Props> = ({
  remainingTime,
  contractAddress,
}) => {
  const { t } = useTranslation()
  const { address: userAddress, isConnected } = useAccount()
  const [isLoading, setIsLoading] = useState(false)
  const [canCancelLoading, setCanCancelLoading] = useState<boolean>(true)

  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const liquidityPercentage = useAuctionLiquidityPercentage(contractAddress)
  const developmentFundPercentage =
    100 - (liquidityPercentage ? liquidityPercentage : 100)

  const [isAmountAll, setIsAmountAll] = useState<boolean>(false)
  const [amount, setAmount] = useState<string>("")
  const { data: myReferrals = [] } = useSuspenseQuery(
    fetchMyReferralsQueryOptions(userAddress)
  )

  const auctionEndAt = useAuctionEndAt(contractAddress)

  const currencyTokenBalanceWei = useErc20Balance(
    userAddress,
    CONTRACT_ADDRESS_AUCTION_CURRENCY
  )
  const currencyFormatter = useErc20Formatter(CONTRACT_ADDRESS_AUCTION_CURRENCY)
  const currencyTokenBalance = parseFloat(
    currencyFormatter.formatFixed(currencyTokenBalanceWei)
  )

  const auctionDepositor = useAuctionGetDepositor(contractAddress, userAddress)
  const depositedAmount = parseFloat(
    currencyFormatter.formatFixed(auctionDepositor?.depositedAmountWei || 0n)
  )

  const onAmountChange = (value: string = "") => {
    const amount = getFloatValueFromDecimalString(value || "0")
    if (amount <= currencyTokenBalance) setAmount(amount.toString())
    setIsAmountAll(amount === currencyTokenBalance)
  }

  const currentCurrencyAllowanceWei = useErc20Allowance(
    userAddress,
    CONTRACT_ADDRESS_AUCTION_CURRENCY,
    contractAddress
  )

  const auctionStarted = !!auctionEndAt
  const auctionEnded = auctionStarted && remainingTime <= 0
  const disableDeposit =
    !auctionStarted ||
    currencyTokenBalance <= 0 ||
    isLoading ||
    !userAddress ||
    !auctionDepositor?.isWhitelisted
  const disableSubmit = disableDeposit || parseFloat(amount || "0") <= 0

  const lowerCaseLabel = () => {
    switch (contractAddress) {
      case CONTRACT_ADDRESS_AUCTION_BACKER_ROUND:
        return t("Backer Round auction.")
      case CONTRACT_ADDRESS_AUCTION_SEED_ROUND:
        return t("Seed Round auction.")
      case CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_1:
        return t("Private Sale 1 auction.")
      case CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_2:
        return t("Private Sale 2 auction.")
      case CONTRACT_ADDRESS_AUCTION_PUBLIC_SALE:
        return t("Public Sale auction.")
      default:
        return ""
    }
  }

  const isWhitelisting = myReferrals.some((ref) => {
    let sameRound = false
    switch (contractAddress) {
      case CONTRACT_ADDRESS_AUCTION_BACKER_ROUND:
        sameRound = ref.round === "BACKER"
        break
      case CONTRACT_ADDRESS_AUCTION_SEED_ROUND:
        sameRound = ref.round === "SEED"
        break
      case CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_1:
        sameRound = ref.round === "PRIVATE_SALE_1"
        break
      case CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_2:
        sameRound = ref.round === "PRIVATE_SALE_2"
        break
      case CONTRACT_ADDRESS_AUCTION_PUBLIC_SALE:
        sameRound = ref.round === "PUBLIC_SALE"
        break
      default:
        sameRound = false
    }
    return sameRound && ref.status === "WHITELISTING"
  })

  const isValidating = myReferrals.some((ref) => {
    let sameRound = false
    switch (contractAddress) {
      case CONTRACT_ADDRESS_AUCTION_BACKER_ROUND:
        sameRound = ref.round === "BACKER"
        break
      case CONTRACT_ADDRESS_AUCTION_SEED_ROUND:
        sameRound = ref.round === "SEED"
        break
      case CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_1:
        sameRound = ref.round === "PRIVATE_SALE_1"
        break
      case CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_2:
        sameRound = ref.round === "PRIVATE_SALE_2"
        break
      case CONTRACT_ADDRESS_AUCTION_PUBLIC_SALE:
        sameRound = ref.round === "PUBLIC_SALE"
        break
      default:
        sameRound = false
    }
    return sameRound && ref.status === "VALIDATING"
  })

  const onPercentChange = (percent: number) => {
    if (percent === 100) {
      setAmount(currencyTokenBalance.toString())
    } else {
      setAmount(((percent * currencyTokenBalance) / 100).toString())
    }
  }

  const onSubmitDeposit = async () => {
    if (!auctionStarted) return
    if (auctionEnded) return
    if (currentCurrencyAllowanceWei === undefined) return
    if (currencyTokenBalanceWei === undefined) return
    if (!ethersProvider) return

    const amountWei = currencyFormatter.parse(amount)
    if (amountWei <= 0n) return

    try {
      setIsLoading(true)

      if (currentCurrencyAllowanceWei < amountWei) {
        const txHash = await writeContractAsync({
          address: CONTRACT_ADDRESS_AUCTION_CURRENCY,
          abi: erc20Abi,
          functionName: "approve",
          args: [contractAddress, BigInt(MAX_UINT256)],
        })
        setCanCancelLoading(!txHash)
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          setIsLoading(false)
          showError(t("Failed to approve"))
          return
        }
      }
      setCanCancelLoading(true)
      const txHash = await writeContractAsync({
        abi: auctionAbi,
        address: contractAddress,
        functionName: "deposit",
        args: [isAmountAll ? currencyTokenBalanceWei : amountWei],
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Deposit success") + ". " + t("Data will be updated in few seconds")
        )
        setAmount("0")
      } else {
        showError(t("Deposit failed"))
      }
    } catch (e) {
      logger.error("Deposit failed", e)
      if (e instanceof ContractFunctionExecutionError) {
        logger.error("Contract execution error details", {
          cause: e.cause,
          metaMessages: e.cause.metaMessages,
          docsPath: e.cause.docsPath,
        })
      }
      showError(t("Deposit failed"))
    } finally {
      setIsLoading(false)
    }
  }

  const getWhitelistStatusInfo = (): [string, string] | null => {
    if (!auctionDepositor || !isConnected) {
      return [t("Please login to deposit."), Colors.primary]
    }
    if (isValidating) {
      return [
        t("Your whitelist request is being validated, please come back later."),
        Colors.blueLink,
      ]
    }
    if (isWhitelisting) {
      return [
        t(
          "Your whitelist request is still in progress, please come back later."
        ),
        Colors.blueLink,
      ]
    }
    if (!auctionDepositor.isWhitelisted) {
      return [
        `${t("You must be whitelisted to join our")} ${lowerCaseLabel()}`,
        Colors.red,
      ]
    }
    if (auctionDepositor.isWhitelisted) {
      return [
        `${t("You are on the whitelist!")} ${auctionStarted ? t("You can deposit now.") : t("Please wait for the auction to start depositing.")}`,
        Colors.green,
      ]
    }
    return null
  }

  const whiteListStatus = getWhitelistStatusInfo()

  if (!userAddress) {
    return null
  }

  return (
    <>
      <CardView style={styles.card}>
        <View style={styles.container}>
          <Text
            style={[
              textStyles.titleL,
              { color: Colors.primary, marginBottom: 16 },
            ]}
          >
            {t("Deposit")}
          </Text>
          {whiteListStatus != null && (
            <Text style={{ flex: 1, color: whiteListStatus[1] }}>
              {whiteListStatus[0]}
            </Text>
          )}
          {auctionDepositor?.isWhitelisted && (
            <>
              <View style={styles.infoContainer}>
                <AuctionInfoRow
                  name={t("You have deposited")}
                  value={parseCurrency(depositedAmount)}
                  isShowIcon={true}
                />
                <AuctionInfoRow
                  name={t("Treasury percentage")}
                  value={`${liquidityPercentage}%`}
                  isShowIcon={false}
                />
                <AuctionInfoRow
                  name={t("Development Fund percentage")}
                  value={`${developmentFundPercentage}%`}
                  isShowIcon={false}
                />
                <AuctionInfoRow
                  name={t("Treasury contribution")}
                  value={parseCurrency(
                    ((liquidityPercentage || 0) * depositedAmount) / 100
                  )}
                  isShowIcon={true}
                />
                <AuctionInfoRow
                  name={t("Development Fund contribution")}
                  value={parseCurrency(
                    (developmentFundPercentage * depositedAmount) / 100
                  )}
                  isShowIcon={true}
                />
              </View>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  value={parseCurrency(amount)}
                  onChangeText={onAmountChange}
                  editable={!disableDeposit}
                  placeholder="Enter amount"
                />
                <Image source={usdtIcon} style={styles.inputIcon} />
              </View>
              <PerCentSlider
                maxAmount={currencyTokenBalance}
                disabled={disableDeposit}
                amount={parseFloat(amount || "0")}
                onAmountChange={onPercentChange}
              />
              <View style={styles.percentageContainer}>
                <Text style={styles.percentageText}>0%</Text>
                <Text style={styles.percentageText}>25%</Text>
                <Text style={styles.percentageText}>50%</Text>
                <Text style={styles.percentageText}>75%</Text>
                <Text style={styles.percentageText}>100%</Text>
              </View>
              <View style={styles.buttonContainer}>
                <PrimaryButton
                  title={t("auction-Deposit")}
                  enabled={!disableSubmit}
                  isLoading={isLoading}
                  onPress={onSubmitDeposit}
                />
              </View>
            </>
          )}
          {isConnected &&
            !auctionDepositor?.isWhitelisted &&
            !isWhitelisting &&
            !isValidating && <RequestForWhiteListButton />}

          {isLoading && (
            <ActivityIndicator size="small" color={Colors.primary} />
          )}
        </View>
      </CardView>
      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  card: {
    marginVertical: 16,
    backgroundColor: Colors.white,
    padding: 12,
  },
  infoContainer: {
    marginVertical: 16,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 16,
    borderWidth: 1,
    borderColor: Colors.black4,
    padding: 8,
    borderRadius: 4,
  },
  inputIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  textYellow: {
    flex: 1,
    color: Colors.primary,
  },
  textBlue: {
    flex: 1,
    color: Colors.blueLink,
  },
  textRed: {
    color: Colors.red,
  },
  textGreen: {
    flex: 1,
    color: Colors.green,
  },
  input: {
    flex: 1,
  },
  slider: {
    marginTop: 16,
  },
  percentageContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
  },
  percentageText: {
    width: 40,
    textAlign: "center",
  },
  buttonContainer: {
    marginTop: 16,
    alignItems: "center",
  },
})
