import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import {
  auctionAbi,
  useAuctionEndAt,
  useAuctionGetDepositor,
  useAuctionWithdrawableAmount,
  useErc20Formatter,
} from "src/api/contracts"
import { textStyles } from "src/config/styles"
import {
  CONTRACT_ADDRESS_AUCTION_CURRENCY,
  CONTRACT_ADDRESS_PRIMARY_TOKEN,
} from "src/config/env"
import { parseCurrency } from "utils"
import { PrimaryButton, SimpleLoadingView } from "components"
import Colors from "src/config/colors"
import { useAccount, useWriteContract } from "wagmi"
import { useEthersProvider } from "hooks"
import usdtIcon from "assets/images/ic_usdt.png"
import brikIcon from "assets/images/ic_brik.png"
import { showError, showSuccessWhenCallContract } from "utils/toast"

type Props = {
  remainingTime: number
  contractAddress: `0x${string}`
}

export const FormAuctionWithdraw: React.FC<Props> = ({
  remainingTime,
  contractAddress,
}) => {
  const { t } = useTranslation()
  const { address: userAddress, isConnected } = useAccount()

  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const auctionEndAt = useAuctionEndAt(contractAddress)
  const primaryTokenFormatter = useErc20Formatter(
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )

  const withdrawableAmountWei = useAuctionWithdrawableAmount(
    contractAddress,
    userAddress
  )
  const withdrawableAmount = parseFloat(
    primaryTokenFormatter.formatFixed(withdrawableAmountWei)
  )

  const currencyFormatter = useErc20Formatter(CONTRACT_ADDRESS_AUCTION_CURRENCY)
  const auctionDepositor = useAuctionGetDepositor(contractAddress, userAddress)
  const depositedAmount = parseFloat(
    currencyFormatter.formatFixed(auctionDepositor?.depositedAmountWei || 0n)
  )

  const auctionStarted = !!auctionEndAt && Number(auctionEndAt) > 0
  const auctionEnded = auctionStarted && remainingTime <= 0

  const [isLoading, setIsLoading] = React.useState(false)
  const [canCancelLoading, setCanCancelLoading] = React.useState(true)
  const onSubmitButtonClick = async () => {
    if (!auctionStarted) return
    if (!auctionEnded) return
    if (!ethersProvider) return
    setIsLoading(true)
    try {
      const txHash = await writeContractAsync({
        abi: auctionAbi,
        address: contractAddress,
        functionName: "withdraw",
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Withdraw success") +
            ". " +
            t("Data will be updated in few seconds")
        )
      } else {
        showError(t("Withdraw failed"))
      }
    } catch (error) {
      const errorMessage = `${t("Withdraw failed with error")} ${error}}`
      showError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  if (!userAddress) {
    return null
  }
  return (
    <>
      <View style={styles.container}>
        <Text style={textStyles.titleL}>{t("Backer Round")}</Text>
        <View style={styles.content}>
          {!isConnected && (
            <Text style={styles.errorText}>
              {t("Please login to withdraw your BRIK.")}
            </Text>
          )}
          <View style={styles.infoContainer}>
            {userAddress && (
              <>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>
                    {t("auction-YouHaveDeposited")}
                  </Text>
                  <View style={styles.infoValueContainer}>
                    <Text style={styles.infoValue}>
                      {parseCurrency(
                        currencyFormatter.formatFixed(
                          auctionDepositor?.depositedAmountWei
                        )
                      )}
                    </Text>
                    <Image source={usdtIcon} style={styles.coinImage} />
                  </View>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>
                    {t("auction-WithdrawableAmount")}
                  </Text>
                  <View style={styles.infoValueContainer}>
                    <Text style={styles.infoValue}>
                      {parseCurrency(
                        primaryTokenFormatter.format(withdrawableAmountWei)
                      )}
                    </Text>
                    <Image source={brikIcon} style={styles.coinImage} />
                  </View>
                </View>
              </>
            )}
          </View>
          {depositedAmount > 0 && withdrawableAmount <= 0 && (
            <Text style={styles.italicText}>
              {t("auction-YouHaveWithdrawnAllYourRewards")}
            </Text>
          )}
        </View>
        <PrimaryButton
          title={t("auction-Withdraw")}
          enabled={withdrawableAmount > 0}
          isLoading={isLoading}
          onPress={onSubmitButtonClick}
        />
      </View>
      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    padding: 16,
    borderRadius: 16,
    marginTop: 12,
    shadowColor: Colors.border,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  content: {
    marginVertical: 16,
  },
  errorText: {
    color: Colors.red,
    fontStyle: "italic",
  },
  infoContainer: {
    width: "100%",
    marginVertical: 8,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 4,
  },
  infoLabel: {
    flex: 1,
  },
  infoValueContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoValue: {
    marginRight: 8,
  },
  coinImage: {
    width: 16,
    height: 16,
  },
  italicText: {
    fontStyle: "italic",
    marginTop: 20,
  },
})
