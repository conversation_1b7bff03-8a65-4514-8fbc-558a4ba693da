import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"

type NextRewardCountDownProps = {
  remainingTime: number
  title: string
}

export const NextRewardCountDown: React.FC<NextRewardCountDownProps> = ({
  remainingTime,
  title,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={[styles.title]}>{title}</Text>
        <View style={styles.countdownGrid}>
          <CountDownItem
            value={Math.floor(remainingTime / (3600 * 24))}
            label={t("common-day")}
          />
          <CountDownItem
            value={Math.floor((remainingTime % (3600 * 24)) / 3600)}
            label={t("common-hour")}
          />
          <CountDownItem
            value={Math.floor((remainingTime % 3600) / 60)}
            label={t("common-min")}
          />
          <CountDownItem value={remainingTime % 60} label={t("common-sec")} />
        </View>
      </View>
    </View>
  )
}

type CountDownItemProps = {
  value: number
  label: string
}

export const CountDownItem: React.FunctionComponent<CountDownItemProps> = ({
  value,
  label,
}) => {
  return (
    <View style={styles.countdownItem}>
      <Text style={styles.countdownValue}>{value}</Text>
      <Text style={styles.countdownLabel}>{label}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    position: "relative",
    borderRadius: 16,
    overflow: "hidden",
    height: 256,
    backgroundColor: "white",
  },
  backgroundImage: {
    position: "absolute",
    width: "100%",
    height: 256,
    resizeMode: "cover",
  },
  content: {
    maxWidth: 320,
    marginHorizontal: "auto",
    paddingTop: 64,
    zIndex: 1,
    position: "relative",
  },
  title: {
    fontSize: 24,
    textAlign: "center",
    marginBottom: 16,
    color: "#E0E0E0",
    fontWeight: "600",
    textShadowColor: "rgba(0, 0, 0, 0.4)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 0,
  },
  countdownGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  countdownItem: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 16,
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    aspectRatio: 1,
    marginHorizontal: 4,
  },
  countdownValue: {
    fontSize: 24,
    color: "white",
  },
  countdownLabel: {
    fontSize: 12,
    color: "white",
  },
})
