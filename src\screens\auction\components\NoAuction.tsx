import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import {
  CONTRACT_ADDRESS_AUCTION_BACKER_ROUND,
  CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_1,
  CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_2,
  CONTRACT_ADDRESS_AUCTION_PUBLIC_SALE,
  CONTRACT_ADDRESS_AUCTION_SEED_ROUND,
} from "src/config/env"
import clockImage from "assets/images/img_clock.png"

type Props = {
  contractAddress: `0x${string}`
}

export const NoAuction: React.FunctionComponent<Props> = ({
  contractAddress,
}) => {
  const { t } = useTranslation()

  const getLabel = () => {
    switch (contractAddress) {
      case CONTRACT_ADDRESS_AUCTION_BACKER_ROUND:
        return t("Backer Round")
      case CONTRACT_ADDRESS_AUCTION_SEED_ROUND:
        return t("Seed Round")
      case CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_1:
        return t("Private Sale 1")
      case CONTRACT_ADDRESS_AUCTION_PRIVATE_SALE_2:
        return t("Private Sale 2")
      case CONTRACT_ADDRESS_AUCTION_PUBLIC_SALE:
        return t("Public Sale")
      default:
        return ""
    }
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{getLabel()}</Text>
      <View style={styles.content}>
        <View style={styles.imageContainer}>
          <Image source={clockImage} style={styles.image} />
        </View>
        <Text style={styles.text}>
          {getLabel()} {t("auction will start soon.")}
        </Text>
        <Text style={styles.text}>
          {t("Please stay tuned! We'll be back with more updates soon.")}
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 32,
    borderRadius: 16,
    marginTop: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontWeight: "600",
    color: "#4B5563",
    fontSize: 18,
    textAlign: "center",
  },
  content: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    textAlign: "center",
    color: "#6B7280",
  },
  imageContainer: {
    width: 104,
    marginVertical: 32,
  },
  image: {
    width: 120,
    height: 120,
    marginVertical: 50,
  },
  text: {
    color: "#6B7280",
    textAlign: "center",
    marginVertical: 4,
  },
})
