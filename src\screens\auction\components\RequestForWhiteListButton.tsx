import React, { useState } from "react"
import { PrimaryButton } from "components"
import { RequestForWhiteListModal } from "./RequestForWhiteListModal"
import { StyleSheet } from "react-native"
import { useTranslation } from "react-i18next"

export const RequestForWhiteListButton: React.FC = () => {
  const [isShow, setIsShow] = useState<boolean>(false)

  const { t } = useTranslation()

  return (
    <>
      <PrimaryButton
        style={styles.requestWhiteListButton}
        title={t("Request for whitelist")}
        onPress={() => {
          setIsShow(true)
        }}
      />
      <RequestForWhiteListModal
        visible={isShow}
        requestForWhiteList={() => {}}
        onClose={() => setIsShow(false)}
      />
    </>
  )
}

const styles = StyleSheet.create({
  requestWhiteListButton: {
    alignSelf: "flex-start",
    marginTop: 12,
  },
})
