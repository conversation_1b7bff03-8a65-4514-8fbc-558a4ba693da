import { Input<PERSON><PERSON>, PrimaryButton } from "components"
import { useTranslation } from "react-i18next"
import React from "react"
import { textStyles, viewStyles } from "src/config/styles"
import { StyleSheet, Text, View } from "react-native"
import { Controller, useForm } from "react-hook-form"
import { Dropdown } from "react-native-element-dropdown"
import { useMutation } from "@tanstack/react-query"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { postMyReferral } from "src/api"
import { BaseModal } from "components/common/BaseModal"
import { useAccount } from "wagmi"
import { showError, showSuccess } from "utils/toast"

type Props = {
  visible: boolean
  requestForWhiteList: () => void
  onClose: () => void
}

export const RequestForWhiteListModal: React.FunctionComponent<Props> = ({
  visible,
  onClose,
}) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = React.useState(false)
  const { address: userAddress } = useAccount()

  const formSchema = z.object({
    referralCode: z.string().refine((val) => val.length >= 8, {
      message: t("String must contain at least 8 character(s)"),
    }),
    round: z.string(),
    packageAmount: z.string(),
  })

  type Payload = z.infer<typeof formSchema>

  const postMyReferralMutation = useMutation({
    mutationFn: (formData: FormData) => {
      setIsLoading(true)
      return postMyReferral(formData, userAddress)
    },
    onError: (error) => {
      setIsLoading(false)
      showError(`${t("Request for tokenization failed")}: ${error.message}`)
    },
    onSuccess: () => {
      setIsLoading(false)
      showSuccess(t("Request for tokenization success"))
    },
  })

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    if (isLoading) return
    const payload = new FormData()
    payload.append("round", data.round)
    payload.append("packageAmount", data.packageAmount)
    payload.append("referralCode", data.referralCode)
    await postMyReferralMutation.mutateAsync(payload)
  }

  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      referralCode: "",
      round: "SEED",
      packageAmount: (100e18).toString(),
    },
  })
  return (
    <BaseModal
      visible={visible}
      isDisableClose={isLoading}
      onClose={onClose}
      title={t("Request for whitelist")}
      isShowCloseIcon={true}
    >
      <View style={styles.content}>
        <Controller
          control={form.control}
          name="referralCode"
          defaultValue={""}
          render={({ field: { onChange, onBlur, value } }) => (
            <InputField
              label={t("Referral Code")}
              style={{ marginTop: 12 }}
              onBlur={onBlur}
              placeholder={t("Referral Code")}
              value={value}
              onChangeText={onChange}
              error={
                form.formState.errors.referralCode?.message &&
                String(form.formState.errors.referralCode?.message)
              }
            />
          )}
        />
        <Text style={[textStyles.labelL, { marginTop: 12 }]}>
          {t("Investment round")}
        </Text>
        <Controller
          control={form.control}
          render={({ field: { onChange, value } }) => (
            <Dropdown
              value={value}
              data={[
                { label: t("Seed round"), value: "SEED" },
                { label: t("Private sale 1"), value: "PRIVATE_SALE_1" },
                { label: t("Private sale 2"), value: "PRIVATE_SALE_2" },
                { label: t("Public sale"), value: "PUBLIC_SALE" },
              ]}
              labelField="label"
              valueField="value"
              onChange={(item) => onChange(item.value)}
              style={[viewStyles.input, styles.dropdown]}
            />
          )}
          name="round"
          rules={{ required: true }}
        />

        <Text style={[textStyles.labelL, { marginTop: 12 }]}>
          {t("Investment package")}
        </Text>
        <Controller
          control={form.control}
          render={({ field: { onChange, value } }) => (
            <Dropdown
              value={value}
              data={[
                { label: t("100$"), value: (100e18).toString() },
                { label: t("1,000$"), value: (1000e18).toString() },
                { label: t("10,000$"), value: (10000e18).toString() },
              ]}
              labelField="label"
              valueField="value"
              onChange={(item) => onChange(item.value)}
              containerStyle={{ width: "100%" }}
              style={[viewStyles.input, styles.dropdown]}
            />
          )}
          name="packageAmount"
          rules={{ required: true }}
        />
        <View style={styles.buttonRow}>
          <PrimaryButton
            title={t("Request for whitelist")}
            isLoading={isLoading}
            onPress={form.handleSubmit(onSubmit)}
          />
        </View>
      </View>
    </BaseModal>
  )
}

const styles = StyleSheet.create({
  content: {
    alignContent: "center",
    width: "100%",
    paddingHorizontal: 10,
  },
  icon: {
    width: 16,
    height: 16,
  },
  referral: {
    marginTop: 16,
  },
  dropdown: {
    marginTop: 8,
  },
  buttonRow: {
    width: "100%",
    marginTop: 16,
  },
})
