import React, { useState } from "react"
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native"
import {
  queryOptions,
  useMutation,
  useSuspenseQuery,
} from "@tanstack/react-query"
import {
  createApplication,
  EstateTokenAreaUnit,
  getCurrencies,
  getMyProfile,
  User,
} from "src/api"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import {
  CustomCheckbox,
  DateTimeInput,
  ErrorLabel,
  InputField,
  PrimaryButton,
  SearchView,
  SelectCurrency,
  TopBar,
  UploadImages,
} from "components"
import { Controller, useForm } from "react-hook-form"
import AreaPicker from "components/areapicker"
import { z } from "zod"
import { Dropdown } from "react-native-element-dropdown"
import {
  formatCurrency,
  formatMoney,
  formatNumericByDecimals,
  isValidWalletAddress,
} from "utils"
import { zodResolver } from "@hookform/resolvers/zod"
import { useAtom } from "jotai"
import { profileAtom } from "src/context/AuthContext"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import Colors, { textColors } from "src/config/colors"
import { LabelView } from "components/common/LabelView"
import forwardIcon from "assets/images/ic_forward.png"
import { parseEther } from "@ethersproject/units"
import { showSuccess } from "utils/toast"
import QueryKeys from "src/config/queryKeys"
import { useHandleError } from "src/api/errors/handleError"
import Logger from "src/utils/logger"
import { useAccount } from "wagmi"

const logger = new Logger({ tag: "CreateNFTScreen" })

const getCurrenciesQueryOptions = queryOptions({
  queryKey: QueryKeys.CURRENCY.LIST,
  queryFn: () => getCurrencies(),
})

const durationUnitMap: Record<string, number> = {
  minute: 60,
  hour: 60 * 60,
  day: 60 * 60 * 24,
  week: 60 * 60 * 24 * 7,
  month: 60 * 60 * 24 * 30,
}

const isVietnameseUppercaseCharacter = (char: string) =>
  /^[AĂÂBCDĐEÊGHIKLMNOÔƠPQRSTUƯVXY]$/.test(char)

const isValidSerialNumber = (serial: string) => {
  return (
    serial.length === 8 &&
    /^\d+$/.test(serial.slice(2)) &&
    isVietnameseUppercaseCharacter(serial[0]) &&
    isVietnameseUppercaseCharacter(serial[1])
  )
}

const CreateNFTScreen: React.FC = () => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const { data: currencies = [] } = useSuspenseQuery(getCurrenciesQueryOptions)
  const [profile] = useAtom(profileAtom)
  const { address } = useAccount()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const { handleError } = useHandleError()

  const imageSchema = z.object({
    url: z.string(),
    fileName: z.string(),
    type: z.string(),
  })

  const sellingLimitSchema = z.object({
    sellingLimit: z
      .object({
        minSellingAmount: z.number().refine((val) => val > 0, {
          message: t("Please input the min selling amount"),
        }),
        maxSellingAmount: z.number().superRefine((val, ctx) => {
          if (val <= 0) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t("Please input the max selling amount"),
            })
            return false
          }
          if (val > totalSupply) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: t(
                "max selling amount must be less than or equal to total supply"
              ),
            })
            return false
          }
          return true
        }),
      })
      .refine((val) => +val.minSellingAmount <= +val.maxSellingAmount, {
        message: t(
          "Min selling amount must be less than or equal to max selling amount"
        ),
        path: ["minSellingAmount"],
      }),
  })

  const formWithoutSellingLimitSchema = z.object({
    name: z
      .string()
      .refine((val) => val.length >= 20, {
        message: t("Name must be at least 20 characters long"),
      })
      .refine((val) => val.length <= 100, {
        message: t("Name must be at most 100 characters long"),
      }),
    serial: z
      .string()
      .refine((val) => val.length > 0, {
        message: t("Not empty"),
      })
      .refine((val) => isValidSerialNumber(val), {
        message: t("Serial number is not in the correct format, ex: AB123456"),
      }),
    address: z
      .string()
      .refine((val) => val.length >= 20, {
        message: t("Address must be at least 20 characters long"),
      })
      .refine((val) => val.length <= 255, {
        message: t("Address must be at most 255 characters long"),
      }),
    addressCodeLevel1: z.string().refine((val) => val.length > 0, {
      message: t("Please select a city/province"),
    }),
    addressCodeLevel2: z.string().refine((val) => val.length > 0, {
      message: t("Please select a district"),
    }),
    addressCodeLevel3: z.string().refine((val) => val.length > 0, {
      message: t("Please select a ward"),
    }),
    area: z.number().refine((val) => val > 0, {
      message: t("Please input the area of the property"),
    }),
    areaUnit: z.string(),
    category: z.union([
      z.literal("RESIDENTIAL"),
      z.literal("PRODUCTION"),
      z.literal("AGRICULTURAL"),
      z.literal("NON_AGRICULTURAL"),
      z.literal("FOREST"),
      z.literal("PERENNIAL_CROP"),
      z.literal("INDUSTRIAL"),
    ]),
    description: z.string(),
    unitPrice: z.number().superRefine((val, ctx) => {
      if (
        val < Number(formatNumericByDecimals(currenMinUnitPrice, decimals)) ||
        val > Number(formatNumericByDecimals(currenMaxUnitPrice, decimals))
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t("Please input value in range from min to max", {
            min: formatCurrency(
              formatNumericByDecimals(currenMinUnitPrice, decimals)
            ),
            max: formatCurrency(
              formatNumericByDecimals(currenMaxUnitPrice, decimals)
            ),
          }),
        })
        return false
      }

      return true
    }),
    currencyId: z.string().min(1),
    decimals: z.number().refine((val) => val > 0, {
      message: t("Please input the decimals"),
    }),
    totalSupply: z.number().refine((val) => val > 0, {
      message: t("Please input the total supply"),
    }),
    landUseRights: z.array(imageSchema).refine((val) => val.length > 1, {
      message: t("Please upload at least 2 images of land use rights"),
    }),
    landMedia: z.array(imageSchema).refine((val) => val.length > 3, {
      message: t("Please upload at least 4 images of the property"),
    }),
    thumnail: z.array(imageSchema).refine((val) => val.length > 0, {
      message: t("Please upload thumnail"),
    }),
    expiredAt: z.string().superRefine((val, ctx) => {
      if (isInfinite) {
        return true
      }
      if (isNaN(Date.parse(val))) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t("Please input property expiry date"),
        })
        return false
      }
      const currentDate = new Date()
      const next1Years = new Date(currentDate).setFullYear(
        currentDate.getFullYear() + 1
      )
      if (Date.parse(val) < next1Years) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t("The expiry date must be at least 1 year from now"),
        })
        return false
      }

      return true
    }),
    isInfinite: z.boolean(),
    duration: z.number().refine((val) => val > 0, {
      message: t("Please input the public sale duration"),
    }),
    durationUnit: z.string(),
    brokerAddress: z
      .string()
      .refine((val) => isValidWalletAddress(val) || val === "", {
        message: t("Invalid wallet address"),
      }),
    requesterAddress: z
      .string()
      .refine(
        (val) => {
          if (isCurrentUserJustSeller) return true
          return val.length > 0
        },
        {
          message: t("Please enter seller's wallet address"),
        }
      )
      .refine(
        (val) => {
          if (isCurrentUserJustSeller) return true
          return isValidWalletAddress(val)
        },
        {
          message: t("Invalid wallet address"),
        }
      ),
  })

  const formSchema = z.intersection(
    sellingLimitSchema,
    formWithoutSellingLimitSchema
  )

  type Payload = z.infer<typeof formSchema>

  const mutationCreateNFT = useMutation({
    mutationFn: (formData: FormData) => {
      setIsLoading(true)
      return createApplication(formData)
    },
    onError: (error: any) => {
      setIsLoading(false)
      handleError(error, t("Request for tokenization failed"))
    },
    onSuccess: () => {
      setIsLoading(false)
      showSuccess(t("Request for tokenization success"))
      navigation.goBack()
    },
  })

  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      serial: "",
      name: "",
      address: "",
      area: 0,
      category: "RESIDENTIAL",
      unitPrice: 0,
      areaUnit: EstateTokenAreaUnit.SQM,
      sellingLimit: {
        minSellingAmount: 0,
        maxSellingAmount: 0,
      },
      totalSupply: 0,
      landUseRights: [],
      landMedia: [],
      thumnail: [],
      decimals: 18,
      currencyId: currencies[0]?.currency,
      addressCodeLevel1: "",
      addressCodeLevel2: "",
      addressCodeLevel3: "",
      expiredAt: "",
      isInfinite: false,
      duration: 1,
      durationUnit: "day",
      description: "",
      brokerAddress: "",
      requesterAddress: "",
    },
  })

  const [
    addressCodeLevel1,
    addressCodeLevel2,
    addressCodeLevel3,
    unitPrice,
    currencyId,
    isInfinite,
    decimals,
    totalSupply,
  ] = form.watch([
    "addressCodeLevel1",
    "addressCodeLevel2",
    "addressCodeLevel3",
    "unitPrice",
    "currencyId",
    "isInfinite",
    "decimals",
    "totalSupply",
  ])

  const selectedCurrency = currencies.find((c) => c.currency === currencyId)

  const currenSymbol = selectedCurrency?.symbol
  const currenMinUnitPrice = selectedCurrency?.minUnitPrice || "0"
  const currenMaxUnitPrice = selectedCurrency?.maxUnitPrice || "0"

  const isVerified = profile?.status === "VERIFIED"

  const isCurrentUserAdmin = profile?.isManager || profile?.isModerator
  const isCurrentUserJustBroker = profile?.isBroker && !isCurrentUserAdmin
  const isCurrentUserJustSeller =
    !isCurrentUserJustBroker && !isCurrentUserAdmin

  const location = {
    addressCodeLevel1,
    addressCodeLevel2,
    addressCodeLevel3,
  }

  const onChangeLocation = (level: string, value: string) => {
    if (level === "addressCodeLevel1") {
      form.setValue("addressCodeLevel1", value)
      form.clearErrors("addressCodeLevel1")
      form.setValue("addressCodeLevel2", "")
      form.setValue("addressCodeLevel3", "")
    } else if (level === "addressCodeLevel2") {
      form.clearErrors("addressCodeLevel2")
      form.setValue("addressCodeLevel2", value)
      form.setValue("addressCodeLevel3", "")
    } else {
      form.clearErrors("addressCodeLevel3")
      form.setValue("addressCodeLevel3", value)
    }
  }

  const BROKER_SEARCH_TYPE = "broker"
  const SELLER_SEARCH_TYPE = "seller"
  const [brokerInfo, setBrokerInfo] = useState<User | null>(null)
  const [searchBrokerWarning, setSearchBrokerWarning] = useState<string | null>(
    null
  )
  const [sellerInfo, setSellerInfo] = useState<User | null>(null)
  const [searchSellerWarning, setSearchSellerWarning] = useState<string | null>(
    null
  )

  const useSearchUserMutation = (
    onSuccessCallback: (profile: User) => void,
    onErrorCallback: () => void
  ) => {
    return useMutation({
      mutationFn: (walletAddress: string) => {
        setIsLoading(true)
        return getMyProfile(walletAddress)
      },
      onError: (error: any) => {
        handleError(error)
        onErrorCallback()
      },
      onSuccess: (profile: User) => {
        onSuccessCallback(profile)
      },
      onSettled: () => {
        setIsLoading(false)
      },
    })
  }

  const mutationGetBrokerInfo = useSearchUserMutation(
    (brokerProfile) => {
      if (brokerProfile.address === profile?.address) {
        setSearchBrokerWarning(
          t("This wallet address is the same as your wallet address")
        )
        setBrokerInfo(null)
      } else if (brokerProfile.isBroker) {
        setSearchBrokerWarning("")
        setBrokerInfo(brokerProfile)
      } else {
        setSearchBrokerWarning(t("Wallet address not found"))
        setBrokerInfo(null)
      }
    },
    () => {
      setSearchBrokerWarning(t("Wallet address not found"))
      setBrokerInfo(null)
    }
  )

  const mutationGetSellerInfo = useSearchUserMutation(
    (sellerProfile) => {
      setSearchSellerWarning("")
      setSellerInfo(sellerProfile)
    },
    () => {
      setSearchSellerWarning(t("Wallet address not found"))
      setSellerInfo(null)
    }
  )

  const onChangeSearchUser =
    (onChange: (value: string | number) => void, type: string) =>
    async (text: string | number) => {
      if (!isVerified) return

      const walletAddress = String(text)
      onChange(walletAddress)

      if (isValidWalletAddress(walletAddress)) {
        if (type === BROKER_SEARCH_TYPE) {
          mutationGetBrokerInfo.mutate(walletAddress)
        } else if (type === SELLER_SEARCH_TYPE) {
          mutationGetSellerInfo.mutate(walletAddress)
        }
      } else {
        if (type === BROKER_SEARCH_TYPE) {
          setSearchBrokerWarning(t(""))
          setBrokerInfo(null)
        } else if (type === SELLER_SEARCH_TYPE) {
          setSearchSellerWarning(t(""))
          setSellerInfo(null)
        }
      }
    }

  const onSubmit = ({
    landUseRights,
    landMedia,
    thumnail,
    ...data
  }: Payload) => {
    if (isLoading) return

    // TODO
    const zone = "VIETNAM"
    const credentialType = "VN001"
    const credentialId = "AĐ12345678"

    const formData = new FormData()
    const decimalsPower = BigInt(10) ** BigInt(data.decimals)
    formData.append("zone", zone)
    formData.append("credentialType", credentialType)
    formData.append("credentialId", credentialId)
    formData.append(
      "attributes",
      JSON.stringify({ traitType: "Category", value: data.category })
    )

    formData.append("name", data.name)
    formData.append("address", data.address)
    formData.append("localeDetailVietnamLevel1", data.addressCodeLevel1)
    formData.append("localeDetailVietnamLevel2", data.addressCodeLevel2)
    formData.append("localeDetailVietnamLevel3", data.addressCodeLevel3)
    formData.append("description", data.description)
    formData.append(
      "area",
      JSON.stringify({ value: data.area.toString(), unit: data.areaUnit })
    )
    formData.append("category", data.category)
    formData.append("serial", data.serial)
    formData.append(
      "unitPrice",
      parseEther(data.unitPrice.toString()).toString()
    )
    formData.append("currency", data.currencyId)
    formData.append(
      "minSellingAmount",
      (BigInt(data.sellingLimit.minSellingAmount) * decimalsPower).toString()
    )
    formData.append(
      "maxSellingAmount",
      (BigInt(data.sellingLimit.maxSellingAmount) * decimalsPower).toString()
    )
    formData.append(
      "totalSupply",
      (BigInt(data.totalSupply) * decimalsPower).toString()
    )

    formData.append(
      "expireAt",
      data.isInfinite
        ? (Math.pow(2, 40) - 1).toString()
        : Math.floor(new Date(data.expiredAt!).getTime() / 1000).toString()
    )
    formData.append("decimals", data.decimals.toString())
    formData.append(
      "duration",
      (Number(data.duration) * durationUnitMap[data.durationUnit]).toString()
    )
    landUseRights.forEach((photo) => {
      // @ts-ignore
      formData.append("credentialPhotos", {
        uri: photo.url,
        type: photo.type,
        name: photo.fileName,
      })
    })

    landMedia.forEach((photo) => {
      // @ts-ignore
      formData.append("estatePhotos", {
        uri: photo.url,
        type: photo.type,
        name: photo.fileName,
      })
    })

    thumnail.forEach((photo) => {
      // @ts-ignore
      formData.append("image", {
        uri: photo.url,
        type: photo.type,
        name: photo.fileName,
      })
    })
    formData.append("brokerAddress", data.brokerAddress)

    if (isCurrentUserJustSeller) {
      formData.append("requesterAddress", address || "")
    } else {
      formData.append("requesterAddress", data.requesterAddress)
    }

    mutationCreateNFT.mutate(formData)
  }

  Object.keys(form.formState.errors).forEach((error) => {
    logger.error("Form state error", error)
  })

  return (
    <ScrollView>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.container}>
          <TopBar
            enableBack={true}
            title={t("Request for Real Estate Tokenization").toUpperCase()}
          />

          {!isVerified && (
            <View
              style={{
                backgroundColor: Colors.primaryLight,
                padding: 16,
                borderRadius: 8,
                alignItems: "center",
              }}
            >
              <Text style={[textStyles.bodyM, { color: Colors.red }]}>
                {t("You need to verify your account to start creating NFT.")}
              </Text>
            </View>
          )}

          {(isCurrentUserAdmin || isCurrentUserJustBroker) && (
            <View style={styles.brokerSellerSection}>
              <Text style={textStyles.titleS}>{t("Owner information")}</Text>
              <Controller
                control={form.control}
                name="requesterAddress"
                render={({ field: { onChange, value } }) => (
                  <View style={styles.marginTopDefault}>
                    <LabelView label={t("Wallet address")} require={true} />
                    <SearchView
                      value={value}
                      onChangeText={onChangeSearchUser(
                        onChange,
                        SELLER_SEARCH_TYPE
                      )}
                      style={styles.marginTopDefault}
                      multiline={false}
                      error={
                        form.formState.errors.requesterAddress?.message &&
                        String(form.formState.errors.requesterAddress?.message)
                      }
                      placeholder={t("Enter wallet address")}
                    />
                  </View>
                )}
              />
              {searchSellerWarning && (
                <Text style={styles.searchWarning}>{searchSellerWarning}</Text>
              )}
              <LabelView
                style={styles.marginTopDefault}
                label={`${t("Full name")}:`}
                require={false}
              />
              <Text style={styles.brokerSellerName}>
                {sellerInfo ? sellerInfo.alias : ""}
              </Text>
            </View>
          )}

          <Controller
            control={form.control}
            name="name"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Name of the property")}
                value={value}
                require={true}
                onChangeText={onChange}
                onBlur={onBlur}
                style={styles.marginTopDefault}
                placeholder={t("Example City center apartment")}
                error={
                  form.formState.errors.name?.message &&
                  String(form.formState.errors.name?.message)
                }
              />
            )}
          />
          <Controller
            control={form.control}
            name="serial"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Serial Number")}
                value={value}
                onChangeText={onChange}
                require={true}
                style={styles.marginTopDefault}
                onBlur={onBlur}
                placeholder={t("Example AB123456")}
                error={
                  form.formState.errors.serial?.message &&
                  String(form.formState.errors.serial?.message)
                }
              />
            )}
          />
          <Controller
            control={form.control}
            name="expiredAt"
            render={({ field: { onChange, value } }) => (
              <>
                <DateTimeInput
                  title={t("Date of expiry")}
                  value={value}
                  require={true}
                  disabled={isInfinite}
                  minimumDate={new Date()}
                  onChangeDate={(date) => {
                    onChange(date?.toDateString() || "")
                  }}
                />
                <ErrorLabel error={form.formState.errors.expiredAt?.message} />
              </>
            )}
          />
          <Controller
            control={form.control}
            name="isInfinite"
            render={({ field: { onChange, value } }) => (
              <CustomCheckbox
                label={t("Is Infinite")}
                isChecked={value}
                onToggle={onChange}
              />
            )}
          />
          <AreaPicker
            {...location}
            onChange={onChangeLocation}
            errorMessage={
              form.formState.errors.addressCodeLevel1?.message ||
              form.formState.errors.addressCodeLevel2?.message ||
              form.formState.errors.addressCodeLevel3?.message
            }
            style={styles.areaPicker}
          />
          <Controller
            control={form.control}
            name="address"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Address")}
                value={value}
                require={true}
                onChangeText={onChange}
                onBlur={onBlur}
                style={{ marginTop: 12 }}
                placeholder={t("Example No. 1, Nguyen Hue Street")}
                error={
                  form.formState.errors.address?.message &&
                  String(form.formState.errors.address?.message)
                }
              />
            )}
          />
          <LabelView
            label={t("Area")}
            require={true}
            style={styles.marginTopDefault}
          />
          <View
            style={{
              flexDirection: "row",
              marginTop: 8,
              alignItems: "flex-start",
            }}
          >
            <Controller
              control={form.control}
              name="area"
              render={({ field: { onChange, onBlur, value } }) => (
                <InputField
                  value={value}
                  require={true}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  inputMode={"decimal"}
                  type={"number"}
                  style={{ flex: 1 }}
                  error={
                    form.formState.errors.area?.message &&
                    String(form.formState.errors.area?.message)
                  }
                />
              )}
            />
            <Controller
              control={form.control}
              name="areaUnit"
              render={({ field: { onChange, value } }) => (
                <Dropdown
                  value={value}
                  data={[
                    { label: t("sqm"), value: EstateTokenAreaUnit.SQM },
                    { label: t("sqft"), value: EstateTokenAreaUnit.SQFT },
                  ]}
                  labelField={"label"}
                  valueField={"value"}
                  onChange={({ value }) => onChange(value)}
                  style={[viewStyles.input, { width: 100, marginStart: 8 }]}
                  selectedTextStyle={styles.dropdownItem}
                  placeholderStyle={styles.dropdownItem}
                  itemTextStyle={styles.dropdownItem}
                />
              )}
            />
          </View>
          <LabelView
            label={t("Denominations")}
            require={true}
            style={styles.marginTopDefault}
          />
          <Text style={textStyles.labelL}>
            {t("Price of each ERC1155 token")}
          </Text>
          <View style={{ flexDirection: "row", marginTop: 8 }}>
            <Controller
              control={form.control}
              name="unitPrice"
              render={({ field: { onChange, onBlur, value } }) => (
                <View style={{ width: "40%" }}>
                  <InputField
                    require={true}
                    value={value}
                    inputMode={"numeric"}
                    type={"number"}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={form.formState.errors.unitPrice?.message}
                  />
                </View>
              )}
            />
            <SelectCurrency
              currencies={currencies}
              control={form.control}
              isShowMinMaxPrice={true}
              setValue={form.setValue}
            />
          </View>
          <Controller
            control={form.control}
            name="decimals"
            render={({ field: { onChange, onBlur, value } }) => (
              <>
                <InputField
                  label={t("Decimals")}
                  require={true}
                  value={value}
                  type={"number"}
                  inputMode={"numeric"}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  style={styles.marginTopDefault}
                  error={form.formState.errors.decimals?.message}
                />
              </>
            )}
          />
          <Controller
            control={form.control}
            name="totalSupply"
            render={({ field: { onChange, value } }) => (
              <>
                <InputField
                  label={t("ERC1155 total supply")}
                  value={value}
                  require={true}
                  type={"number"}
                  inputMode={"decimal"}
                  onChangeText={onChange}
                  style={styles.marginTopDefault}
                  error={form.formState.errors.totalSupply?.message}
                />
                {!isNaN(+unitPrice) && !isNaN(+value) && (
                  <Text style={styles.propertyValue}>
                    {t("Property value")}: {formatMoney(+unitPrice * +value)}{" "}
                    {currenSymbol}
                  </Text>
                )}
              </>
            )}
          />
          <LabelView
            label={t("Selling amount")}
            require={true}
            style={styles.marginTopDefault}
          />
          <Text style={[textStyles.bodyM, { marginTop: 4 }]}>
            {t(
              "The total sold amount cannot exceed the maximum selling amount." +
                " If the minimum selling amount is not met by the end of the selling period," +
                " the tokenization process will fail."
            )}
          </Text>
          <View style={styles.row}>
            <Controller
              control={form.control}
              name="sellingLimit.minSellingAmount"
              render={({ field: { onChange, value } }) => (
                <View style={{ flex: 1 }}>
                  <InputField
                    value={value}
                    require={true}
                    label={t("Minimum selling amount")}
                    type={"number"}
                    inputMode={"decimal"}
                    onChangeText={onChange}
                    error={
                      form.formState.errors.sellingLimit?.minSellingAmount
                        ?.message
                    }
                  />
                </View>
              )}
            />
            <Image
              source={forwardIcon}
              style={[viewStyles.tinyIcon, styles.forward]}
            />
            <Controller
              control={form.control}
              name="sellingLimit.maxSellingAmount"
              render={({ field: { onChange, value } }) => (
                <View style={{ flex: 1 }}>
                  <InputField
                    value={value}
                    require={true}
                    label={t("Maximum selling amount")}
                    type={"number"}
                    inputMode={"decimal"}
                    onChangeText={onChange}
                    error={
                      form.formState.errors.sellingLimit?.maxSellingAmount
                        ?.message
                    }
                  />
                </View>
              )}
            />
          </View>
          <View>
            <Text style={[textStyles.labelL, styles.marginTopDefault]}>
              {t("Public sale duration")}
            </Text>
            <View style={{ flexDirection: "row", marginTop: 8 }}>
              <Controller
                control={form.control}
                name="duration"
                render={({ field: { onChange, value } }) => (
                  <View style={{ flex: 1 }}>
                    <InputField
                      value={value}
                      require={true}
                      type={"number"}
                      inputMode={"decimal"}
                      onChangeText={onChange}
                      error={form.formState.errors.duration?.message}
                    />
                  </View>
                )}
              />
              <Controller
                control={form.control}
                name="durationUnit"
                render={({ field: { onChange, value } }) => (
                  <Dropdown
                    value={value}
                    data={Object.keys(durationUnitMap).map((unit) => ({
                      label: t(unit),
                      value: unit,
                    }))}
                    labelField={"label"}
                    valueField={"value"}
                    onChange={({ value }) => onChange(value)}
                    style={[viewStyles.input, { width: 150, marginStart: 8 }]}
                    selectedTextStyle={styles.dropdownItem}
                    placeholderStyle={styles.dropdownItem}
                    itemTextStyle={styles.dropdownItem}
                  />
                )}
              />
            </View>
            <Controller
              control={form.control}
              name="landUseRights"
              render={({ field: { onChange, value } }) => (
                <>
                  <LabelView
                    label={t("Land use rights images")}
                    require={true}
                    style={styles.marginTopDefault}
                  />
                  <Text style={[textStyles.bodyM, { marginVertical: 4 }]}>
                    {t(
                      "Requires full and clear photos of both sides of documents"
                    )}
                  </Text>
                  <UploadImages images={value} onChangeImages={onChange} />
                  <ErrorLabel
                    error={form.formState.errors.landUseRights?.message}
                  />
                </>
              )}
            />
          </View>
          <Controller
            control={form.control}
            name="landMedia"
            render={({ field: { onChange, value } }) => (
              <>
                <LabelView
                  label={t("Images of the property")}
                  require={true}
                  style={styles.marginTopDefault}
                />
                <UploadImages images={value} onChangeImages={onChange} />
                <ErrorLabel error={form.formState.errors.landMedia?.message} />
              </>
            )}
          />
          <Controller
            control={form.control}
            name="thumnail"
            render={({ field: { onChange, value } }) => (
              <>
                <LabelView
                  label={t("Thumnail of property")}
                  require={true}
                  style={styles.marginTopDefault}
                />
                <UploadImages
                  images={value}
                  isHideAddImageIcon={value.length > 0}
                  onChangeImages={onChange}
                />
                <ErrorLabel error={form.formState.errors.thumnail?.message} />
              </>
            )}
          />
          <Controller
            control={form.control}
            name="description"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Property description")}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                multiline={true}
                height={100}
                error={form.formState.errors.description?.message}
              />
            )}
          />

          {(isCurrentUserAdmin || isCurrentUserJustSeller) && (
            <View style={styles.brokerSellerSection}>
              <Text style={textStyles.titleS}>{t("Broker information")}</Text>
              <Controller
                control={form.control}
                name="brokerAddress"
                render={({ field: { onChange, value } }) => (
                  <View style={styles.marginTopDefault}>
                    <LabelView label={t("Wallet address")} />
                    <SearchView
                      value={value}
                      onChangeText={onChangeSearchUser(
                        onChange,
                        BROKER_SEARCH_TYPE
                      )}
                      style={styles.marginTopDefault}
                      multiline={false}
                      error={
                        form.formState.errors.brokerAddress?.message &&
                        String(form.formState.errors.brokerAddress?.message)
                      }
                      placeholder={t("Enter wallet address")}
                    />
                  </View>
                )}
              />
              {searchBrokerWarning && (
                <Text style={styles.searchWarning}>{searchBrokerWarning}</Text>
              )}
              <LabelView
                style={styles.marginTopDefault}
                label={`${t("Full name")}:`}
                require={false}
              />
              <Text style={styles.brokerSellerName}>
                {brokerInfo ? brokerInfo.alias : ""}
              </Text>
            </View>
          )}

          <PrimaryButton
            title={t("Request for tokenization")}
            onPress={form.handleSubmit(onSubmit)}
            style={{ marginTop: 16 }}
            isLoading={isLoading}
            enabled={isVerified}
          />
        </View>
      </KeyboardAvoidingView>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    padding: 16,
  },
  areaPicker: {
    marginTop: 12,
    borderRadius: 8,
    borderWidth: 1,
    padding: 8,
    borderColor: Colors.black5,
  },
  dropdownItem: {
    ...textStyles.bodyM,
    color: textColors.textBlack9,
    height: 20,
  },
  row: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginTop: 8,
  },
  marginTopDefault: {
    marginTop: 8,
  },
  marginHorizontalDefault: {
    marginHorizontal: 8,
  },
  forward: {
    marginHorizontal: 8,
    marginTop: 40,
  },
  brokerSellerName: {
    marginTop: 8,
    paddingStart: 8,
    borderWidth: 1,
    borderColor: Colors.black5,
    borderRadius: 8,
    paddingVertical: 12,
    backgroundColor: Colors.whiteGhost,
  },
  brokerSellerSection: {
    backgroundColor: Colors.white,
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: Colors.black5,
  },
  searchWarning: {
    marginTop: 8,
    ...textStyles.bodyS,
    color: Colors.yellow,
  },
  searchIcon: {
    marginEnd: 12,
  },
  propertyValue: {
    ...textStyles.bodyM,
    marginTop: 8,
  },
})

export default CreateNFTScreen
