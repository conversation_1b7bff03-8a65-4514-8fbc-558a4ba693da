import React, { useRef, useState } from "react"
import { ScrollView, StyleSheet, Text, View } from "react-native"
import { Background, GetInTouch, GradientText } from "src/components"
import HomeIntroduction from "./components/HomeIntroduction"
import HomeTokenization from "./components/HomeTokenization"
import HomeTokenomics from "./components/HomeTokenomics"
import HomeJourney from "./components/HomeJourney"
import { HomeWhatIsBrikyLand } from "./components/HomeWhatIsBrikyLand"
import { default as BuildConfig } from "expo-constants"
import { useTranslation } from "react-i18next"
import Colors, { textColors } from "src/config/colors"
import { textStyles } from "src/config/styles"

interface LayoutPosition {
  x: number
  y: number
  width: number
  height: number
}

const Header: React.FC = () => {
  const { t } = useTranslation()
  return (
    <>
      <GradientText
        style={styles.brandTitle}
        text={BuildConfig.expoConfig?.name ?? ""}
      />
      <Text style={styles.heading}>
        {t("A new standard in Real Estate Platform")}
      </Text>
      <View
        style={[styles.auctionContainer, { marginStart: 12, marginEnd: 12 }]}
      >
        <Text style={styles.auction}>{t("Seed Round")}</Text>
        <GradientText
          text={t("Closing Soon!")}
          style={styles.auctionGradient}
        />
      </View>
    </>
  )
}

const HomeView: React.FC = () => {
  // const { t } = useTranslation()
  const scrollViewRef = useRef<ScrollView>(null)
  const tokenizationRef = useRef<View>(null)
  const journeyRef = useRef<View>(null)
  // const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const [sectionsLayout, setSectionsLayout] = useState<
    Record<string, LayoutPosition>
  >({})

  const handleLayout = (sectionKey: string, event: any) => {
    const { x, y, width, height } = event.nativeEvent.layout
    setSectionsLayout((prev) => ({
      ...prev,
      [sectionKey]: { x, y, width, height },
    }))
  }

  const scrollToSection = (sectionKey: string) => {
    const layout = sectionsLayout[sectionKey]
    if (layout) {
      scrollViewRef.current?.scrollTo({ y: layout.y, animated: true })
    }
  }

  return (
    <ScrollView
      ref={scrollViewRef}
      contentContainerStyle={styles.scrollContainer}
    >
      <Background>
        <View style={styles.container}>
          <Header />
          {/* TODO: wait confirm with PO where will be navigate to */}
          {/* <GradientButton
            style={styles.primaryButton}
            title={t("Explore now").toUpperCase()}
            width={200}
            height={48}
            borderRadius={24}
            onPress={() => navigation.navigate(Router.Auction)}
          /> */}
          <HomeIntroduction
            scrollViewRef={scrollViewRef}
            scrollToSection={scrollToSection}
          />
          <HomeWhatIsBrikyLand
            onLayout={(event) => handleLayout("introduction", event)}
          />
          <View style={styles.spacer} />
          <HomeTokenization
            ref={tokenizationRef}
            onLayout={(event) => handleLayout("tokenization", event)}
          />
          <HomeTokenomics />
          <HomeJourney
            ref={journeyRef}
            onLayout={(event) => handleLayout("journey", event)}
          />
          <GetInTouch />
        </View>
      </Background>
    </ScrollView>
  )
}

export default HomeView

const styles = StyleSheet.create({
  scrollContainer: {
    backgroundColor: Colors.white,
    flexGrow: 1,
  },
  container: {
    flex: 1,
    alignItems: "center",
    paddingTop: 60,
    paddingHorizontal: 16,
  },
  brandTitle: {
    ...textStyles.h3,
    fontFamily: "Audiowide",
    marginBottom: 12,
    textAlign: "center",
  },
  heading: {
    ...textStyles.h4Large,
    fontFamily: "Arimo",
    textAlign: "center",
    marginHorizontal: 16,
    color: textColors.textBlack10,
  },
  coundown: {
    backgroundColor: Colors.white,
    flex: 1,
    marginVertical: 12,
    paddingVertical: 16,
    paddingStart: 12,
    justifyContent: "center",
    alignItems: "flex-start",
  },
  auctionContainer: {
    width: "100%",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 32,
  },
  auction: {
    ...textStyles.titleL,
    lineHeight: 22,
    fontFamily: "Arimo",
    color: textColors.textBlack10,
    textAlign: "center",
  },
  auctionGradient: {
    ...textStyles.titleL,
    marginLeft: 4,
    lineHeight: 22,
    fontFamily: "Arimo",
    textAlign: "center",
  },
  primaryButton: {
    marginTop: 24,
  },
  spacer: {
    height: 16,
  },
})
