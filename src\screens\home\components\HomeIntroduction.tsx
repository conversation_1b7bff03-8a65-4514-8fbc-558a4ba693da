import React from "react"
import {
  Image,
  ImageSourcePropType,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from "react-native"
import { CardView } from "src/components"
import { textStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import { textColors } from "src/config/colors"
import presentationIcon from "assets/images/ic_presentation.png"
import tokenomicsIcon from "assets/images/ic_tokenomics.png"
import journeyIcon from "assets/images/ic_journey.png"

const useIntroData = () => {
  const { t } = useTranslation()

  const introData: IntroSectionData[] = [
    {
      image: presentationIcon,
      title: t("What is Briky Land"),
      descriptions: [
        t(
          "A revolutionary NFT platform empowering users to own, trade, and create securely on virtual land."
        ),
      ],
    },
    {
      image: tokenomicsIcon,
      title: t("Tokenomics"),
      descriptions: [
        t(
          "Aiming to sustain growth and stability through “fair distribution”."
        ),
        t("We foster long-term value and active user participation."),
      ],
    },
    {
      image: journeyIcon,
      title: t("Road map"),
      descriptions: [
        t("Key milestones."),
        t("Strategic goals."),
        t("Planning to grow the user base."),
        t("Integrating our advanced features."),
      ],
    },
  ]
  return introData
}

interface HomeIntroductionProps {
  scrollViewRef: React.RefObject<ScrollView>
  scrollToSection: (sectionKey: string) => void
}

interface IntroSectionData {
  image: ImageSourcePropType
  title: string
  descriptions: string[]
}

interface IntroSectionProps {
  onPress?: () => void
  data: IntroSectionData
  style?: ViewStyle
}

interface IntroCardProps {
  style?: ViewStyle
  title: string
  titleColor?: string
  descriptions: string[]
  image?: ImageSourcePropType
}

const HomeIntroduction: React.FC<HomeIntroductionProps> = ({
  scrollToSection,
}) => {
  const introData = useIntroData()
  const sectionKeys = ["introduction", "tokenization", "journey"]

  return (
    <View style={styles.center}>
      {introData.map((data, index) => (
        <IntroSection
          key={data.title}
          onPress={() => scrollToSection(sectionKeys[index])}
          data={data}
          style={{ marginTop: index === 0 ? 40 : 16 }}
        />
      ))}
    </View>
  )
}

const IntroSection: React.FC<IntroSectionProps> = ({
  onPress,
  data,
  style,
}) => (
  <Pressable style={styles.center} onPress={onPress}>
    <IntroCard
      style={style}
      image={data.image}
      title={data.title}
      descriptions={data.descriptions}
    />
  </Pressable>
)

const IntroCard: React.FC<IntroCardProps> = ({
  style,
  title,
  titleColor = textColors.textBlack11,
  descriptions,
  image,
}) => (
  <CardView style={{ ...styles.container, ...style }} elevation={10}>
    {image && <Image source={image} style={styles.image} />}
    <Text style={[styles.title, { color: titleColor }]}>
      {title.toUpperCase()}
    </Text>
    {descriptions.map((description, index) => (
      <Text style={styles.description} key={index}>
        {"\u2022"} {description}
      </Text>
    ))}
  </CardView>
)

export default HomeIntroduction

const styles = StyleSheet.create({
  container: {
    width: "100%",
    padding: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  center: {
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  image: {
    width: 64,
    height: 64,
  },
  title: {
    ...textStyles.titleL,
    marginVertical: 20,
    textAlign: "center",
  },
  description: {
    ...textStyles.bodyM,
    width: "100%",
    textAlign: "justify",
    color: textColors.textBlack9,
  },
})
