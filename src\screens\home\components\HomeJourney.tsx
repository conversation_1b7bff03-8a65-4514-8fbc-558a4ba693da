import React, { forwardRef } from "react"
import { LayoutChangeEvent, StyleSheet, Text, View } from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { textStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"

interface JourneyItem {
  title: string
  description: string
}

interface Journey {
  time: string
  items: JourneyItem[]
  color: string
}

interface Props {
  onLayout?: (event: LayoutChangeEvent) => void
}

const HomeJourney = forwardRef<View, Props>(({ onLayout }, ref) => {
  const { t } = useTranslation()
  const journeyData: Journey[] = [
    {
      time: "Q3 - 2024",
      color: "#4CB074",
      items: [
        {
          title: t("Briky Land Platform Announcement"),
          description: t(
            "Introducing the Briky Land Platform, including our comprehensive Native Land NFT Collection, Native Land NFT Marketplace and the Testnet Beta Platform launch for development and testing purpose."
          ),
        },
        {
          title: t("BRIK ICO"),
          description: t(
            "Initiation of the BRIK Initial Coin Offering through on-chain auctions with 5 stages of investments"
          ),
        },
        {
          title: t("BRIKI"),
          description: t(
            "Introduction of BRIKI, a derivative token designed for staking BRIK."
          ),
        },
      ],
    },
    {
      time: "Q4 - 2024",
      color: "#E91E63",
      items: [
        {
          title: t("Native Land P2P Lending"),
          description: t(
            "Launch Global P2P features helping people approach low-cost fund. This platform will enable users to leverage their Native Land NFTs as collateral to secure loans, fostering a decentralized lending ecosystem"
          ),
        },
        {
          title: t("Governor Hub"),
          description: t(
            "Launch of Governor Hub, our governance platform that allows real estate stakeholders to participate in decision-making processes, ensuring a decentralized and community-driven approach."
          ),
        },
        {
          title: t("Real Estate Renting Platform"),
          description: t(
            "Introduction of our Real Estate Renting Platform, enabling users to lease tokenized real estate properties, providing a new dimension of utility and income generation within our ecosystem."
          ),
        },
      ],
    },
    {
      time: "Q2 - 2025",
      color: "#2196F3",
      items: [
        {
          title: t("Native Land DEX"),
          description: t(
            "Deployment of a Native Land NFT DEX, a transparent and efficient system for listing and trading Native Land NFTs, ensuring optimal market operations and price discovery."
          ),
        },
      ],
    },
    {
      time: "Q4 - 2025",
      color: "#00BCD4",
      items: [
        {
          title: t("Unlock BRIK Liquidation"),
          description: t(
            "Enabling BRIK token liquidation, providing liquidity options for token holders to seamlessly convert their tokens into other assets."
          ),
        },
      ],
    },
  ]

  return (
    <View ref={ref} style={styles.container} onLayout={onLayout}>
      <Text style={styles.title}>{t("Our journey")}</Text>
      {journeyData.map((journey, index) => (
        <JourneySection
          key={index}
          journey={journey}
          isLast={index === journeyData.length - 1}
        />
      ))}
    </View>
  )
})

const JourneySection = ({
  journey,
  isLast,
}: {
  journey: Journey
  isLast: boolean
}) => (
  <View style={styles.journeySection}>
    <JourneyHeader time={journey.time} color={journey.color} />
    <View style={styles.journeyContent}>
      <JourneyLine color={journey.color} isLast={isLast} />
      <View style={styles.journeyItemsContainer}>
        {journey.items.map((item, index) => (
          <JourneyItemCard key={index} item={item} color={journey.color} />
        ))}
        <View style={{ height: isLast ? 60 : 30 }} />
      </View>
    </View>
  </View>
)

const JourneyHeader = ({ time, color }: { time: string; color: string }) => (
  <View style={styles.journeyHeader}>
    <View style={styles.journeyDotContainer}>
      <View style={[styles.journeyDot, { backgroundColor: color }]} />
    </View>
    <Text style={[styles.journeyTime, { color }]}>{time}</Text>
    <View style={[styles.journeyLineDashed, { borderColor: color }]} />
  </View>
)

const JourneyLine = ({ color, isLast }: { color: string; isLast: boolean }) => (
  <View style={styles.journeyLineContainer}>
    <LinearGradient
      colors={isLast ? [color, color, "rgba(255,255,255,0.3)"] : [color, color]}
      style={styles.journeyLine}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
    />
  </View>
)

const JourneyItemCard = ({
  item,
  color,
}: {
  item: JourneyItem
  color: string
}) => (
  <View style={[styles.journeyItemCard, { borderColor: color }]}>
    <Text style={[styles.journeyItemTitle, { color }]}>{item.title}</Text>
    <Text style={styles.journeyItemDescription}>{item.description}</Text>
  </View>
)

HomeJourney.displayName = "HomeJourney"

export default HomeJourney

const styles = StyleSheet.create({
  container: {
    width: "100%",
    padding: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    ...textStyles.titleL,
    marginVertical: 30,
  },
  journeySection: {
    width: "100%",
    marginTop: -10,
  },
  journeyHeader: {
    flexDirection: "row",
    alignItems: "flex-end",
    width: "100%",
  },
  journeyDotContainer: {
    width: 40,
    justifyContent: "flex-end",
    alignItems: "center",
    marginStart: -10,
  },
  journeyDot: {
    width: 20,
    height: 20,
    transform: [{ rotate: "45deg" }],
  },
  journeyTime: {
    fontSize: 30,
    fontWeight: "500",
    marginStart: 10,
    marginEnd: 16,
    marginBottom: -10,
  },
  journeyLineDashed: {
    flex: 1,
    opacity: 0.5,
    borderStyle: "dashed",
    borderWidth: 3,
  },
  journeyContent: {
    width: "100%",
    flexDirection: "row",
  },
  journeyLineContainer: {
    width: 40,
    alignItems: "center",
    marginStart: -10,
  },
  journeyLine: {
    width: 3,
    flexGrow: 1,
  },
  journeyItemsContainer: {
    width: "100%",
  },
  journeyItemCard: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 10,
    padding: 16,
    marginVertical: 16,
    marginEnd: 16,
  },
  journeyItemTitle: {
    ...textStyles.titleL,
  },
  journeyItemDescription: {
    textAlign: "justify",
    ...textStyles.bodyM,
  },
})
