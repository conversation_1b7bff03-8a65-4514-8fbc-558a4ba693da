import React, { forwardRef } from "react"
import { Image, LayoutChangeEvent, StyleSheet, Text, View } from "react-native"
import { GradientButton } from "src/components"
import { textStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { Router } from "src/navigation/Router"
import nftBanner from "assets/images/nft-banner.jpg"

const useTokenizationSectionData = () => {
  const { t } = useTranslation()

  const tokenizationSectionData: TokenizationSectionProps[] = [
    {
      title: t("Traditional market"),
      descriptions: [
        t("High costs"),
        t("Limited access"),
        t("Complex transactions in real estate."),
      ],
    },
    {
      title: t("Briky Land's NFT System"),
      descriptions: [
        t("“NFT Representation” Verifies ownership and authenticity."),
        t("“Fractional Investment” Enables accessible property ownership."),
        t("“Secure Transactions” Ensured by blockchain smart contracts."),
      ],
    },
    {
      title: t("User Benefits:"),
      descriptions: [
        t("Lower investment barriers"),
        t("Increased liquidity"),
        t("Efficient and reliable process"),
      ],
    },
  ]
  return tokenizationSectionData
}

interface HomeTokenizationProps {
  onLayout?: (event: LayoutChangeEvent) => void
}

interface TokenizationSectionProps {
  title: string
  descriptions: string[]
}

const TokenizationSectionView: React.FC<TokenizationSectionProps> = ({
  title,
  descriptions,
}) => {
  return (
    <View style={styles.tokenizationSection}>
      <Text style={styles.titleTokenzationSection}>{title}</Text>
      {descriptions.map((description, index) => (
        <Text key={index} style={styles.description}>
          {"\u2022"} {description}
        </Text>
      ))}
    </View>
  )
}

const HomeTokenization = forwardRef<View, HomeTokenizationProps>(
  ({ onLayout }, ref) => {
    const { t } = useTranslation()
    const tokenizationSectionData = useTokenizationSectionData()
    const navigation = useNavigation<NavigationProp<ParamListBase>>()

    return (
      <View ref={ref} style={styles.container} onLayout={onLayout}>
        <Image
          source={nftBanner}
          style={styles.backgroundImage}
          resizeMode="cover"
        />

        <View style={[styles.content]}>
          <Text style={styles.label}>{t("Tokenization")}</Text>
          {tokenizationSectionData.map((item, index) => {
            return (
              <TokenizationSectionView
                key={`${index} - ${item.title}`}
                title={item.title}
                descriptions={item.descriptions}
              />
            )
          })}
        </View>

        <GradientButton
          style={styles.button}
          title={t("Briky now").toUpperCase()}
          width={"90%"}
          height={48}
          borderRadius={24}
          onPress={() => {
            navigation.navigate(Router.MarketPlace, {
              screen: Router.CreateNFT,
            })
          }}
        />
      </View>
    )
  }
)

HomeTokenization.displayName = "HomeTokenization"

const styles = StyleSheet.create({
  container: {
    width: "100%",
    justifyContent: "flex-start",
    flexDirection: "column",
    borderRadius: 10,
    alignItems: "center",
  },
  backgroundImage: {
    position: "absolute",
    width: "100%",
    height: "100%",
    aspectRatio: 9 / 16,
  },
  label: {
    ...textStyles.wHeadlineS,
    width: "100%",
    paddingHorizontal: 12,
    marginBottom: 12,
  },
  content: {
    paddingTop: 150,
    paddingBottom: 40,
    alignItems: "center",
    width: "100%",
    zIndex: 1,
  },
  title: {
    color: Colors.white,
    marginHorizontal: 16,
    marginVertical: 30,
  },
  description: {
    ...textStyles.bodyM,
    color: Colors.white,
    marginBottom: 4,
  },
  highlight: {
    color: Colors.primary,
    marginHorizontal: 16,
    marginBottom: 30,
  },
  button: {
    marginBottom: 40,
    marginHorizontal: 8,
    zIndex: 1,
  },
  tokenizationSection: {
    paddingHorizontal: 12,
    width: "100%",
    marginTop: 18,
  },
  titleTokenzationSection: {
    ...textStyles.bodyM,
    color: Colors.primary,
  },
})

export default HomeTokenization
