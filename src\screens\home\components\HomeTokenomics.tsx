import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import <PERSON><PERSON><PERSON> from "react-native-pie-chart"
import { CardView } from "src/components"
import { Divider } from "react-native-paper"
import { textStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import { textColors } from "src/config/colors"
import logoIcon from "assets/images/ic_logo_B.png"

interface TokenData {
  amount: number
  allocation: string
  color: string
}

const HomeTokenomics: React.FC = () => {
  const { t } = useTranslation()
  const total = 20_000_000_000

  const tokenData: TokenData[] = [
    { amount: 100_000_000, allocation: t("Backer Round"), color: "#F054BB" },
    { amount: 50_000_000, allocation: t("Seed Round"), color: "#C1272D" },
    { amount: 30_000_000, allocation: t("Private Sale 1"), color: "#AB857F" },
    { amount: 50_000_000, allocation: t("Private Sale 2"), color: "#78C13F" },
    { amount: 500_000_000, allocation: t("Public Sale"), color: "#C38B1E" },
    { amount: 2_300_000_000, allocation: t("Market Maker"), color: "#7841D0" },
    { amount: 1_000_000_000, allocation: t("Core Team"), color: "#5CC4F0" },
    {
      amount: 1_000_000_000,
      allocation: t("External Treasury"),
      color: "#26A17B",
    },
    {
      amount: 15_000_000_000,
      allocation: t("Staking Rewards"),
      color: "#FEC332",
    },
  ]

  return (
    <View style={styles.center}>
      <Text style={[textStyles.titleL, styles.title]}>{t("Tokenomics")}</Text>
      <TokenPieChart tokenData={tokenData} />
      <TokenDetails total={total} tokenData={tokenData} />
    </View>
  )
}

interface TokenPieChartProps {
  tokenData: TokenData[]
}

const TokenPieChart: React.FC<TokenPieChartProps> = ({ tokenData }) => {
  return (
    <View style={styles.chartContainer}>
      <PieChart
        widthAndHeight={300}
        series={tokenData.map((token) => token.amount)}
        sliceColor={tokenData.map((token) => token.color)}
        coverFill={"#fff"}
        coverRadius={0.45}
      />
      <View style={styles.logoContainer}>
        <Image source={logoIcon} style={styles.logo} />
      </View>
    </View>
  )
}

interface TokenDetailsProps {
  tokenData: TokenData[]
  total: number
}

const TokenDetails: React.FC<TokenDetailsProps> = ({ total, tokenData }) => {
  const { t } = useTranslation()

  return (
    <CardView style={styles.card}>
      <View style={styles.cardContent}>
        <View style={styles.headerRow}>
          <Text style={styles.headerText}>{t("Percentage")}</Text>
          <Text style={styles.headerText}>{t("Allocation")}</Text>
          <Text style={[styles.headerText, styles.headerTextRight]}>
            {t("Amount")}
          </Text>
        </View>
        <Divider />
        {tokenData.map((token, index) => (
          <TokenDetailRow key={index} token={token} total={total} />
        ))}
        <Divider />
        <View style={styles.footerRow}>
          <Text style={styles.footerText}>{t("100%")}</Text>
          <Text style={styles.footerText}>{t("Total")}</Text>
          <Text style={[styles.footerText, styles.footerTextRight]}>
            {t("20B")}
          </Text>
        </View>
      </View>
    </CardView>
  )
}

interface TokenDetailRowProps {
  token: TokenData
  total: number
}

const TokenDetailRow: React.FC<TokenDetailRowProps> = ({ token, total }) => (
  <View style={styles.dataRow}>
    <View style={styles.colorBoxContainer}>
      <View style={[styles.colorBox, { backgroundColor: token.color }]} />
      <Text style={[styles.percentageText, { color: token.color }]}>
        {((token.amount / total) * 100).toFixed(2)}%
      </Text>
    </View>
    <Text style={styles.allocationText}>{token.allocation}</Text>
    <Text style={styles.amountText}>{token.amount.toLocaleString()}</Text>
  </View>
)

export default HomeTokenomics

const styles = StyleSheet.create({
  center: {
    justifyContent: "center",
    width: "100%",
    alignItems: "center",
  },
  title: {
    marginTop: 30,
  },
  chartContainer: {
    width: 300,
    height: 300,
    marginVertical: 40,
  },
  logoContainer: {
    position: "absolute",
    left: 0,
    top: 0,
    width: 300,
    height: 300,
    justifyContent: "center",
    alignItems: "center",
  },
  logo: {
    width: 50,
    height: 60,
  },
  card: {
    width: "100%",
    padding: 16,
  },
  cardContent: {
    width: "100%",
  },
  headerRow: {
    flexDirection: "row",
    paddingBottom: 8,
  },
  headerText: {
    flex: 1,
    ...textStyles.titleL,
  },
  headerTextRight: {
    textAlign: "right",
    ...textStyles.titleL,
  },
  dataRow: {
    flexDirection: "row",
    paddingVertical: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  colorBoxContainer: {
    flex: 1,
    flexDirection: "row",
  },
  colorBox: {
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  percentageText: {
    marginStart: 8,
    ...textStyles.titleL,
  },
  allocationText: {
    flex: 1,
    ...textStyles.bodyM,
    color: textColors.textBlack10,
  },
  amountText: {
    flex: 1,
    textAlign: "right",
    ...textStyles.bodyM,
    color: textColors.textBlack10,
  },
  footerRow: {
    flexDirection: "row",
    paddingTop: 8,
  },
  footerText: {
    flex: 1,
    ...textStyles.titleL,
  },
  footerTextRight: {
    textAlign: "right",
    ...textStyles.titleL,
  },
})
