import React from "react"
import {
  Image,
  ImageSourcePropType,
  LayoutChangeEvent,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from "react-native"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { CardView } from "components"
import { useTranslation } from "react-i18next"
import rocketImage from "assets/images/ic_rocket.png"
import pcImage from "assets/images/ic_pc.png"
import keyImage from "assets/images/ic_key.png"

const useIntroData = () => {
  const { t } = useTranslation()

  const introData: IntroItem[] = [
    {
      image: rocketImage,
      title: t("ERC-Driven Evolution"),
      descriptions: [
        t("Boosting digital assets with the power of “ERC-1155” and “ERC-20”"),
        t(
          "Enabling seamless trading, fractional ownership, low fees, and advanced security."
        ),
      ],
    },
    {
      image: pcImage,
      title: t("Transformed by AI and Blockchain"),
      descriptions: [
        t("Enhancing “smarter decisions” and “secure transactions”"),
        t(
          "Connecting buyers and sellers “in real time” to boost transparency and liquidity."
        ),
      ],
    },
    {
      image: keyImage,
      title: t("“BRIKI” TOKEN"),
      descriptions: [
        t(
          "Primary Token: Used for buying and selling real estate, “starting from just $100”."
        ),
        t("Multi-Use: Covers platform fees seamlessly."),
      ],
    },
  ]
  return introData
}

interface IntroItem {
  image: ImageSourcePropType
  title: string
  descriptions: string[]
}

interface HomeIntroProps {
  style?: ViewStyle
  title: string
  titleColor?: string
  descriptions: string[]
  image?: ImageSourcePropType
}

interface Props {
  onLayout?: (event: LayoutChangeEvent) => void
}

const HomeIntro: React.FC<HomeIntroProps> = ({
  style,
  title,
  titleColor = textColors.textBlack,
  descriptions,
  image,
}) => (
  <CardView style={[styles.container, style]} elevation={10}>
    {image && <Image source={image} style={styles.image} />}
    <Text style={[styles.title, { color: titleColor }]}>{title}</Text>
    {descriptions.map((description, index) => (
      <Text key={index} style={styles.description}>
        {description}
      </Text>
    ))}
  </CardView>
)

export const HomeWhatIsBrikyLand: React.FC<Props> = ({ onLayout }) => {
  const { t } = useTranslation()
  const introData = useIntroData()

  return (
    <>
      <View onLayout={onLayout}>
        <Text style={styles.mainTitle}>
          {t("What is Briky Land").toUpperCase()}
        </Text>
      </View>
      {introData.map((data, index) => (
        <HomeIntro
          key={index}
          style={{ marginTop: 16 }}
          image={data.image}
          title={data.title}
          titleColor={Colors.blueLink}
          descriptions={data.descriptions}
        />
      ))}
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    padding: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  image: {
    width: 64,
    height: 64,
  },
  title: {
    ...textStyles.titleL,
    marginVertical: 20,
    textAlign: "center",
  },
  description: {
    ...textStyles.bodyM,
    textAlign: "justify",
    width: "100%",
    alignContent: "flex-start",
    color: textColors.textBlack9,
  },
  mainTitle: {
    ...textStyles.titleL,
    marginTop: 40,
    textAlign: "center",
    color: textColors.textBlack10,
  },
})
