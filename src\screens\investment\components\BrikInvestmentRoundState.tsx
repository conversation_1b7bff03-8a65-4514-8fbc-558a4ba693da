import React from "react"
import { StyleSheet, Text, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { useTranslation } from "react-i18next"

interface BrikInvestmentRoundStateConfig {
  color: string
  label: string
  textColor: string
}

interface InvestmentRoundStateProps {
  style?: ViewStyle
  state: string
  color?: string
}

const getInvestmentRoundStateConfig = (
  state: string,
  t: (key: string) => string
): BrikInvestmentRoundStateConfig => {
  const configs: Record<string, BrikInvestmentRoundStateConfig> = {
    FINISHED: {
      color: Colors.lightPurple,
      label: t("Done"),
      textColor: textColors.textBlack11,
    },
    PROGRESSING: {
      color: Colors.lightBlue,
      label: t("On going"),
      textColor: textColors.textBlack11,
    },
    PENDING: {
      color: Colors.black5,
      label: t("Comming soon"),
      textColor: textColors.textBlack11,
    },
  }

  return (
    configs[state] || {
      color: Colors.primary,
      label: t("Unknown status"),
      textColor: textColors.textBlack11,
    }
  )
}

const BrikInvestmentRoundState: React.FC<InvestmentRoundStateProps> = ({
  state,
  color,
  style,
}) => {
  const { t } = useTranslation()
  const config = getInvestmentRoundStateConfig(state, t)

  return (
    <Text
      style={[
        style,
        styles.status,
        { backgroundColor: color || config.color, color: config.textColor },
      ]}
    >
      {config.label}
    </Text>
  )
}

const styles = StyleSheet.create({
  status: {
    ...textStyles.labelS,
    alignSelf: "flex-start",
    borderRadius: 4,
    padding: 4,
  },
})

export { BrikInvestmentRoundState }
