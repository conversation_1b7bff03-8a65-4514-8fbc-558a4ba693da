import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { GradientButton, GradientText, PrimaryButton } from "src/components"
import Colors from "src/config/colors"
import { Divider } from "react-native-paper"
import { MaterialIcons } from "@expo/vector-icons"
import { textStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { Router } from "navigation/Router"

interface InvestmentPackageProps {
  title: string
  amount: string
  benefits: string[]
  description: string
  isBestValue?: boolean
  onPress: () => void
}

const InvestmentPackage: React.FC<InvestmentPackageProps> = ({
  title,
  amount,
  benefits = [],
  description,
  isBestValue,
  onPress,
}) => {
  const { t } = useTranslation()
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      {!isBestValue ? (
        <Text style={styles.amount}>{`${amount} $`}</Text>
      ) : (
        <GradientText
          colors={["#FFC330", "#e95a2b"]}
          style={styles.amount}
          text={`${amount} $`}
        />
      )}
      <Divider style={styles.divider} />
      {benefits.map((benefit, index) => (
        <View key={index} style={styles.row}>
          <MaterialIcons name="check" size={24} color={Colors.black12} />
          <Text style={styles.description}>{benefit}</Text>
        </View>
      ))}
      <Divider style={styles.divider} />
      <Text style={styles.footerText}>{description}</Text>
      {isBestValue ? (
        <GradientButton
          title={t("Invest Now")}
          height={48}
          borderRadius={0}
          onPress={onPress}
          style={styles.button}
        />
      ) : (
        <PrimaryButton
          title={t("Invest Now")}
          height={48}
          borderRadius={0}
          onPress={onPress}
          style={styles.button}
        />
      )}
      {isBestValue && (
        <GradientButton
          width={100}
          borderRadius={0}
          height={30}
          style={styles.bestValueBanner}
          title={t("Best Deal")}
          onPress={() => {}}
        />
      )}
    </View>
  )
}

const InvestmentPackages: React.FC = () => {
  const { t } = useTranslation()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const InvestmentData = [
    {
      title: t("Collaborator Package"),
      amount: "100",
      benefits: [t("Become our Collaborator and get return commission")],
      description: t(
        "Receive BRIK based on the market price at the time of participation"
      ),
    },
    {
      title: t("Broker Package"),
      amount: "1,000",
      benefits: [
        t("Become a Professional Real Estate Broker"),
        t(
          "Attain training and certification as a Professional Real Estate Broker"
        ),
        t(
          "Earn permanent affiliate commissions when NATIVE LANDs (Real Estate NFTs) are traded on the platform"
        ),
      ],
      description: t(
        "Receive BRIK based on the market price at the time of participation"
      ),
    },
    {
      title: t("Partner Package"),
      amount: "10,000",
      benefits: [
        t("Become a Professional Real Estate Investor"),
        t(
          "Attain training and certification as a Professional Real Estate Broker"
        ),
        t("Earn bonuses based on revenue"),
        t(
          "Earn permanent affiliate commissions when NATIVE LANDs (Real Estate NFTs) are traded on the platform"
        ),
        t("Own your Local Real Estate Service Center"),
      ],
      description: t(
        "Receive BRIK based on the market price at the time of participation"
      ),
      isBestValue: true,
    },
  ]
  return (
    <View style={styles.packagesContainer}>
      {InvestmentData.map((data, index) => (
        <InvestmentPackage
          key={index}
          title={data.title}
          amount={data.amount}
          benefits={data.benefits}
          description={data.description}
          isBestValue={data.isBestValue}
          onPress={() => navigation.navigate(Router.Auction)}
        />
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  packagesContainer: {
    alignSelf: "stretch",
    marginHorizontal: 16,
  },
  container: {
    alignSelf: "stretch",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.primary,
    marginTop: 30,
  },
  title: {
    marginHorizontal: 16,
    marginTop: 20,
    ...textStyles.titleL,
  },
  amount: {
    marginHorizontal: 16,
    marginTop: 8,
    fontSize: 30,
    fontWeight: "500",
    fontFamily: "Allerta",
  },
  divider: {
    height: 0.5,
    marginVertical: 24,
    marginHorizontal: 16,
  },
  row: {
    flexDirection: "row",
    marginHorizontal: 16,
  },
  description: {
    marginStart: 8,
    ...textStyles.bodyM,
  },
  footerText: {
    fontWeight: "500",
    marginHorizontal: 16,
    textAlign: "center",
    marginBottom: 24,
  },
  button: {
    marginBottom: -1,
    width: "100%",
    borderBottomLeftRadius: 11,
    borderBottomRightRadius: 11,
  },
  bestValueBanner: {
    position: "absolute",
    right: -1,
    top: -1,
    borderTopRightRadius: 11,
    borderBottomLeftRadius: 12,
  },
})

export { InvestmentPackages }
