import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { InvestmentPlansModel } from "../model"
import { textStyles } from "src/config/styles"
import { convertHexToRGBA } from "utils"
import Colors from "src/config/colors"
import { Divider } from "react-native-paper"
import { useTranslation } from "react-i18next"

const colors = ["#FEC332", "#78C1C1", "#BF93B8"]

const InvestmentDevelopmentPlan: React.FC = () => {
  const { t } = useTranslation()

  const investmentData: InvestmentPlansModel[] = [
    {
      title: t("Real estate Department"),
      plans: [
        {
          time: "Q3/2024 - Q3/2025",
          value: "100",
        },
        {
          time: "Q3/2025 - Q3/2026",
          value: "1000",
        },
        {
          time: "Q3/2026 - Q3/2027",
          value: "10.000",
        },
      ],
    },
    {
      title: t("Real estate Saler"),
      plans: [
        {
          time: "Q3/2024 - Q3/2025",
          value: "1000",
        },
        {
          time: "Q3/2025 - Q3/2026",
          value: "10.000",
        },
        {
          time: "Q3/2026 - Q3/2027",
          value: "100.000",
        },
      ],
    },
    {
      title: t("Real estate partner"),
      plans: [
        {
          time: "Q3/2024 - Q3/2025",
          value: "5000",
        },
        {
          time: "Q3/2025 - Q3/2026",
          value: "10.000",
        },
        {
          time: "Q3/2026 - Q3/2027",
          value: "500.000",
        },
      ],
    },
  ]

  return (
    <View style={styles.container}>
      <Text style={textStyles.titleL}>{t("Members Development Plan")}</Text>
      {investmentData.map((investment, index) => (
        <InvestmentPlanItem
          key={index}
          plan={investment}
          color={colors[index]}
        />
      ))}
    </View>
  )
}

interface InvestmentPlanItemProps {
  plan: InvestmentPlansModel
  color: string
}

const InvestmentPlanItem: React.FC<InvestmentPlanItemProps> = ({
  plan,
  color,
}) => {
  return (
    <View style={styles.planContainer}>
      {plan.plans.map((item, index) => (
        <View style={styles.planItem} key={index}>
          <Text style={styles.planTime}>{item.time}</Text>
          <Text style={styles.planValue}>{item.value}</Text>
          <Divider style={styles.divider} />
        </View>
      ))}
      <Text
        style={[
          styles.planTitle,
          {
            borderColor: color,
            backgroundColor: convertHexToRGBA(color, 0.5),
          },
        ]}
      >
        {plan.title}
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    alignSelf: "stretch",
    alignItems: "center",
  },
  planContainer: {
    borderWidth: 0.5,
    borderColor: Colors.black12,
    borderRadius: 12,
    alignSelf: "stretch",
    marginHorizontal: 16,
    marginTop: 16,
    paddingVertical: 12,
  },
  planItem: {
    marginBottom: 12,
    alignItems: "center",
  },
  planTime: {
    textDecorationLine: "underline",
    ...textStyles.body1,
  },
  planValue: {
    ...textStyles.titleL,
  },
  divider: {
    width: "100%",
    marginTop: 12,
  },
  planTitle: {
    borderWidth: 0.5,
    borderRadius: 12,
    textAlign: "center",
    paddingVertical: 8,
    minWidth: 150,
    paddingHorizontal: 16,
    alignSelf: "center",
    ...textStyles.titleL,
  },
})

export { InvestmentDevelopmentPlan }
