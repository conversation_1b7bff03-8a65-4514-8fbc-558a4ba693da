import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import brikIcon from "assets/images/ic_brik.png"
import flagPrimaryIcon from "assets/images/ic_flag_primary.png"
import lockIcon from "assets/images/ic_lock.png"
import { CustomPressable } from "components"
import { useSetAtom } from "jotai"
import React from "react"
import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  View,
} from "react-native"
import { InvestmentRound, InvestmentRoundState } from "src/api/types"
import Colors, { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { formatNumberToReadableFormat } from "utils"
import { selectedRoundIdAtom } from "../atom/InvestmentAtom"
import { BrikInvestmentRoundState } from "./BrikInvestmentRoundState"

interface InvestmentRoundItemViewProps {
  investmentRound: InvestmentRound
}

const InvestmentRoundItemView: React.FC<InvestmentRoundItemViewProps> = ({
  investmentRound,
}) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const setSelectedRoundId = useSetAtom(selectedRoundIdAtom)

  const getRoundStateVisualElements = (
    state: string
  ): { icon: ImageSourcePropType | null; background: string } => {
    switch (state) {
      case InvestmentRoundState.PROGRESSING:
        return { icon: flagPrimaryIcon, background: Colors.white }
      case InvestmentRoundState.PENDING:
        return { icon: lockIcon, background: Colors.black3 }
      default:
        return { icon: null, background: Colors.white }
    }
  }

  const roundStateVisualElements = getRoundStateVisualElements(
    investmentRound.state
  )

  const onGoBack = () => {
    navigation.goBack()
  }

  const onSelectRound = () => {
    setSelectedRoundId(investmentRound.round)
    onGoBack()
  }

  return (
    <CustomPressable
      enabled={investmentRound.state !== InvestmentRoundState.PENDING}
      onPress={onSelectRound}
      style={[
        styles.item,
        { backgroundColor: roundStateVisualElements.background },
      ]}
    >
      <View style={styles.row}>
        {roundStateVisualElements.icon && (
          <Image
            source={roundStateVisualElements.icon}
            style={viewStyles.icon}
          />
        )}
        <Text style={styles.name}>{investmentRound.round}</Text>
        <BrikInvestmentRoundState state={investmentRound.state} />
      </View>
      <View style={styles.row}>
        <Text style={styles.amount}>
          {formatNumberToReadableFormat(investmentRound.tokenAllocation)}
        </Text>
        <Image source={brikIcon} style={viewStyles.tinyIcon} />
      </View>
    </CustomPressable>
  )
}

const styles = StyleSheet.create({
  item: {
    borderWidth: 1,
    borderColor: Colors.black5,
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    flex: 1,
    marginVertical: 4,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  name: {
    ...textStyles.labelL,
    color: textColors.textBlack,
    marginHorizontal: 8,
  },
  amount: {
    ...textStyles.bodyM,
    color: textColors.textBlack,
    marginEnd: 4,
  },
})

export { InvestmentRoundItemView }
