import React from "react"
import { Image, ImageRequireSource, StyleSheet, Text, View } from "react-native"
import { CardView } from "components"
import Colors from "src/config/colors"
import { formatToPercent, shortenNumber } from "utils"
import { textStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import brikIcon from "assets/images/ic_brik.png"
import usdtIcon from "assets/images/ic_usdt.png"

const InvestmentTokenDistribution: React.FC = () => {
  const { t } = useTranslation()

  const distributionRoundData = [
    {
      name: t("Backer Round"),
      percentage: 1.0,
      brikValue: 100_000_000,
      estimatedValue: 100_000_000 * 0.003,
      estimatedPrice: 0.003,
    },
    {
      name: t("Seed Round"),
      percentage: 0.25,
      brikValue: 50_000_000,
      estimatedValue: 50_000_000 * 0.03,
      estimatedPrice: 0.03,
    },
    {
      name: t("Private Sale 1"),
      percentage: 0.75,
      brikValue: 30_000_000,
      estimatedValue: 30_000_000 * 0.09,
      estimatedPrice: 0.09,
    },
    {
      name: t("Pre IPO"),
      percentage: 0.25,
      brikValue: 50_000_000,
      estimatedValue: 50_000_000 * 0.27,
      estimatedPrice: 0.27,
    },
    {
      name: t("IPO"),
      percentage: 2.5,
      brikValue: 500_000_000,
      estimatedValue: 500_000_000 * 0.54,
      estimatedPrice: 0.54,
    },
  ]

  return (
    <View style={styles.container}>
      <Text style={styles.header}> {t("Token distribution")}</Text>
      {distributionRoundData.map((data, index) => (
        <DistributionRoundItem
          key={index}
          name={`${index + 1}. ${data.name}`}
          percentage={data.percentage}
          brikValue={data.brikValue}
          estimatedValue={data.estimatedValue}
          estimatedPrice={data.estimatedPrice}
        />
      ))}
    </View>
  )
}

interface DistributionRoundItemProps {
  name: string
  percentage: number
  brikValue: number
  estimatedValue: number
  estimatedPrice: number
}

const DistributionRoundItem: React.FC<DistributionRoundItemProps> = ({
  name,
  percentage,
  brikValue,
  estimatedValue,
  estimatedPrice,
}) => {
  return (
    <CardView style={styles.card}>
      <View>
        <View style={styles.row}>
          <Text style={styles.roundName}>{name}</Text>
          <Text style={styles.percentage}>{formatToPercent(percentage)}</Text>
        </View>
        <DistributionValueItem
          brikValue={brikValue}
          estimatedValue={estimatedValue}
          estimatedPrice={estimatedPrice}
        />
      </View>
    </CardView>
  )
}

interface DistributionValueItemProps {
  brikValue: number
  estimatedValue: number
  estimatedPrice: number
}

const DistributionValueItem: React.FC<DistributionValueItemProps> = ({
  brikValue,
  estimatedValue,
  estimatedPrice,
}) => {
  const { t } = useTranslation()
  return (
    <View style={styles.valueContainer}>
      <ValueBox
        label={t("BRIK distribution")}
        value={shortenNumber(brikValue)}
        imageSource={brikIcon}
        backgroundColor="#f5f5f5"
      />
      <View style={{ width: 8 }} />
      <ValueBox
        label={t("Estimated value")}
        value={shortenNumber(estimatedValue)}
        imageSource={usdtIcon}
        backgroundColor="#FFF6E0"
      />
      <View style={{ width: 8 }} />
      <ValueBox
        label={t("Estimated price")}
        value={shortenNumber(estimatedPrice)}
        imageSource={usdtIcon}
        backgroundColor="#DEF1EB"
      />
    </View>
  )
}

interface ValueBoxProps {
  label: string
  value: string
  imageSource: ImageRequireSource
  backgroundColor: string
}

const ValueBox: React.FC<ValueBoxProps> = ({
  label,
  value,
  imageSource,
  backgroundColor,
}) => {
  return (
    <View style={[styles.valueBox, { backgroundColor }]}>
      <Text style={styles.valueLabel}>{label}</Text>
      <View style={styles.valueRow}>
        <Text style={styles.valueText}>{value}</Text>
        <Image source={imageSource} style={styles.valueImage} />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  header: {
    marginBottom: 40,
    alignSelf: "center",
    ...textStyles.titleL,
  },
  card: {
    marginTop: 30,
    marginHorizontal: 16,
    padding: 8,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  roundName: {
    ...textStyles.titleL,
    marginTop: 6,
  },
  percentage: {
    textAlign: "center",
    borderRadius: 6,
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    width: 80,
    ...textStyles.titleL,
  },
  valueContainer: {
    flexDirection: "row",
    marginTop: 20,
  },
  valueBox: {
    flex: 1,
    alignItems: "flex-start",
    padding: 8,
    borderRadius: 6,
  },
  valueLabel: {
    textAlign: "center",
    ...textStyles.bodyM,
    color: Colors.black7,
  },
  valueRow: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
    ...textStyles.bodyM,
  },
  valueText: {
    ...textStyles.titleL,
  },
  valueImage: {
    width: 16,
    height: 16,
    marginStart: 4,
  },
})

export default InvestmentTokenDistribution
