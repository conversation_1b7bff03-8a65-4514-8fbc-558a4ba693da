import React, { useContext } from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { CollapseWithHeaderView } from "screens/landdetail/component"
import { InvestorFullItemView, InvestorShortItemView } from "./index"
import { useTranslation } from "react-i18next"
import { Divider } from "react-native-paper"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import icBrik from "assets/images/ic_brik.png"
import InvestmentContext from "../context/InvestmentContext"
import { useAtom } from "jotai"
import { selectedRoundIdAtom } from "../atom/InvestmentAtom"
import {
  Investment,
  InvestmentRoundState,
  InvestmentRoundType,
} from "src/api/types"

interface InvestmentsViewProps {
  investments: Investment[]
}

const InvestmentsFullView: React.FC<InvestmentsViewProps> = ({
  investments,
}) => {
  return (
    <View style={styles.investors}>
      {investments.map((item, index) => (
        <InvestorFullItemView investor={item} key={index} />
      ))}
    </View>
  )
}

const InvestmentsShortView: React.FC<InvestmentsViewProps> = ({
  investments,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.investors}>
      <View style={styles.label}>
        <Text style={[styles.title, styles.address]}>
          {t("Wallet Address")}
        </Text>
        <Text style={[styles.title, styles.value]}>
          {t("Brik").toUpperCase()}
        </Text>
      </View>
      {investments.map((item, index) => (
        <InvestorShortItemView investor={item} key={index} />
      ))}
    </View>
  )
}

interface MyInvestsViewProps {
  selectedRoundName: string
}

const InvestmentsView: React.FC<MyInvestsViewProps> = ({
  selectedRoundName,
}) => {
  const { t } = useTranslation()
  const [selectedRoundId] = useAtom(selectedRoundIdAtom)
  const { investmentRounds = [] } = useContext(InvestmentContext)
  const selectedRound = investmentRounds.find(
    (investmentRound) =>
      investmentRound.round === selectedRoundId ||
      investmentRound.state === InvestmentRoundState.PROGRESSING
  )
  const investments = selectedRound?.investments || []
  const isShowFullView = selectedRound?.round === InvestmentRoundType.SEED

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.investmentLabel}>{`${t("Your investment")}:`}</Text>
        <View style={styles.row}>
          {/* TODO: hardcode */}
          <Text style={styles.totalAmount}>240</Text>
          <Image source={icBrik} style={viewStyles.tinyIcon} />
        </View>
      </View>
      <Divider style={styles.divider} />
      <View style={styles.listContainer}>
        <CollapseWithHeaderView
          headerStyle={styles.rowSpaceBetween}
          title={t("Round investors list", { round: selectedRoundName || "" })}
          emptyTitle={t("No investors")}
          showEmpty={investments.length === 0}
          style={{ marginBottom: 4 }}
        >
          {isShowFullView ? (
            <InvestmentsFullView investments={investments} />
          ) : (
            <InvestmentsShortView investments={investments} />
          )}
        </CollapseWithHeaderView>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.black5,
    padding: 12,
    marginVertical: 12,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 4,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  investors: {
    width: "100%",
    paddingHorizontal: 4,
  },
  investmentLabel: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
  },
  totalAmount: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
    marginEnd: 8,
  },
  listContainer: {
    marginTop: 8,
    flex: 1,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  divider: {
    backgroundColor: Colors.black5,
    height: 1,
    marginTop: 12,
    marginHorizontal: 4,
  },
  label: {
    flexDirection: "row",
    marginTop: 12,
  },
  title: {
    ...textStyles.labelM,
    color: Colors.black7,
    alignItems: "flex-start",
  },
  address: {
    flex: 3,
    alignItems: "flex-start",
  },
  value: {
    flex: 2,
  },
})

export { InvestmentsView }
