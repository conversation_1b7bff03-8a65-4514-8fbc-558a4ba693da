import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { Investment } from "src/api/types"
import { AddressView } from "components"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { formatCurrency } from "utils"
import { useTranslation } from "react-i18next"

type ItemViewProps = {
  label: string
  value: string
}

const ItemView: React.FC<ItemViewProps> = ({ label, value }) => {
  return (
    <View style={styles.rowSpacebetween}>
      <Text style={styles.label}>{label}</Text>
      <Text style={styles.value}>{value}</Text>
    </View>
  )
}

interface InvestorFullItemViewProps {
  investor: Investment
}

const InvestorFullItemView: React.FC<InvestorFullItemViewProps> = ({
  investor,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.item}>
      <ItemView label={t("Office")} value={investor.officeName} />
      <ItemView label={t("Representative")} value={investor.investorName} />
      <View style={styles.rowSpacebetween}>
        <Text style={styles.label}>{t("Wallet Address")}</Text>
        <AddressView address={investor.address} copy={false} />
      </View>
      <ItemView
        label={t("BRIK amount")}
        value={formatCurrency(investor.amount)}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  item: {
    borderWidth: 1,
    borderColor: Colors.black5,
    borderRadius: 8,
    padding: 16,
    alignItems: "center",
    flex: 1,
    marginVertical: 4,
  },
  rowSpacebetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginVertical: 2,
    alignItems: "center",
  },
  name: {
    ...textStyles.labelL,
    color: textColors.textBlack,
    marginHorizontal: 8,
  },
  amount: {
    ...textStyles.bodyM,
    color: textColors.textBlack,
  },
  label: {
    ...textStyles.labelL,
    color: Colors.black7,
    flex: 1,
  },
  value: {
    ...textStyles.bodyM,
    color: textColors.textBlack11,
    flex: 2,
    textAlign: "right",
  },
})

export { InvestorFullItemView }
