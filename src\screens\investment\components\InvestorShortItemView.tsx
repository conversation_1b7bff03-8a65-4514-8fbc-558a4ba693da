import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { Investment } from "src/api/types"
import { AddressView } from "components"
import { formatCurrency } from "utils"
import { textStyles } from "src/config/styles"
import { textColors } from "src/config/colors"

interface InvestorShortItemViewProps {
  investor: Investment
}

const InvestorShortItemView: React.FC<InvestorShortItemViewProps> = ({
  investor,
}) => {
  return (
    <View style={styles.container}>
      <AddressView
        address={investor.address}
        copy={false}
        style={styles.address}
      />
      <Text style={styles.amount}>{formatCurrency(investor.amount)}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    flexDirection: "row",
  },
  address: {
    flex: 3,
    alignItems: "flex-start",
  },
  amount: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
    flex: 2,
    alignItems: "flex-start",
  },
})

export { InvestorShortItemView }
