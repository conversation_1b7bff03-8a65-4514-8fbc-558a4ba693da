import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import icClock from "assets/images/ic_clock.png"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import icBrik from "assets/images/ic_brik.png"
import { formatCurrency } from "utils"
import { CustomCountDownTimer, PrimaryButton } from "components"
import { TIME_TYPE } from "../../../components/common/CustomCountDownTimer"
import { LAST_SECOND_TIME_STAMP_2025 } from "src/utils/timeExt"

interface AmountItemViewProps {
  label: string
  amount: string
}

const AmountItemView: React.FC<AmountItemViewProps> = ({ label, amount }) => {
  return (
    <View style={styles.rowSpaceBetween}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.row}>
        <Text style={styles.amount}>{formatCurrency(amount)}</Text>
        <Image source={icBrik} style={viewStyles.tinyIcon} />
      </View>
    </View>
  )
}

const SlowWithdrawView: React.FC = () => {
  const { t } = useTranslation()
  const currentTimestampInSeconds = Math.floor(Date.now() / 1000)
  const countDownTime = LAST_SECOND_TIME_STAMP_2025 - currentTimestampInSeconds

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <Image source={icClock} style={viewStyles.icon} />
        <Text style={styles.title}>{`${t("Slow withdraw")}:`}</Text>
      </View>
      <View style={styles.investment}>
        <Text
          style={styles.slowInvestmentTitle}
        >{`${t("Your investment")}:`}</Text>
        {/* TODO fake amount data */}
        <AmountItemView label={t("Total")} amount="50000" />
        <AmountItemView label={t("BRIK unlock")} amount="30000" />
        <AmountItemView label={t("Has withdrawn")} amount="10000" />
        <AmountItemView label={t("Withdrawable amount")} amount="20000" />
        <View style={styles.countdownRow}>
          <Text style={styles.label}>{t("Remaining opening time")}</Text>
          {countDownTime > 0 && (
            <CustomCountDownTimer
              style={styles.countdown}
              duration={countDownTime}
              type={TIME_TYPE.SHORT}
            />
          )}
        </View>
        <PrimaryButton
          title={t("Withdraw")}
          onPress={() => {
            // TODO: wait contract doc
          }}
          borderRadius={8}
          width={"100%"}
          style={styles.button}
          contentColor={textColors.textBlack}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 12,
    flex: 1,
  },
  title: {
    ...textStyles.titleM,
    color: textColors.textBlack11,
    marginStart: 8,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  rowSpaceBetween: {
    justifyContent: "space-between",
    flexDirection: "row",
    marginBottom: 8,
    alignItems: "center",
  },
  slowInvestmentTitle: {
    ...textStyles.titleS,
    color: textColors.textBlack11,
    marginVertical: 4,
  },
  investment: {
    marginTop: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.black5,
    padding: 12,
    backgroundColor: Colors.white,
  },
  countdownRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  countdown: {
    flex: 1,
    paddingStart: 12,
    justifyContent: "center",
    alignItems: "flex-end",
    borderRadius: 8,
  },
  label: {
    ...textStyles.labelL,
    color: Colors.black7,
  },
  amount: {
    ...textStyles.bodyM,
    color: textColors.textBlack11,
    marginEnd: 8,
  },
  priceIcon: {
    position: "absolute",
    padding: 4,
    right: 8,
    top: 8,
    bottom: 8,
    width: 16,
    height: 16,
  },
  slowWithdraw: {
    padding: 12,
    marginTop: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.black5,
  },
  brikPriceIcon: {
    ...viewStyles.tinyIcon,
    position: "absolute",
    right: 8,
    top: "50%",
    transform: [{ translateY: -8 }],
  },
  percent: {
    position: "absolute",
    right: 8,
    textAlignVertical: "center",
    height: "100%",
  },
  button: {
    marginTop: 8,
  },
  swap: {
    ...viewStyles.icon,
    marginHorizontal: 4,
  },
})

export { SlowWithdrawView }
