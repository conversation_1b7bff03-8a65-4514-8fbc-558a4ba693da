import { Oracle } from "src/api/types"
import { useQueryWithErrorHandling } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import { getOracles } from "src/api"

export const useGetOracles = () => {
  const {
    data: oracle,
    isLoading,
    refetch,
  } = useQueryWithErrorHandling<Oracle>({
    queryKey: [...QueryKeys.ORACLE.LIST],
    queryFn: () => getOracles(),
    refetchOnMount: true,
  })

  return {
    oracle,
    isLoading: isLoading,
    refresh: refetch,
  }
}
