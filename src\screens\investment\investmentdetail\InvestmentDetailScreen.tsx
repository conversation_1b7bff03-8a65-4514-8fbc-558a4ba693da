import React, { useEffect } from "react"
import { InvestmentDetailView } from "./InvestmentDetailView"
import { useGetOracles } from "../hooks"
import InvestmentContext from "../context/InvestmentContext"
import { useAtom, useSetAtom } from "jotai"
import { selectedRoundIdAtom } from "../atom/InvestmentAtom"

const InvestmentDetailScreen: React.FC = () => {
  const { oracle, isLoading, refresh } = useGetOracles()

  const [selectedRoundId] = useAtom(selectedRoundIdAtom)
  const setSelectedRoundId = useSetAtom(selectedRoundIdAtom)

  const investmentRounds = oracle?.investment.rounds || []

  useEffect(() => {
    if (!selectedRoundId && investmentRounds.length > 0) {
      setSelectedRoundId(investmentRounds[0].round)
    }
  }, [selectedRoundId, investmentRounds, setSelectedRoundId])

  return (
    <InvestmentContext.Provider
      value={{
        investmentRounds,
        onRefresh: refresh,
        isLoading: isLoading,
      }}
    >
      <InvestmentDetailView />
    </InvestmentContext.Provider>
  )
}

export default InvestmentDetailScreen
