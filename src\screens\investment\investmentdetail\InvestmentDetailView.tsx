import {
  Background,
  CustomPressable,
  LoadingView,
  OverviewBrik,
} from "src/components"
import React, { useContext } from "react"
import { Image, ScrollView, StyleSheet, Text, View } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import { SlowWithdrawView } from "../components"
import { InvestmentsView } from "../components/InvestmentsView"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { Router } from "navigation/Router"
import { BrikInvestmentRoundState } from "../components/BrikInvestmentRoundState"
import { useTranslation } from "react-i18next"
import Colors, { textColors } from "src/config/colors"
import icChevronRight from "assets/images/ic_chevron_right.png"
import icNext from "assets/images/ic_next.png"
import { useAtom } from "jotai"
import { selectedRoundIdAtom } from "../atom/InvestmentAtom"
import { InvestmentRound } from "src/api/types"
import InvestmentContext from "../context/InvestmentContext"

const InvestmentHeader: React.FC<{
  selectedRound: InvestmentRound | undefined
}> = ({ selectedRound }) => {
  const { t } = useTranslation()
  return (
    <View style={styles.header}>
      <Text
        style={styles.title}
      >{`${t("Investment").toUpperCase()} - ${selectedRound?.round.toUpperCase()}`}</Text>
      <BrikInvestmentRoundState
        state={selectedRound?.state || ""}
        style={{ height: "100%" }}
      />
    </View>
  )
}

const TokenomicAllocationView: React.FC = () => {
  const { t } = useTranslation()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const navigateToInvestmentRounds = () => {
    navigation.navigate(Router.TokenomicsAllocation)
  }

  return (
    <CustomPressable onPress={navigateToInvestmentRounds}>
      <View style={styles.allocation}>
        <Image source={icChevronRight} style={viewStyles.icon} />
        <Text style={styles.allocationText}>{t("Tokenomics allocation")}</Text>
        <View style={{ flex: 1, alignItems: "flex-end" }}>
          <Image source={icNext} style={viewStyles.icon} />
        </View>
      </View>
    </CustomPressable>
  )
}

export const InvestmentDetailView: React.FC = () => {
  const [selectedRoundId] = useAtom(selectedRoundIdAtom)
  const { investmentRounds = [], isLoading } = useContext(InvestmentContext)
  const selectedRound = investmentRounds.find(
    (investmentRound) => investmentRound.round === selectedRoundId
  )
  return (
    <Background>
      <View style={styles.container}>
        {isLoading ? (
          <LoadingView />
        ) : (
          <>
            <InvestmentHeader selectedRound={selectedRound} />
            <ScrollView>
              <View style={styles.content}>
                <TokenomicAllocationView />
                <OverviewBrik
                  totalAmount={selectedRound?.tokenAllocation || 0}
                  brikSold={selectedRound?.sold || 0}
                  brikUnlock={selectedRound?.unlocked || 0}
                />
                <InvestmentsView
                  selectedRoundName={selectedRound?.round || ""}
                />
                <SlowWithdrawView />
              </View>
            </ScrollView>
          </>
        )}
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
  },
  content: {
    flex: 1,
    paddingVertical: 16,
  },
  header: {
    paddingTop: 12,
    flexDirection: "row",
    alignItems: "center",
    alignContent: "center",
  },
  allocation: {
    padding: 16,
    flexDirection: "row",
    borderRadius: 8,
    backgroundColor: Colors.black2,
    marginHorizontal: 4,
    alignItems: "center",
    marginVertical: 12,
  },
  allocationText: {
    ...textStyles.titleS,
    color: textColors.textBlack11,
    marginStart: 8,
  },
  overviewContainer: {
    marginTop: 20,
  },
  overviewTitle: {
    ...textStyles.titleM,
    color: textColors.textBlack11,
    marginBottom: 16,
  },
  amountContainer: {
    backgroundColor: Colors.black3,
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  amountLabel: {
    ...textStyles.bodyM,
    color: Colors.gray,
    marginBottom: 4,
  },
  amountValue: {
    ...textStyles.titleM,
    color: textColors.textBlack,
  },
  brikContainer: {
    flexDirection: "row",
    gap: 12,
  },
  brikBox: {
    flex: 1,
    backgroundColor: Colors.black3,
    padding: 16,
    borderRadius: 8,
  },
  brikLabel: {
    ...textStyles.bodyM,
    color: Colors.gray,
    marginBottom: 4,
  },
  brikValue: {
    ...textStyles.titleM,
    color: textColors.textBlack,
  },
  row: {
    flexDirection: "row",
  },
  title: {
    ...textStyles.titleL,
    color: textColors.textBlack,
    marginEnd: 8,
  },
  description: {
    textAlign: "center",
    marginHorizontal: 8,
    marginVertical: 30,
    ...textStyles.body1,
  },
})
