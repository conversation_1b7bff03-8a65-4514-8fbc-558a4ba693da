import React from "react"
import TokenomicsAllocationView from "./TokenomicsAllocationView"
import InvestmentContext from "../context/InvestmentContext"
import { useGetOracles } from "../hooks"

const TokenomicsAllocationScreen: React.FC = () => {
  const { oracle, isLoading, refresh } = useGetOracles()

  const investmentRounds = oracle?.investment.rounds || []

  return (
    <InvestmentContext.Provider
      value={{
        investmentRounds,
        onRefresh: refresh,
        isLoading: isLoading,
      }}
    >
      <TokenomicsAllocationView />
    </InvestmentContext.Provider>
  )
}

export default TokenomicsAllocationScreen
