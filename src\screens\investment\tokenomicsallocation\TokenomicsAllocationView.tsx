import React, { useCallback, useContext } from "react"
import { EmptyView, LoadingView, TopBar } from "components"
import { FlatList, StyleSheet, View } from "react-native"
import { InvestmentRoundItemView } from "../components"
import { useTranslation } from "react-i18next"
import { InvestmentRound } from "src/api/types"
import InvestmentContext from "../context/InvestmentContext"
import Colors from "src/config/colors"

const TokenomicsAllocationView: React.FC = () => {
  const { t } = useTranslation()
  const { investmentRounds = [], isLoading } = useContext(InvestmentContext)

  return (
    <View style={styles.container}>
      <TopBar title={t("Tokenomics allocation")} enableBack={true} />
      {isLoading ? (
        <LoadingView />
      ) : investmentRounds.length === 0 ? (
        <EmptyView />
      ) : (
        <ListInvestmentRoundView investmentRounds={investmentRounds} />
      )}
    </View>
  )
}

const ListInvestmentRoundView: React.FC<{
  investmentRounds: InvestmentRound[]
}> = ({ investmentRounds }) => {
  const keyExtractor = useCallback((item: InvestmentRound) => item.round, [])

  return (
    <FlatList
      style={styles.flatList}
      data={investmentRounds}
      keyExtractor={keyExtractor}
      renderItem={({ item }) => (
        <InvestmentRoundItemView investmentRound={item} />
      )}
    />
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    flex: 1,
    padding: 16,
  },
  flatList: {
    width: "100%",
  },
})

export default TokenomicsAllocationView
