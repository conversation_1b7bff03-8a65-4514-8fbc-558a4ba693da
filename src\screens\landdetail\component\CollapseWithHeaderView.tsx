import React, { useState } from "react"
import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from "react-native"
import { SectionHeader } from "./SectionHeader"
import { Divider } from "react-native-paper"
import { textStyles, viewStyles } from "src/config/styles"
import clockImage from "assets/images/img_clock.png"
import Colors from "src/config/colors"

interface CollapseWithHeaderViewProps {
  title: string
  showEmpty: boolean
  headerStyle?: ViewStyle
  headerIconUri?: ImageSourcePropType
  children: React.ReactNode
  emptyTitle: string
  style?: ViewStyle
}

const CollapseWithHeaderView: React.FC<CollapseWithHeaderViewProps> = ({
  style = styles.container,
  headerStyle,
  title,
  showEmpty,
  emptyTitle,
  children,
  headerIconUri,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(true)
  return (
    <View style={style}>
      <View style={{ alignItems: "center" }}>
        <SectionHeader
          icon={
            headerIconUri && (
              <Image source={headerIconUri} style={viewStyles.icon} />
            )
          }
          title={title}
          style={headerStyle}
          isOpen={!isCollapsed}
          onPress={() => setIsCollapsed(!isCollapsed)}
        />
        {!isCollapsed && <Divider />}
        {!isCollapsed &&
          (showEmpty ? <EmptyView emptyTitle={emptyTitle} /> : children)}
      </View>
    </View>
  )
}

const EmptyView: React.FC<{ emptyTitle: string }> = ({ emptyTitle }) => {
  return (
    <View style={{ alignItems: "center", paddingVertical: 50 }}>
      <Image source={clockImage} style={{ width: 150, height: 150 }} />
      <Text style={[textStyles.titleL, { marginTop: 16 }]}>{emptyTitle}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    marginTop: 16,
    borderWidth: 1,
    borderColor: Colors.black5,
  },
})

export { CollapseWithHeaderView }
