import { useQuery } from "@tanstack/react-query"
import { CurrencySymbol } from "components/estates/CurrencySymbol"
import React from "react"
import { useTranslation } from "react-i18next"
import { StyleSheet, Text, View } from "react-native"
import { formatAddress } from "utils/addressExt"
import {
  getDepositListByRequestId,
  TokenizationDeposit,
  TokenizationRequest,
} from "src/api"
import { shortenAddress } from "utils"
import { formatNumericByDecimalsDisplay } from "utils/format"
import { CollapseWithHeaderView } from "./CollapseWithHeaderView"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import QueryKeys from "src/config/queryKeys"
import menuIcon from "assets/images/ic_menu.png"

interface DepositListViewProps {
  request: TokenizationRequest
}

const cmpDeposits = (a: TokenizationDeposit, b: TokenizationDeposit) =>
  parseInt(b.amount) - parseInt(a.amount)

const DepositListLabelsView: React.FC = () => {
  const { t } = useTranslation()

  return (
    <View style={styles.label}>
      <Text style={[styles.title, styles.holder]}>{t("From")}</Text>
      <Text style={[styles.title, styles.hash]}>{t("Transaction Hash")}</Text>
      <Text style={[styles.title, styles.value]}>{t("Value")}</Text>
    </View>
  )
}

export const DepositListView: React.FC<DepositListViewProps> = ({
  request,
}) => {
  const { t } = useTranslation()

  const { id, decimals, currency } = request
  const { data } = useQuery({
    queryKey: QueryKeys.ESTATE.DEPOSITS(id),
    queryFn: () => getDepositListByRequestId(`${id}`),
  })

  const deposits = data?.list || []
  return (
    <CollapseWithHeaderView
      title={t("Deposits")}
      headerIconUri={menuIcon}
      showEmpty={!deposits || deposits.length == 0}
      emptyTitle={t("No deposits")}
    >
      <View>
        <DepositListLabelsView />
        {deposits.sort(cmpDeposits).map((deposit, index) => {
          const { transaction, depositor, amount, value } = deposit
          return (
            <View key={index} style={styles.container}>
              <Text style={styles.addressText}>
                {shortenAddress(depositor.address)}
              </Text>
              <Text style={styles.addressText}>
                {formatAddress(transaction?.hash)}
              </Text>
              <View style={styles.rightContainer}>
                <Text style={textStyles.labelL}>
                  {formatNumericByDecimalsDisplay(amount, decimals)} {t("NFT")}{" "}
                  -{" "}
                </Text>
                <Text style={textStyles.bodyM}>
                  {formatNumericByDecimalsDisplay(value, decimals)}{" "}
                  <CurrencySymbol currency={currency} />
                </Text>
              </View>
            </View>
          )
        })}
      </View>
    </CollapseWithHeaderView>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    marginVertical: 8,
    width: "100%",
  },
  label: {
    paddingHorizontal: 16,
    flexDirection: "row",
  },
  title: {
    ...textStyles.labelM,
    color: Colors.black7,
  },
  holder: {
    flex: 1,
  },
  hash: {
    flex: 1,
  },
  value: {
    textAlign: "center",
    flex: 2,
  },
  rightContainer: {
    flex: 2,
    justifyContent: "center",
    flexDirection: "row",
    alignItems: "center",
  },
  addressText: {
    ...textStyles.bodyS,
    color: Colors.blueLink,
    flex: 1,
  },
})
