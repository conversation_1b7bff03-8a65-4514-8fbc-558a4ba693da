import React from "react"
import { Linking, StyleSheet, View } from "react-native"
import { EstateTokenAreaUnit } from "src/api"
import { useTranslation } from "react-i18next"
import { shortenAddress } from "utils"
import { DetailItemView } from "components/DetailItemView"

interface DetailsViewProps {
  requestId: string
  uri: string
  area: number
  areaUnit: EstateTokenAreaUnit
}

const DetailsView: React.FC<DetailsViewProps> = ({ requestId, uri }) => {
  const { t } = useTranslation()

  const navigateToRequestId = () => {
    void Linking.openURL(uri)
  }
  const navigateToContractAddress = () => {
    void Linking.openURL(
      "https://bscscan.com/address/0x9919F4cc203badF636cc7fd6A7823004441e8F0f"
    )
  }

  return (
    <View style={styles.detailsContainer}>
      <DetailItemView
        label={`${t("Request ID")}:`}
        value={requestId}
        onPress={navigateToRequestId}
      />
      <DetailItemView
        label={`${t("Contract address")}:`}
        value={shortenAddress("0x9919F4cc203badF636cc7fd6A7823004441e8F0f")}
        onPress={navigateToContractAddress}
      />
      <DetailItemView label={`${t("Token standard")}:`} value={t("erc_1155")} />
      <DetailItemView
        label={`${t("Chain")}:`}
        value={t("Binance Smart Chain")}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  detailsContainer: {
    padding: 12,
  },
})

export { DetailsView }
