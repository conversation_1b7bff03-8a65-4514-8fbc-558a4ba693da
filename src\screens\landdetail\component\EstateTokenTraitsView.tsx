import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { EstateTokenAttribute, TraitType } from "src/api"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { useTranslation } from "react-i18next"
import bedIcon from "assets/images/ic_bed.png"
import credentialIcon from "assets/images/ic_credential.png"
import kitchenIcon from "assets/images/ic_kitchen.png"
import highwayIcon from "assets/images/ic_highway.png"
import categoryIcon from "assets/images/ic_category.png"
import provinceIcon from "assets/images/ic_province.png"
import roomIcon from "assets/images/ic_room.png"
import floorIcon from "assets/images/ic_floor.png"
import umbrellaIcon from "assets/images/ic_umbrella.png"
import attributeIcon from "assets/images/ic_attribute.png"
import areaIcon from "assets/images/ic_area.png"
import sofaIcon from "assets/images/ic_sofa.png"
import sunIcon from "assets/images/ic_sun.png"
import cirlePaperIcon from "assets/images/ic_circle_paper.png"

interface EstateTokenTraitViewProps {
  estateTokenAttribute: EstateTokenAttribute
}

const EstateTokenAttributeView: React.FC<EstateTokenTraitViewProps> = ({
  estateTokenAttribute,
}) => {
  const { t } = useTranslation()

  const getTrait = (traitType: string): { icon: any; title: string } => {
    switch (traitType) {
      case TraitType.CREDENTIAL_TYPE:
        return { icon: credentialIcon, title: t("Credential Type") }
      case TraitType.CITY_PROVINCE:
        return { icon: provinceIcon, title: t("City/Province") }
      case TraitType.DISTRICT:
        return { icon: provinceIcon, title: t("District") }
      case TraitType.WARD:
        return { icon: provinceIcon, title: t("Ward") }
      case TraitType.STREET:
        return { icon: highwayIcon, title: t("Street") }
      case TraitType.ROAD_ACCESS:
        return { icon: highwayIcon, title: t("Road Access") }
      case TraitType.BEDROOMS:
        return { icon: bedIcon, title: t("Bedrooms") }
      case TraitType.AREA_201_300:
        return { icon: areaIcon, title: t("Area") }
      case TraitType.BUILT_SQM:
        return { icon: umbrellaIcon, title: t("Built SQM") }
      case TraitType.ROOMS:
        return { icon: roomIcon, title: t("Rooms") }
      case TraitType.LIVING_ROOMS:
        return { icon: sofaIcon, title: t("Living Rooms") }
      case TraitType.LANDSCAPE:
        return { icon: sunIcon, title: t("Landscape") }
      case TraitType.FLOORS:
        return { icon: floorIcon, title: t("Floors") }
      case TraitType.KITCHENS:
        return { icon: kitchenIcon, title: t("Kitchens") }
      case TraitType.CATEGORY:
        return { icon: categoryIcon, title: t("Category") }
      case TraitType.BATHROOMS:
        return { icon: cirlePaperIcon, title: t("Bathrooms") }
      case TraitType.Nation:
        return { icon: attributeIcon, title: t("Nation") }
      default:
        return { icon: attributeIcon, title: t("Other") }
    }
  }

  const trait = getTrait(estateTokenAttribute.trait_type)

  return (
    <View style={styles.traitItem}>
      <Image source={trait.icon} style={viewStyles.icon} />
      <Text style={styles.traitType}>{trait.title}</Text>
      <Text style={styles.traitValue}>{estateTokenAttribute.value}</Text>
    </View>
  )
}

interface EstateTokenTraitsViewProps {
  estateTokenTraits: EstateTokenAttribute[]
}

const EstateTokenTraitsView: React.FC<EstateTokenTraitsViewProps> = ({
  estateTokenTraits,
}) => {
  return (
    <View style={styles.traitsContainer}>
      {estateTokenTraits.map((attribute, index) => (
        <EstateTokenAttributeView
          key={index}
          estateTokenAttribute={attribute}
        />
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  traitsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    padding: 12,
  },
  traitItem: {
    margin: 4,
    alignItems: "center",
    padding: 12,
    backgroundColor: Colors.surfaceNormal,
    borderRadius: 8,
  },
  traitType: {
    marginTop: 2,
    ...textStyles.labelM,
    maxWidth: 100,
    color: textColors.textBlack,
  },
  traitValue: {
    ...textStyles.bodyM,
    marginTop: 4,
    paddingHorizontal: 8,
    alignContent: "center",
    textAlign: "center",
    maxWidth: 100,
    color: textColors.textBlack9,
  },
})

export { EstateTokenTraitsView }
