import React from "react"
import { Linking, StyleSheet, View } from "react-native"
import { useTranslation } from "react-i18next"
import { CollapseWithHeaderView } from "./CollapseWithHeaderView"
import documentDoneIcon from "assets/images/ic_document_done.png"
import { LegalRequirementGroupView } from "./LegalRequirementGroupView"
import { LegalRequirementDetailModal } from "./LegalRequirementDetailModal"
import { BSCSCAN_URL } from "src/config/env"
import {
  LegalRequirementGroup,
  useLegalRequirement,
} from "../hooks/useLegalRequirement"
import { RequirementType } from "src/api"

interface LegalAspectViewProps {
  metadataId: number
  tokenMintEventTxHash: string
}

const LegalAspectView: React.FC<LegalAspectViewProps> = ({
  metadataId,
  tokenMintEventTxHash,
}) => {
  const { t } = useTranslation()
  const {
    showingDocumentFiles,
    showingIssuerName,
    selectLegalRequirement,
    isOpenModal,
    setIsOpenModal,
    legalRequirementGroups,
  } = useLegalRequirement(metadataId, tokenMintEventTxHash)

  const handleSelectLegalRequirement = (requirementType: RequirementType) => {
    if (requirementType === RequirementType.MORTGAGE_STATUS) {
      const url = `${BSCSCAN_URL}/tx/${tokenMintEventTxHash}`
      Linking.openURL(url)
    } else {
      selectLegalRequirement(requirementType)
    }
  }

  return (
    <CollapseWithHeaderView
      title={t("Administrative Process")}
      headerIconUri={documentDoneIcon}
      showEmpty={legalRequirementGroups.length === 0}
      emptyTitle={t("No legal requirement")}
    >
      <View style={styles.legalRequirementGroups}>
        {legalRequirementGroups.map((item, index) => (
          <DocumentGroupItem
            key={index}
            legalRequirementGroup={item}
            onSelectLegalRequirement={handleSelectLegalRequirement}
          />
        ))}
      </View>
      <LegalRequirementDetailModal
        issuerName={showingIssuerName}
        documentFiles={showingDocumentFiles}
        isOpen={isOpenModal}
        setIsOpen={setIsOpenModal}
      />
    </CollapseWithHeaderView>
  )
}

const DocumentGroupItem: React.FC<{
  legalRequirementGroup: LegalRequirementGroup
  onSelectLegalRequirement: (requirementType: RequirementType) => void
}> = ({ legalRequirementGroup, onSelectLegalRequirement }) => (
  <LegalRequirementGroupView
    legalRequirementGroup={legalRequirementGroup}
    onSelectLegalRequirement={onSelectLegalRequirement}
  />
)

const styles = StyleSheet.create({
  legalRequirementGroups: {
    width: "100%",
  },
})

export { LegalAspectView }
