import React, { useCallback } from "react"
import { CustomPressable } from "components"
import { FlatList, Image, Linking, StyleSheet, Text, View } from "react-native"
import { BaseModal } from "components/common/BaseModal"
import { useTranslation } from "react-i18next"
import Colors, { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { DocumentFile } from "../hooks/useLegalRequirement"
import openNewIcon from "assets/images/ic_open_new.png"

interface LegalRequirementDetailModalProps {
  isOpen: boolean
  setIsOpen: (value: boolean) => void
  issuerName: string
  documentFiles: DocumentFile[]
}

interface DocumentFileViewProps {
  documentFile: DocumentFile
}

const DocumentFileView: React.FC<DocumentFileViewProps> = ({
  documentFile,
}) => {
  const { fileName, fileUrl } = documentFile
  const handleOpenDocument = () => {
    Linking.openURL(fileUrl)
  }

  return (
    <CustomPressable style={styles.row} onPress={handleOpenDocument}>
      <Image source={openNewIcon} style={viewStyles.icon} />
      <Text style={styles.fileName}>{fileName}</Text>
    </CustomPressable>
  )
}

const ListDocumentFileView: React.FC<{
  documentFiles: DocumentFile[]
  renderItem: ({ item }: { item: DocumentFile }) => React.ReactElement
}> = ({ documentFiles, renderItem }) => {
  const keyExtractor = useCallback(
    (item: DocumentFile) => item.fileUrl.toString(),
    []
  )

  return (
    <FlatList
      style={styles.flatList}
      data={documentFiles}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
    />
  )
}

const LegalRequirementDetailModal: React.FC<
  LegalRequirementDetailModalProps
> = ({ isOpen, setIsOpen, documentFiles, issuerName }) => {
  const { t } = useTranslation()
  const onClose = useCallback(() => {
    setIsOpen(false)
  }, [setIsOpen])

  const renderDocumentItem = useCallback(
    ({ item }: { item: DocumentFile }) => (
      <DocumentFileView documentFile={item} />
    ),
    []
  )

  return (
    <BaseModal
      isShowCloseIcon={true}
      visible={isOpen}
      title={t("Detail")}
      onClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.issuer}>
          <Text style={styles.issuerLabel}>{t("Issuer")}</Text>
          <Text style={styles.issuerName}>{issuerName}</Text>
        </View>
        <View style={styles.filesContent}>
          <Text style={styles.legalTitle}>{t("Legal documents")}</Text>
          <ListDocumentFileView
            renderItem={renderDocumentItem}
            documentFiles={documentFiles}
          />
        </View>
      </View>
    </BaseModal>
  )
}

export { LegalRequirementDetailModal }

const styles = StyleSheet.create({
  container: {
    alignContent: "center",
    width: "100%",
  },
  issuer: {
    backgroundColor: Colors.black3,
    borderRadius: 8,
    padding: 16,
  },
  issuerLabel: {
    ...textStyles.titleS,
    color: textColors.textBlack,
  },
  issuerName: {
    ...textStyles.bodyM,
    color: textColors.textBlack,
  },
  filesContent: {
    marginTop: 4,
    maxHeight: 400,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  legalTitle: {
    ...textStyles.titleS,
    color: textColors.textBlack,
    marginTop: 8,
    marginBottom: 4,
  },
  fileName: {
    ...textStyles.bodyM,
    color: Colors.blueLink,
    marginStart: 4,
  },
  flatList: {
    width: "100%",
  },
})
