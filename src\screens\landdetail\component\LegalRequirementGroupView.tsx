import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { LegalRequirementState } from "./LegalRequirementState"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import {
  getIsEnoughRequireData,
  LegalRequirementGroup,
} from "../hooks/useLegalRequirement"
import { RequirementType } from "src/api"

interface LegalRequirementGroupProps {
  legalRequirementGroup: LegalRequirementGroup
  onSelectLegalRequirement: (requirementType: RequirementType) => void
}

const LegalRequirementGroupView: React.FC<LegalRequirementGroupProps> = ({
  legalRequirementGroup,
  onSelectLegalRequirement,
}) => {
  const { title, legalRequirements } = legalRequirementGroup
  const isEnoughRequireData = getIsEnoughRequireData(legalRequirements)
  return (
    <View style={styles.container}>
      <View style={styles.label}>
        <Text style={styles.title}>{title}</Text>
        {!isEnoughRequireData && <Text style={styles.warning}>(!)</Text>}
      </View>
      <View style={styles.documentList}>
        {legalRequirements.map((item, index) => (
          <LegalRequirementState
            key={index}
            legalRequirement={item}
            onSelectLegalRequirement={onSelectLegalRequirement}
          />
        ))}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  label: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  documentList: {
    marginHorizontal: 12,
  },
  title: {
    ...textStyles.titleS,
    color: textColors.textBlack,
  },
  warning: {
    ...textStyles.titleS,
    color: Colors.redLight,
    marginStart: 4,
  },
})

export { LegalRequirementGroupView }
