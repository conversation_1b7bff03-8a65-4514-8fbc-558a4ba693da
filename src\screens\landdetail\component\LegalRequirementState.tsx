import React from "react"
import { StyleSheet, Text } from "react-native"
import { textStyles } from "src/config/styles"
import { CustomPressable } from "src/components"
import Colors, { textColors } from "src/config/colors"
import { useTranslation } from "react-i18next"
import { TFunction } from "i18next"
import { LegalRequirement, RequirementType } from "src/api"

interface LegalRequirementStateConfig {
  color: string
  label: string
  textColor: string
}

interface LegalRequirementStateProps {
  legalRequirement: LegalRequirement
  onSelectLegalRequirement: (requirementType: RequirementType) => void
}

const getLegalRequirementStateConfig = (
  legalRequirement: LegalRequirement,
  t: TFunction
): LegalRequirementStateConfig => {
  const { requirementType, fileUrl } = legalRequirement
  const isEnoughData = fileUrl

  const configs: Record<string, LegalRequirementStateConfig> = {
    OWNERSHIP_CERTIFICATE: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Certificate of Real Estate Ownership/Use Rights"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    COMPANY_IDENTITY_VERIFICATION: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Company Identity Verification"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    LEGAL_REPRESENTATIVE_VERIFICATION: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Legal Representative Identity Verification in Vietnam"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    PARENT_COMPANY_RELATIONSHIP_VERIFICATION: {
      color: isEnoughData ? Colors.lightGreen : Colors.primaryLight,
      label: t("Verification of Relationship with Parent Company"),
      textColor: textColors.textBlack11,
    },
    OWNER_IDENTITY_VERIFICATION: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Property Owner Identity Verification"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    TRANSFER_FROM_OWNER: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Property Transfer from Owner to Company"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    PROPERTY_SEALING: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Property Sealing"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    LEGAL_ASSESSMENT: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Legal Due Diligence of Property"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    MARITAL_STATUS: {
      color: isEnoughData ? Colors.lightGreen : Colors.primaryLight,
      label: t("Marital Status of Property Owner"),
      textColor: textColors.textBlack11,
    },
    ESTATE_VALUATION: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Property Valuation"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    INCOME_TAX: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Personal Income Tax"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    VAT: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Value-Added Tax (VAT)"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    REGISTRATION_FEE: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Registration Fee"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    RESOURCE_TAX: {
      color: isEnoughData ? Colors.lightGreen : Colors.primaryLight,
      label: t("Resource Tax"),
      textColor: textColors.textBlack11,
    },
    CERTIFICATE_UPDATE: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Updated Certificate of Land Use Rights"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    MORTGAGE_STATUS: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Enable digitization, token minting, payments"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    TRANSFER_FROM_MORTGAGEE: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Transfer of real estate from mortgagee to company"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
    MORTGAGE_RELEASE: {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Mortgage release"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    },
  }

  return (
    configs[requirementType] || {
      color: isEnoughData ? Colors.lightGreen : Colors.redLight,
      label: t("Unknown status"),
      textColor: isEnoughData ? textColors.textBlack11 : Colors.white,
    }
  )
}

const LegalRequirementState: React.FC<LegalRequirementStateProps> = ({
  legalRequirement,
  onSelectLegalRequirement,
}) => {
  const { t } = useTranslation()
  const { requirementType, fileUrl } = legalRequirement
  const config = getLegalRequirementStateConfig(legalRequirement, t)

  const handleOpenDocument = () => {
    onSelectLegalRequirement(requirementType)
  }

  return (
    <CustomPressable
      enabled={fileUrl !== ""}
      style={[styles.container, { backgroundColor: config.color }]}
      onPress={handleOpenDocument}
    >
      <Text
        style={[
          styles.status,
          { backgroundColor: config.color, color: config.textColor },
        ]}
      >
        {config.label}
      </Text>
      {!fileUrl && (
        <Text
          style={[textStyles.labelS, { color: config.textColor }]}
        >{`(${t("N/A")})`}</Text>
      )}
    </CustomPressable>
  )
}

const styles = StyleSheet.create({
  status: {
    ...textStyles.labelM,
    alignSelf: "flex-start",
    borderRadius: 4,
    paddingVertical: 4,
  },
  container: {
    justifyContent: "space-between",
    borderRadius: 8,
    marginBottom: 12,
    padding: 16,
    width: "100%",
  },
})

export { LegalRequirementState }
