import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { textStyles } from "src/config/styles"
import { CurrencySymbol } from "components/estates/CurrencySymbol"
import { AddressView } from "components"
import { useTranslation } from "react-i18next"

interface ListHoldNFTViewProps {
  holders: {
    address: string
    amount: string
    tokenAmount: string
    currency: string
  }[]
}

const ListHoldNFTView: React.FC<ListHoldNFTViewProps> = ({ holders }) => {
  const { t } = useTranslation()

  const renderHolder = (holder: (typeof holders)[0], index: number) => {
    return (
      <View key={index} style={styles.holderContainer}>
        <AddressView
          style={styles.holder}
          copy={false}
          address={holder.address}
        />
        <Text style={styles.tokenAmount}>
          {holder.tokenAmount} {t("NFT")}
        </Text>
        <Text style={styles.amount}>
          {holder.amount} <CurrencySymbol currency={holder.currency} />
        </Text>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      {holders.map((holder, index) => renderHolder(holder, index))}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingHorizontal: 12,
    paddingBottom: 12,
  },
  holderContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    marginVertical: 4,
  },
  holder: {
    flexDirection: "row",
    flex: 1,
  },
  tokenAmount: {
    ...textStyles.labelM,
    flex: 1,
  },
  amount: {
    ...textStyles.bodyS,
    textAlign: "center",
    flex: 1,
  },
})

export { ListHoldNFTView }
