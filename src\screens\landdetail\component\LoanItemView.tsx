import React, { useContext } from "react"
import { StyleSheet, Text, View } from "react-native"
import { MortgageTokenLoan } from "src/api"
import { useTranslation } from "react-i18next"
import { convertSecondsToTime } from "utils/timeExt"
import { textStyles } from "src/config/styles"
import { LoanActionButton, LoanState, MoneyWithCurrency } from "components"
import { shortenAddress } from "utils"
import { formatCurrency, formatNumericByDecimals } from "utils/format"
import EstateDetailContext from "../context/EstateContext"
import Colors from "src/config/colors"

const LoanDetail: React.FC<{ label: string; value: string }> = ({
  label,
  value,
}) => {
  return (
    <View style={styles.rowSpaceBetween}>
      <Text style={textStyles.labelL}>{label}: </Text>
      <Text style={textStyles.bodyM}>{value}</Text>
    </View>
  )
}

const LoanDetailWithCurrency: React.FC<{
  label: string
  value: string
  decimals?: number
  currency?: string
}> = ({ label, value, decimals = 18, currency }) => {
  return (
    <View style={styles.rowSpaceBetween}>
      <Text style={textStyles.labelL}>{label}: </Text>
      <MoneyWithCurrency
        amount={value}
        amountStyle={textStyles.bodyM}
        decimals={decimals}
        currency={currency}
        shouldShowIcon={false}
      />
    </View>
  )
}

interface LoanItemViewProps {
  loan: MortgageTokenLoan
}

const LoanItemView: React.FC<LoanItemViewProps> = ({ loan }) => {
  const { t } = useTranslation()
  const { onRefresh } = useContext(EstateDetailContext)

  const {
    currency,
    repayment,
    mortgageAmount,
    principal,
    state,
    estate: { decimals },
    borrower,
    dueInSeconds,
    durationInSeconds,
  } = loan

  const overdue =
    state !== "PENDING" &&
    state !== "CANCELLED" &&
    new Date().getTime() / 1000 > dueInSeconds

  return (
    <View style={styles.itemContainer}>
      <LoanState state={state} style={styles.state} overdue={overdue} />
      <View style={styles.rowSpaceBetween}>
        <Text style={textStyles.labelL}>{`${t("From")}:`}</Text>
        <Text style={styles.addressText}>
          {shortenAddress(borrower.address)}
        </Text>
      </View>
      <LoanDetail
        label={t("Duration")}
        value={convertSecondsToTime(durationInSeconds)}
      />
      <LoanDetail
        label={t("Mortgage")}
        value={`${formatCurrency(formatNumericByDecimals(mortgageAmount, decimals))} ${t("NFT")}`}
      />
      <LoanDetailWithCurrency
        label={t("Principal")}
        value={principal}
        currency={currency}
        decimals={decimals}
      />
      <LoanDetailWithCurrency
        label={t("Repayment")}
        value={repayment}
        currency={currency}
        decimals={decimals}
      />
      <LoanActionButton loan={loan} onRefresh={onRefresh} />
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    borderWidth: 1,
    borderColor: Colors.black5,
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  title: {
    ...textStyles.labelL,
    marginTop: 4,
    marginBottom: 8,
  },
  state: {
    marginBottom: 4,
  },
  addressText: {
    ...textStyles.bodyM,
    color: Colors.blueLink,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
})

export { LoanItemView }
