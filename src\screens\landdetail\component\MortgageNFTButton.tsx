import React from "react"
import { DimensionValue } from "react-native"
import { PrimaryButton } from "components"
import MortgageNFTModal from "screens/landdetail/component/MortgageNFTModal"
import { Estate } from "src/api"
import { useTranslation } from "react-i18next"
import { useAccount } from "wagmi"
import { useEstateTokenBalance } from "src/api/contracts"

interface Props {
  estate: Estate
  width?: DimensionValue
}

export const MortgageNFTButton: React.FC<Props> = ({ estate, width = 150 }) => {
  const { t } = useTranslation()
  const { address } = useAccount()
  const [isShow, setIsShow] = React.useState<boolean>(false)

  const { value: nftBalance } = useEstateTokenBalance(
    address as `0x${string}`,
    estate.id
  )
  const isBalancePositive = Number(nftBalance) > 0

  if (!estate || !address) return null
  return (
    <>
      <PrimaryButton
        title={t("Mortgage")}
        onPress={() => setIsShow(true)}
        width={width}
        borderRadius={8}
        height={44}
        enabled={isBalancePositive}
        style={{ marginBottom: 8 }}
      />
      <MortgageNFTModal
        estate={estate}
        isShow={isShow}
        onClose={() => setIsShow(false)}
      />
    </>
  )
}
