import React, { useState } from "react"
import { StyleSheet, Text, View } from "react-native"
import { textStyles } from "src/config/styles"
import { InputField, PrimaryButton, SelectCurrency } from "components"
import { Dropdown } from "react-native-element-dropdown"
import Colors from "src/config/colors"
import { Currency, Estate, getCurrencies } from "src/api"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import {
  collectionAbi,
  useCollectionIsApprovedForAll,
  useEstateTokenBalance,
} from "src/api/contracts"
import {
  CONTRACT_ADDRESS_ESTATE_TOKEN,
  CONTRACT_ADDRESS_MORTGAGE_TOKEN,
} from "src/config/env"
import { useQuery } from "@tanstack/react-query"
import { z } from "zod"
import { Controller, useForm } from "react-hook-form"
import { mortgageTokenAbi } from "src/api/contracts/mortgage-token"
import { ContractFunctionExecutionError } from "viem"
import { zodResolver } from "@hookform/resolvers/zod"
import { BaseModal } from "components/common/BaseModal"
import { useEthersProvider } from "hooks"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import QueryKeys from "src/config/queryKeys"
import { LabelView } from "components/common/LabelView"
import { queryClient } from "src/api/query"
import { formatNumber } from "utils"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "MortgageNFTModal" })

const useFormSchema = (nftBalance: string) => {
  const { t } = useTranslation()
  const formSchema = z.object({
    currencyId: z.string(),
    mortgageAmount: z
      .number()
      .min(1, {
        message: t("Please input a valid mortgage amount greater than 0"),
      })
      .refine((val) => Number(nftBalance) >= val, {
        message: t("Mortgage amount cannot exceed your NFT balance"),
      }),
    principal: z.number().min(1, {
      message: t("Please input a valid principal amount greater than 0"),
    }),
    repayment: z.number().min(1, {
      message: t("Please input a valid repayment amount greater than 0"),
    }),
    duration: z.number().refine((val) => +val > 0, {
      message: t("Please input the mortgage duration"),
    }),
    durationUnit: z.string(),
  })
  return formSchema
}

const durationUnitMap: Record<string, number> = {
  minute: 60,
  hour: 60 * 60,
  day: 60 * 60 * 24,
  week: 60 * 60 * 24 * 7,
  month: 60 * 60 * 24 * 30,
}

const calcDefaultCurrency = (currencies: Currency[]): Currency | undefined => {
  const USDTCurrency = currencies.find((i) => i.symbol === "USDT")
  return USDTCurrency || currencies?.[0]
}

interface MortgageNFTModalProps {
  estate: Estate
  isShow: boolean
  onClose: () => void
}

const MortgageNFTModal: React.FC<MortgageNFTModalProps> = ({
  isShow,
  onClose,
  estate,
}) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()
  const isApprovedForAll = useCollectionIsApprovedForAll(
    address as string,
    CONTRACT_ADDRESS_MORTGAGE_TOKEN
  )
  const { data: currencies = [] } = useQuery({
    queryKey: QueryKeys.CURRENCY.LIST,
    queryFn: () => getCurrencies(),
  })

  const { value: nftBalance, queryKey } = useEstateTokenBalance(
    address as `0x${string}`,
    estate.id
  )
  const isBalancePositive = Number(nftBalance) > 0

  const formSchema = useFormSchema(nftBalance)

  type Payload = z.infer<typeof formSchema>

  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      currencyId: calcDefaultCurrency(currencies)?.currency,
      mortgageAmount: 0,
      principal: 0,
      repayment: 0,
      duration: 0,
      durationUnit: "minute",
    },
  })

  const watchedCurrencyId = form.watch("currencyId")
  const selectedCurrency = currencies.find(
    (c) => c.currency.toLowerCase() === watchedCurrencyId?.toLowerCase()
  )

  const closeAndResetForm = () => {
    form.reset()
    setIsLoading(false)
    onClose()
  }

  const onSubmit = async (data: Payload) => {
    if (
      isLoading ||
      !ethersProvider ||
      !Number(estate.id) ||
      !data.currencyId ||
      !data.mortgageAmount ||
      !data.principal ||
      !data.repayment
    )
      return

    setIsLoading(true)
    try {
      if (!isApprovedForAll) {
        const txHash = await writeContractAsync({
          address: CONTRACT_ADDRESS_ESTATE_TOKEN,
          abi: collectionAbi,
          functionName: "setApprovalForAll",
          args: [CONTRACT_ADDRESS_MORTGAGE_TOKEN, true],
        })
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          throw new Error(t("Failed to set approval for all"))
        }
      }

      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
        abi: mortgageTokenAbi,
        functionName: "borrow",
        args: [
          BigInt(estate.id),
          BigInt(data.mortgageAmount) * BigInt(Math.pow(10, estate.decimals)),
          BigInt(data.principal) * BigInt(Math.pow(10, estate.decimals)),
          BigInt(data.repayment) * BigInt(Math.pow(10, estate.decimals)),
          data.currencyId,
          data.duration * durationUnitMap[data.durationUnit],
        ],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        await queryClient.invalidateQueries({ queryKey })
        showSuccessWhenCallContract(
          `${t("Borrow success")}. ${t("Data will be updated in few seconds")}`
        )
      } else {
        throw new Error(t("Borrow failed"))
      }
    } catch (e: any) {
      if (e instanceof ContractFunctionExecutionError) {
        logger.error("Contract execution error details", {
          cause: e.cause,
          metaMessages: e.cause.metaMessages,
          docsPath: e.cause.docsPath,
        })
      }
      logger.error("Mortgage NFT error", e)
      showError(e.message)
    } finally {
      setIsLoading(false)
      closeAndResetForm()
    }
  }

  if (!address) {
    return null
  }

  return (
    <BaseModal
      visible={isShow}
      isDisableClose={isLoading}
      onClose={closeAndResetForm}
      isShowCloseIcon={true}
      title={t("Mortgage Native Land")}
    >
      <View style={[styles.modalContent]}>
        <Controller
          control={form.control}
          name="mortgageAmount"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputField
              label={t("Mortgage amount")}
              value={value}
              onChangeText={onChange}
              type={"number"}
              inputMode={"decimal"}
              onBlur={onBlur}
              style={styles.marginTop12}
              error={form.formState.errors.mortgageAmount?.message}
            />
          )}
        />
        <View style={styles.row}>
          <Controller
            control={form.control}
            name="principal"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Principal")}
                value={value}
                onChangeText={onChange}
                type={"number"}
                inputMode={"decimal"}
                onBlur={onBlur}
                style={styles.principal}
                error={form.formState.errors.principal?.message}
              />
            )}
          />
          <SelectCurrency
            currencies={currencies}
            control={form.control}
            style={styles.currency}
            setValue={form.setValue}
          />
        </View>
        <View style={styles.row}>
          <Controller
            control={form.control}
            name="repayment"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Repayment")}
                value={value}
                onChangeText={(val) => {
                  onChange(Number(val))
                }}
                inputMode={"decimal"}
                type={"number"}
                onBlur={onBlur}
                style={styles.flex1}
                error={form.formState.errors.repayment?.message}
              />
            )}
          />
          <Text style={[textStyles.titleL, styles.marginTop26]}>
            {selectedCurrency?.symbol}
          </Text>
        </View>
        <View style={styles.row}>
          <Controller
            control={form.control}
            name="duration"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Duration")}
                value={value}
                onChangeText={onChange}
                inputMode={"decimal"}
                onBlur={onBlur}
                style={styles.flex1}
                type={"number"}
                error={form.formState.errors.duration?.message}
              />
            )}
          />
          <Controller
            control={form.control}
            name="durationUnit"
            defaultValue={"minute"}
            render={({ field: { onChange, value } }) => (
              <TimePicker timeType={value} setTimeType={onChange} />
            )}
          />
        </View>

        <LabelView label={t("Balance")} style={styles.marginTop8} />
        <Text
          style={[
            textStyles.bodyM,
            !isBalancePositive && { color: Colors.red, fontWeight: "700" },
          ]}
        >
          {formatNumber(nftBalance)} {" NFT"}
        </Text>

        <PrimaryButton
          title={t("Borrow")}
          onPress={form.handleSubmit(onSubmit)}
          style={styles.marginTop8}
          isLoading={isLoading}
          enabled={isBalancePositive}
        />
      </View>
    </BaseModal>
  )
}

const TimePicker: React.FC<{
  timeType: string
  setTimeType: (timeType: string) => void
}> = ({ timeType, setTimeType }) => {
  const { t } = useTranslation()
  const data = [
    { label: t("minute"), value: "minute" },
    { label: t("hour"), value: "hour" },
    { label: t("day"), value: "day" },
    { label: t("week"), value: "week" },
    { label: t("month"), value: "month" },
  ]

  return (
    <Dropdown
      style={styles.dropdown}
      data={data}
      selectedTextStyle={textStyles.bodyM}
      labelField="label"
      valueField="value"
      value={timeType}
      onChange={(item) => {
        setTimeType(item.value)
      }}
    />
  )
}

const styles = StyleSheet.create({
  modalContent: {
    width: "100%",
    backgroundColor: "white",
  },
  marginTop12: {
    marginTop: 12,
  },
  marginTop26: {
    marginTop: 34,
    marginStart: 8,
  },
  principal: {
    width: "70%",
  },
  marginTop8: {
    marginTop: 8,
  },
  currency: {
    height: 36,
    width: "100%",
    borderColor: Colors.black5,
    borderWidth: 1,
    marginTop: 26,
    marginStart: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
  },
  row: {
    flexDirection: "row",
    marginTop: 12,
  },
  flex1: {
    flex: 1,
  },
  dropdown: {
    height: 36,
    width: 80,
    borderColor: Colors.black5,
    borderWidth: 1,
    marginTop: 26,
    marginStart: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
  },
})

export default MortgageNFTModal
