import React from "react"
import { DimensionValue, ViewStyle } from "react-native"
import NewOfferModal from "screens/landdetail/component/NewOfferModal"
import { PrimaryButton } from "components"
import { useTranslation } from "react-i18next"
import { useAccount } from "wagmi"
import { Estate } from "src/api"
import { useEstateTokenBalance } from "src/api/contracts"

interface Props {
  estate: Estate
  style?: ViewStyle
  width?: DimensionValue
}

export const NewOfferButton: React.FC<Props> = ({ estate, style, width }) => {
  const { t } = useTranslation()
  const { address } = useAccount()
  const [isShow, setIsShow] = React.useState<boolean>(false)

  const { value: nftBalance } = useEstateTokenBalance(
    address as `0x${string}`,
    estate.id
  )
  const isBalancePositive = Number(nftBalance) > 0

  if (!estate || !address) return null
  return (
    <>
      <PrimaryButton
        title={t("New Offer")}
        onPress={() => setIsShow(true)}
        width={width}
        borderRadius={8}
        style={style}
        enabled={isBalancePositive}
      />
      {estate && (
        <NewOfferModal
          estate={estate}
          isShow={isShow}
          onClose={() => setIsShow(false)}
        />
      )}
    </>
  )
}
