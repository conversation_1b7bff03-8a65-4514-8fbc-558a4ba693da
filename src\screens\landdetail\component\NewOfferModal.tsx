import React, { useEffect, useState } from "react"
import { StyleSheet, Switch, Text, View } from "react-native"
import { textStyles } from "src/config/styles"
import { InputField, PrimaryButton, SelectCurrency } from "components"
import { convertHexToRGBA, formatNumber } from "utils"
import Colors from "src/config/colors"
import {
  CONTRACT_ADDRESS_ESTATE_TOKEN,
  CONTRACT_ADDRESS_MARKETPLACE,
} from "src/config/env"
import {
  collectionAbi,
  useCollectionIsApprovedForAll,
  useEstateTokenBalance,
} from "src/api/contracts"
import { ContractFunctionExecutionError } from "viem"
import { marketplaceAbi } from "src/api/contracts/marketplace"
import { parseEther } from "@ethersproject/units"
import { Estate, getCurrencies } from "src/api"
import { useQuery } from "@tanstack/react-query"
import { z } from "zod"
import { Controller, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useTranslation } from "react-i18next"
import { BaseModal } from "components/common/BaseModal"
import { useAccount, useWriteContract } from "wagmi"
import { useEthersProvider } from "hooks"
import { showError, showSuccessWhenCallContract } from "src/utils/toast"
import { queryClient } from "src/api/query"
import QueryKeys from "src/config/queryKeys"
import { LabelView } from "components/common/LabelView"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "NewOfferModal" })

const useFormSchema = (nftBalance: string) => {
  const { t } = useTranslation()

  return z.object({
    currencyId: z.string(),
    unitPrice: z.number().min(1, {
      message: t("Please input a valid unit price greater than 0"),
    }),
    sellingAmount: z
      .number()
      .min(1, {
        message: t("Please input a valid selling amount greater than 0"),
      })
      .refine((val) => Number(nftBalance) >= val, {
        message: t("Selling amount cannot exceed your NFT balance"),
      }),
    isDiviable: z.boolean(),
  })
}

interface NewOfferModalProps {
  estate: Estate
  isShow: boolean
  onClose: () => void
}

const NewOfferModal: React.FC<NewOfferModalProps> = ({
  isShow,
  onClose,
  estate,
}) => {
  const { t } = useTranslation()

  const [isLoading, setIsLoading] = useState(false)

  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const { data: currencies = [] } = useQuery({
    queryKey: QueryKeys.CURRENCY.LIST,
    queryFn: () => getCurrencies(),
  })

  const { value: nftBalance, queryKey } = useEstateTokenBalance(
    address as `0x${string}`,
    estate.id
  )

  const isBalancePositive = Number(nftBalance) > 0

  const formSchema = useFormSchema(nftBalance)
  type Payload = z.infer<typeof formSchema>
  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      currencyId: currencies[0]?.currency,
      unitPrice: 0,
      sellingAmount: 0,
      isDiviable: false,
    },
  })

  const isApprovedForAll = useCollectionIsApprovedForAll(
    address as string,
    CONTRACT_ADDRESS_MARKETPLACE
  )

  useEffect(() => {
    if (currencies.length > 0 && !form.getValues("currencyId")) {
      form.setValue("currencyId", currencies[0].symbol.toLowerCase())
    }
  }, [currencies, form])

  const onSubmit = async (data: Payload) => {
    if (
      isLoading ||
      !ethersProvider ||
      !Number(estate.id) ||
      !data.unitPrice ||
      !data.unitPrice ||
      !data.sellingAmount
    )
      return

    setIsLoading(true)
    try {
      if (!isApprovedForAll) {
        const txHash = await writeContractAsync({
          address: CONTRACT_ADDRESS_ESTATE_TOKEN,
          abi: collectionAbi,
          functionName: "setApprovalForAll",
          args: [CONTRACT_ADDRESS_MARKETPLACE, true],
        })
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          throw new Error("Failed to set approval for all")
        }
      }

      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MARKETPLACE,
        abi: marketplaceAbi,
        functionName: "listToken",
        args: [
          BigInt(estate.id),
          BigInt(data.sellingAmount) * BigInt(Math.pow(10, estate.decimals)),
          parseEther(data.unitPrice.toString()).toBigInt(),
          data.currencyId,
          data.isDiviable,
        ],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        await queryClient.invalidateQueries({ queryKey })
        showSuccessWhenCallContract(
          t("Create offer success") +
            ". " +
            t("Data will be updated in few seconds")
        )
      } else {
        throw new Error(t("Create offer failed"))
      }
    } catch (e: any) {
      if (e instanceof ContractFunctionExecutionError) {
        logger.error("Contract execution error details", {
          cause: e.cause,
          metaMessages: e.cause.metaMessages,
          docsPath: e.cause.docsPath,
        })
      }
      logger.error("Create offer failed", e)
      const errorMessage = `${t("Create offer failed")} ${e?.toString()}`
      showError(errorMessage)
    } finally {
      closeAndReset()
    }
  }

  const closeAndReset = () => {
    form.reset()
    setIsLoading(false)
    onClose()
  }

  if (!address) {
    return null
  }

  return (
    <BaseModal
      visible={isShow}
      isShowCloseIcon={true}
      isDisableClose={isLoading}
      title={t("New Offer")}
      onClose={closeAndReset}
    >
      <View style={styles.modalContent}>
        <View style={styles.row}>
          <Controller
            control={form.control}
            name="unitPrice"
            render={({ field: { onChange, onBlur, value } }) => (
              <InputField
                label={t("Unit price")}
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
                inputMode={"decimal"}
                type={"number"}
                style={styles.unitPrice}
                error={form.formState.errors.unitPrice?.message}
              />
            )}
          />
          <SelectCurrency
            currencies={currencies}
            control={form.control}
            style={styles.currency}
            setValue={form.setValue}
          />
        </View>
        <View style={styles.row}>
          <Controller
            control={form.control}
            name="sellingAmount"
            render={({ field: { onChange, value } }) => (
              <InputField
                label={t("Selling amount")}
                value={value}
                onChangeText={onChange}
                inputMode={"decimal"}
                type={"number"}
                style={styles.flex1}
                error={form.formState.errors.sellingAmount?.message}
              />
            )}
          />
          <Text style={[textStyles.titleS, styles.nftText]}>{t("NFT")}</Text>
          <View>
            <Text style={textStyles.titleS}>{t("Diviable")}</Text>
            <Controller
              control={form.control}
              name="isDiviable"
              render={({ field: { onChange, value } }) => (
                <Switch
                  value={value}
                  onValueChange={onChange}
                  trackColor={{
                    false: convertHexToRGBA(Colors.border, 0.5),
                    true: convertHexToRGBA(Colors.primary, 0.5),
                  }}
                  thumbColor={value ? Colors.primary : Colors.border}
                />
              )}
            />
          </View>
        </View>
        <LabelView label={t("Balance")} style={styles.marginTop8} />
        <Text
          style={[
            textStyles.bodyM,
            !isBalancePositive && { color: Colors.red, fontWeight: "700" },
          ]}
        >
          {formatNumber(nftBalance)} {" NFT"}
        </Text>
        <PrimaryButton
          title={t("Create New Offer")}
          onPress={form.handleSubmit(onSubmit)}
          style={styles.marginTop8}
          isLoading={isLoading}
          enabled={isBalancePositive}
        />
      </View>
    </BaseModal>
  )
}

const styles = StyleSheet.create({
  modalContent: {
    width: "100%",
  },
  row: {
    flexDirection: "row",
    marginTop: 12,
  },
  unitPrice: {
    width: "70%",
  },
  inputField: {
    flex: 1,
    marginTop: 12,
  },
  flex1: {
    flex: 1,
  },
  nftText: {
    marginStart: 12,
    marginRight: 20,
    marginTop: 36,
  },
  marginTop8: {
    marginTop: 8,
  },
  currency: {
    height: 36,
    width: "100%",
    borderColor: Colors.black5,
    borderWidth: 1,
    marginTop: 26,
    marginStart: 8,
    paddingHorizontal: 4,
    borderRadius: 8,
  },
})

export default NewOfferModal
