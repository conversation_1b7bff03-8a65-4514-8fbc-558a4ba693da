import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { MarketplaceOffer } from "src/api"
import { useTranslation } from "react-i18next"
import Colors from "src/config/colors"
import { MoneyWithCurrency, OfferState } from "components"
import { OfferActionButton } from "components/estates/OfferActionButton"
import { textStyles, viewStyles } from "src/config/styles"
import { shortenAddress } from "utils"
import { formatCurrencyByDecimals } from "utils/format"
import successIcon from "assets/images/ic_success.png"
import failureIcon from "assets/images/ic_failure.png"

const OfferDetail: React.FC<{ label: string; value: string }> = ({
  label,
  value,
}) => {
  return (
    <View style={styles.rowSpaceBetween}>
      <Text style={textStyles.labelL}>{label}: </Text>
      <Text style={textStyles.bodyM}>{value}</Text>
    </View>
  )
}

const OfferDetailWithCurrency: React.FC<{
  label: string
  value: string
  decimals?: number
  currency?: string
}> = ({ label, value, decimals = 18, currency }) => {
  return (
    <View style={styles.rowSpaceBetween}>
      <Text style={textStyles.labelL}>{label}: </Text>
      <MoneyWithCurrency
        amount={value}
        amountStyle={textStyles.bodyM}
        decimals={decimals}
        currency={currency}
        shouldShowIcon={false}
      />
    </View>
  )
}

interface OfferItemViewProps {
  offer: MarketplaceOffer
}

const OfferItemView: React.FC<OfferItemViewProps> = ({ offer }) => {
  const { t } = useTranslation()
  const {
    sellingAmount,
    soldAmount,
    unitPrice,
    state,
    isDivisible,
    seller,
    currency,
    estate: { decimals },
  } = offer

  return (
    <View style={styles.itemContainer}>
      <OfferState state={state} />
      <View style={styles.rowSpaceBetween}>
        <Text style={textStyles.labelL}>{`${t("From")}:`}</Text>
        <Text style={styles.addressText}>{shortenAddress(seller.address)}</Text>
      </View>
      <OfferDetailWithCurrency
        label={t("Price")}
        value={unitPrice}
        currency={currency}
        decimals={decimals}
      />
      <OfferDetail
        label={t("Quantity")}
        value={formatCurrencyByDecimals(sellingAmount, decimals)}
      />
      <OfferDetail
        label={t("Sold")}
        value={formatCurrencyByDecimals(soldAmount, decimals)}
      />
      <View style={styles.rowSpaceBetween}>
        <Text style={textStyles.labelL}>{t("Diviable")}</Text>
        <Image
          style={viewStyles.tinyIcon}
          source={isDivisible ? successIcon : failureIcon}
        />
      </View>
      <OfferActionButton offer={offer} />
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    borderWidth: 1,
    borderColor: Colors.black5,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  title: {
    ...textStyles.labelL,
    marginTop: 4,
    marginBottom: 8,
  },
  addressText: {
    ...textStyles.bodyM,
    color: Colors.blueLink,
  },
})

export { OfferItemView }
