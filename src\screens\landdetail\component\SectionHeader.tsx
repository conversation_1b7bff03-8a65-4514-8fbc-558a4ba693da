import React from "react"
import {
  Image,
  Pressable,
  StyleProp,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from "react-native"
import { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import upIcon from "assets/images/ic_up.png"
import downIcon from "assets/images/ic_down.png"

const SectionHeader: React.FC<{
  style?: StyleProp<ViewStyle>
  icon: React.ReactNode
  title: string
  isOpen: boolean
  onPress: () => void
}> = ({ style = styles.sectionHeader, icon, title, isOpen, onPress }) => (
  <Pressable style={styles.pressable} onPress={onPress}>
    <View style={style}>
      {icon}
      <Text style={styles.sectionTitle}>{title}</Text>
      <Image source={isOpen ? upIcon : downIcon} style={viewStyles.icon} />
    </View>
  </Pressable>
)

export { SectionHeader }

const styles = StyleSheet.create({
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    margin: 12,
  },
  sectionTitle: {
    ...textStyles.titleM,
    color: textColors.textBlack11,
    marginStart: 8,
    flex: 1,
  },
  pressable: {
    paddingVertical: 4,
    width: "100%",
  },
})
