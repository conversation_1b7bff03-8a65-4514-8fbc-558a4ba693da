import React, { useState } from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import {
  CardView,
  ExpandView,
  GradientButton,
  MoneyWithCurrency,
  QuantityView,
  SimpleLoadingView,
} from "components"
import Colors, { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import { CONTRACT_ADDRESS_ESTATE_TOKEN, MAX_UINT256 } from "src/config/env"
import {
  collectionAbi,
  useCollectionDepositedAmount,
  useErc20Allowance,
} from "src/api/contracts"
import { useAccount, useWriteContract } from "wagmi"
import { useEthersProvider } from "hooks"
import Big from "big.js"
import { getDepositorsByEstateRequestId, TokenizationRequest } from "src/api"
import { erc20Abi } from "src/api/contracts/erc20"
import {
  formatCurrency,
  formatCurrencyByDecimals,
  formatNumericByDecimals,
} from "utils"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import { useQuery } from "@tanstack/react-query"
import QueryKeys from "src/config/queryKeys"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "TokenizationDepositView" })

interface TokenizationDepositViewProps {
  style?: ViewStyle
  request: TokenizationRequest
}

const TokenizationDepositView: React.FC<TokenizationDepositViewProps> = ({
  style,
  request,
}) => {
  const {
    unitPrice,
    currency: currencyId,
    totalSupply,
    decimals,
    maxSellingAmount,
  } = request

  const { data: depositors = [] } = useQuery({
    queryKey: [...QueryKeys.ESTATE.DEPOSITORS(request.id)],
    queryFn: () => getDepositorsByEstateRequestId(request.id),
    refetchOnMount: true,
    refetchInterval: 10_000,
  })

  const totalSoldAmount = depositors
    .map((d) => BigInt(d.tokenAmount))
    .reduce((prev, current) => {
      return prev + current
    }, 0n)
  const availableAmount = BigInt(maxSellingAmount) - totalSoldAmount

  const { t } = useTranslation()
  const [quantity, setQuantity] = useState<string | number>(0)
  const { address } = useAccount()
  const [isLoading, setIsLoading] = useState(false)
  const [canCancelLoading, setCanCancelLoading] = useState(true)
  const userDepositedAmount = useCollectionDepositedAmount(
    `${request.id}`,
    address as `0x${string}`
  )

  const ethersProvider = useEthersProvider()

  const price = BigInt(unitPrice) * BigInt(isNaN(+quantity) ? 0 : +quantity)
  const { writeContractAsync } = useWriteContract()

  const maxAmount = BigInt(totalSupply) - totalSoldAmount
  const formValid = +quantity > 0 && +quantity <= maxAmount

  const currentCurrencyAllowanceWei = useErc20Allowance(
    address,
    currencyId as `0x${string}`,
    CONTRACT_ADDRESS_ESTATE_TOKEN
  )

  const handleDeposit = async () => {
    if (!ethersProvider) return
    try {
      setIsLoading(true)
      const currencyAmountWei =
        BigInt(unitPrice) * BigInt(quantity) * BigInt(Math.pow(10, decimals))
      if (currentCurrencyAllowanceWei < currencyAmountWei) {
        const txHash = await writeContractAsync({
          address: currencyId as `0x${string}`,
          abi: erc20Abi,
          functionName: "approve",
          args: [CONTRACT_ADDRESS_ESTATE_TOKEN, MAX_UINT256],
        })
        setCanCancelLoading(!txHash)
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          throw new Error(t("Approve failed"))
        }
      }
      setCanCancelLoading(true)
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_ESTATE_TOKEN,
        abi: collectionAbi,
        functionName: "depositTokenization",
        args: [
          BigInt(request.id),
          BigInt(quantity) * BigInt(Math.pow(10, decimals)),
        ],
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Deposit tokenization success") +
            ". " +
            t("Data will be updated in few seconds")
        )
        setQuantity(0)
      } else {
        throw new Error(t("Deposit tokenization failed"))
      }
    } catch (e) {
      showError(t("Deposit tokenization failed"))
      logger.error("Deposit tokenization failed", e)
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (value: string | number) => {
    const remain = Big(value).mod(1)
    const decimalCount = remain.toFixed().split(".")[1]?.length
    if (!decimalCount || decimalCount <= decimals) {
      const formatValue =
        `${value}`[0] === "0" &&
        (+value >= 1 || (+value === 0 && `${value}`[1] === "0"))
          ? `${value}`.slice(1)
          : value
      setQuantity(formatValue)
    }
    return
  }

  if (!address) {
    return null
  }

  return (
    <>
      <CardView style={style}>
        <View style={styles.container}>
          <Text style={styles.title}>{t("Deposit")}</Text>
          <View style={styles.sell}>
            <InfoRow
              label={`${t("Maximum sale")}:`}
              value={formatCurrencyByDecimals(
                request.maxSellingAmount,
                request.decimals
              )}
            />
            <InfoRow
              label={t("Sold")}
              value={formatCurrencyByDecimals(
                totalSoldAmount.toString(),
                request.decimals
              )}
            />
          </View>
          <InfoRow
            style={styles.paddingHorizontalDefault}
            label={`${t("Ready to deposit")}:`}
            value={formatCurrency(
              formatNumericByDecimals(`${availableAmount}`, request.decimals)
            )}
          />
          <InfoRow
            style={styles.paddingHorizontalDefault}
            label={t("You have deposited")}
            value={formatCurrencyByDecimals(
              userDepositedAmount.toString(),
              request.decimals
            )}
          />
          <View style={styles.depositRow}>
            <MoneyWithCurrency
              amount={price.toString()}
              decimals={decimals}
              amountStyle={{ ...textStyles.labelM }}
              currency={currencyId}
              style={{
                borderWidth: 1,
                paddingVertical: 12,
                paddingHorizontal: 12,
                borderRadius: 8,
                borderColor: Colors.black5,
              }}
            />
            <ExpandView />
            <QuantityView
              value={quantity as number}
              onChangeValue={handleInputChange}
            />
          </View>
          <View style={styles.unitPrice}>
            <Text
              style={styles.startingPrice}
            >{`${t("Starting price")}: `}</Text>
            <MoneyWithCurrency
              amount={unitPrice}
              decimals={decimals}
              currency={currencyId}
              shouldShowIcon
            />
            <Text style={styles.startingPrice}> /1 {t("NFT")}</Text>
          </View>
          <GradientButton
            onPress={handleDeposit}
            enabled={formValid}
            isLoading={isLoading}
            borderRadius={8}
            style={styles.depositButton}
            title={t("Deposit")}
          />
        </View>
      </CardView>
      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </>
  )
}

const InfoRow: React.FC<{
  label: string
  value: string
  style?: ViewStyle
}> = ({ label, value, style }) => (
  <View style={[styles.infoRow, style]}>
    <Text style={textStyles.bodyM}>{label}</Text>
    <Text style={textStyles.bodyM}>{value}</Text>
  </View>
)

export { TokenizationDepositView }

const styles = StyleSheet.create({
  container: {
    padding: 12,
  },
  title: {
    ...textStyles.titleM,
    color: textColors.textBlack11,
  },
  sell: {
    borderRadius: 8,
    backgroundColor: Colors.black3,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginVertical: 8,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  depositValueRow: {
    alignItems: "center",
    flexDirection: "row",
    borderWidth: 1,
    borderRadius: 8,
    borderColor: Colors.black5,
    paddingHorizontal: 12,
    height: 40,
  },
  depositRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8,
    paddingHorizontal: 16,
  },
  unitPrice: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginVertical: 8,
    alignItems: "center",
    paddingHorizontal: 16,
  },
  usdtIcon: {
    ...viewStyles.tinyIcon,
    marginStart: 4,
  },
  depositButton: {
    marginTop: 8,
  },
  paddingHorizontalDefault: {
    paddingHorizontal: 16,
  },
  startingPrice: {
    ...textStyles.bodyS,
    color: textColors.textBlack,
  },
})
