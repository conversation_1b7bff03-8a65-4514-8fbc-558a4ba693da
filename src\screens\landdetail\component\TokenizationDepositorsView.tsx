import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { ListHoldNFTView } from "./ListHoldNFTView"
import { useTranslation } from "react-i18next"
import { getDepositorsByEstateRequestId, TokenizationRequest } from "src/api"
import { useQuery } from "@tanstack/react-query"
import { formatMoneyByDecimals } from "utils"
import { CollapseWithHeaderView } from "./CollapseWithHeaderView"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { QueryKeys } from "src/config/queryKeys"
import usersIcon from "assets/images/ic_users.png"

interface TokenizationDepositorsViewProps {
  request: TokenizationRequest
}

const TokenizationDepositorsLabelsView: React.FC = () => {
  const { t } = useTranslation()

  return (
    <View style={styles.header}>
      <Text style={[styles.title, styles.holder]}>{t("From")}</Text>
      <Text style={[styles.title, styles.deposit]}>{t("Quantity")}</Text>
      <Text style={[styles.title, styles.token]}>{t("Value")}</Text>
    </View>
  )
}

const TokenizationDepositorsView: React.FC<TokenizationDepositorsViewProps> = ({
  request,
}) => {
  const { t } = useTranslation()
  const { id } = request

  const { data } = useQuery({
    queryKey: QueryKeys.ESTATE.DEPOSITORS(id),
    queryFn: () => getDepositorsByEstateRequestId(`${id}`),
    refetchInterval: 10_000,
    refetchOnMount: true,
  })

  const depositors = data || []

  const holders = depositors.map((depositor) => ({
    address: depositor.depositor.address,
    amount: formatMoneyByDecimals(
      depositor.depositAmount,
      depositor.request.decimals
    ),
    currency: depositor.request.currency,
    tokenAmount: formatMoneyByDecimals(
      depositor.tokenAmount,
      depositor.request.decimals
    ),
  }))
  if (holders.length === 0) return null
  return (
    <CollapseWithHeaderView
      title={t("Depositors")}
      showEmpty={!depositors || depositors.length == 0}
      emptyTitle={t("No depositors")}
      headerIconUri={usersIcon}
    >
      <TokenizationDepositorsLabelsView />
      <ListHoldNFTView holders={holders} />
    </CollapseWithHeaderView>
  )
}

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: 16,
    marginBottom: 8,
    flexDirection: "row",
  },
  title: {
    ...textStyles.labelM,
    color: Colors.black7,
  },
  holder: {
    flex: 1,
  },
  deposit: {
    flex: 1,
  },
  token: {
    textAlign: "center",
    flex: 1,
  },
})

export { TokenizationDepositorsView }
