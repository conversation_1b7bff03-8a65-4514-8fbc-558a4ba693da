import React, { useState } from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { AreaUnitView, CardView } from "components"
import { textStyles, viewStyles } from "src/config/styles"
import { Divider } from "react-native-paper"
import { SectionHeader } from "./SectionHeader"
import { EstateTokenAreaUnit, EstateTokenAttribute } from "src/api"
import { useTranslation } from "react-i18next"
import { EstateTokenTraitsView } from "./EstateTokenTraitsView"
import { DetailsView } from "./DetailsView"
import infoIcon from "assets/images/ic_info.png"
import attributeIcon from "assets/images/ic_attribute.png"
import detailIcon from "assets/images/ic_detail.png"
import { textColors } from "src/config/colors"

interface TokenizationDescriptionViewProps {
  address: string
  description: string
  attributes: EstateTokenAttribute[]
  requestId: string
  uri: string
  area: number
  areaUnit: EstateTokenAreaUnit
}

const TokenizationDescriptionView: React.FC<
  TokenizationDescriptionViewProps
> = ({ address, attributes, description, requestId, uri, area, areaUnit }) => {
  const { t } = useTranslation()
  const [showInformation, setsShowInformation] = useState(false)
  const [showTraits, setShowTraits] = useState(false)
  const [showDetails, setShowDetails] = useState(false)

  return (
    <CardView style={styles.card}>
      <View>
        <SectionHeader
          icon={<Image source={infoIcon} style={viewStyles.icon} />}
          title={t("Information")}
          isOpen={showInformation}
          onPress={() => setsShowInformation(!showInformation)}
        />
        <Divider />
        {showInformation && (
          <View style={styles.information}>
            <Text style={[styles.label, { marginBottom: 4 }]}>
              {t("Address")}: <Text style={styles.value}>{address}</Text>
            </Text>
            <View style={[styles.row, { marginBottom: 4 }]}>
              <Text style={styles.label}>{`${t("Area")}: `}</Text>
              <AreaUnitView area={area} areaUnit={areaUnit} />
            </View>
            {description && (
              <Text style={styles.label}>
                {t("Description")}:{" "}
                <Text style={styles.value}>{description}</Text>
              </Text>
            )}
            <Divider style={styles.marginTopDefault} />
          </View>
        )}

        <SectionHeader
          icon={<Image source={attributeIcon} style={viewStyles.icon} />}
          title={t("Traits")}
          isOpen={showTraits}
          onPress={() => setShowTraits(!showTraits)}
        />
        <Divider />
        {showTraits && (
          <>
            <EstateTokenTraitsView estateTokenTraits={attributes} />
            <Divider />
          </>
        )}

        <SectionHeader
          icon={<Image source={detailIcon} style={viewStyles.icon} />}
          title={t("Details")}
          isOpen={showDetails}
          onPress={() => setShowDetails(!showDetails)}
        />
        <Divider />
        {showDetails && (
          <DetailsView
            requestId={requestId}
            uri={uri}
            area={area}
            areaUnit={areaUnit}
          />
        )}
      </View>
    </CardView>
  )
}

export { TokenizationDescriptionView }

const styles = StyleSheet.create({
  card: {
    marginTop: 16,
  },
  information: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  description: {
    ...textStyles.bodyM,
  },
  areaUnit: {
    marginBottom: 4,
    fontSize: 8,
  },
  label: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
  },
  value: {
    ...textStyles.bodyM,
    color: textColors.textBlack11,
  },
  marginTopDefault: { marginTop: 16 },
})
