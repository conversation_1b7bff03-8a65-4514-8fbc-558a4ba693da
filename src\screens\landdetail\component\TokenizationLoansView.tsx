import React, { useContext } from "react"
import { StyleSheet, View } from "react-native"
import { getMortgageLoans, MortgageTokenLoan } from "src/api"
import { MortgageNFTButton } from "screens/landdetail/component/MortgageNFTButton"
import { useTranslation } from "react-i18next"
import { LoanItemView } from "../component/LoanItemView"
import EstateDetailContext from "../context/EstateContext"
import { CollapseWithHeaderView } from "../component/CollapseWithHeaderView"
import { useQuery } from "@tanstack/react-query"
import QueryKeys from "src/config/queryKeys"
import { EmptyView } from "components/common/EmptyView"
import mortgageIcon from "assets/images/ic_mortgage.png"

const TokenizationLoansView: React.FC = () => {
  const { t } = useTranslation()
  const { estateDetail } = useContext(EstateDetailContext)

  const { data: loans = [] } = useQuery({
    queryKey: QueryKeys.MORTGAGE.LOANS(estateDetail?.id),
    queryFn: () => getMortgageLoans(estateDetail?.id),
    refetchOnMount: true,
    refetchIntervalInBackground: true,
    refetchInterval: 10_000,
  })

  return (
    <View style={{ position: "relative" }}>
      <CollapseWithHeaderView
        title={t("Loans")}
        emptyTitle={t("No loans")}
        headerIconUri={mortgageIcon}
        showEmpty={false}
      >
        <View style={styles.container}>
          {estateDetail && (
            <MortgageNFTButton estate={estateDetail} width={"100%"} />
          )}
          {loans.length > 0 ? (
            <LoansList loans={loans} />
          ) : (
            <EmptyView style={{ marginBottom: 16 }} />
          )}
        </View>
      </CollapseWithHeaderView>
    </View>
  )
}

const LoansList: React.FC<{
  loans: MortgageTokenLoan[]
}> = ({ loans }) => {
  return (
    <View style={styles.listContainer}>
      {loans.map((loan) => (
        <LoanItemView key={loan.id} loan={loan} />
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingHorizontal: 12,
  },
  listContainer: {
    width: "100%",
  },
})

export { TokenizationLoansView }
