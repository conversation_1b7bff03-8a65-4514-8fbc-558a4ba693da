import React, { useContext } from "react"
import { StyleSheet, View } from "react-native"
import { getMarketPlaceOffers, MarketplaceOffer } from "src/api"
import { NewOfferButton } from "screens/landdetail/component/NewOfferButton"
import { useTranslation } from "react-i18next"
import EstateDetailContext from "../context/EstateContext"
import { OfferItemView } from "../component/OfferItemView"
import { CollapseWithHeaderView } from "../component/CollapseWithHeaderView"
import { useQuery } from "@tanstack/react-query"
import QueryKeys from "src/config/queryKeys"
import { EmptyView } from "components/common/EmptyView"
import menuIcon from "assets/images/ic_menu.png"

const TokenizationOffersView: React.FC = () => {
  const { t } = useTranslation()
  const { estateDetail } = useContext(EstateDetailContext)

  const { data: offers = [] } = useQuery({
    queryKey: QueryKeys.MARKETPLACE.OFFERS(estateDetail?.id),
    queryFn: () => getMarketPlaceOffers(estateDetail?.id),
    refetchOnMount: true,
    refetchIntervalInBackground: true,
    refetchInterval: 10_000,
  })

  return (
    <CollapseWithHeaderView
      title={t("Offers")}
      showEmpty={false}
      headerIconUri={menuIcon}
      emptyTitle={t("No offers")}
    >
      <View style={styles.container}>
        {estateDetail && (
          <NewOfferButton
            estate={estateDetail}
            width={"100%"}
            style={{ marginBottom: 8 }}
          />
        )}
        <ListOffers offers={offers} />
      </View>
    </CollapseWithHeaderView>
  )
}

const ListOffers: React.FC<{
  offers: MarketplaceOffer[]
}> = ({ offers }) => {
  if (offers.length === 0) {
    return <EmptyView style={{ marginBottom: 16 }} />
  }
  return (
    <View style={styles.listContainer}>
      {offers.map((offer) => (
        <OfferItemView key={offer.id} offer={offer} />
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingHorizontal: 12,
  },
  listContainer: {
    width: "100%",
  },
})

export { TokenizationOffersView }
