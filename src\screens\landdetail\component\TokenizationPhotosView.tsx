import React, { useState } from "react"
import { Image, Pressable, ScrollView, StyleSheet, View } from "react-native"
import Colors from "src/config/colors"
import backIcon from "assets/images/ic_back.png"
import nextIcon from "assets/images/ic_next.png"
import { CustomPressable } from "components"
import { viewStyles } from "src/config/styles"

const TokenizationPhotosView: React.FC<{
  photos: string[]
}> = ({ photos }) => {
  const [index, setIndex] = useState(0)

  const handlePrevious = () => {
    setIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : photos.length - 1))
  }

  const handleNext = () => {
    setIndex((prevIndex) => (prevIndex < photos.length - 1 ? prevIndex + 1 : 0))
  }

  return (
    <View>
      <View style={styles.mainPhotoContainer}>
        <Image
          source={{ uri: photos[index] }}
          style={styles.mainPhoto}
          resizeMode="contain"
        />
        <View style={styles.navigationContainer}>
          <CustomPressable style={styles.navButton} onPress={handlePrevious}>
            <Image source={backIcon} style={viewStyles.icon} />
          </CustomPressable>
          <CustomPressable style={styles.navButton} onPress={handleNext}>
            <Image source={nextIcon} style={viewStyles.icon} />
          </CustomPressable>
        </View>
      </View>
      <ScrollView
        horizontal={true}
        contentContainerStyle={styles.scrollViewContent}
      >
        <View style={styles.thumbnailContainer}>
          {photos.map((photo, index) => (
            <Pressable onPress={() => setIndex(index)} key={index}>
              <Image source={{ uri: photo }} style={styles.thumbnail} />
            </Pressable>
          ))}
        </View>
      </ScrollView>
    </View>
  )
}

export { TokenizationPhotosView }

const styles = StyleSheet.create({
  mainPhotoContainer: {
    borderRadius: 12,
    overflow: "hidden",
  },
  mainPhoto: {
    width: "100%",
    aspectRatio: 2,
    borderRadius: 8,
    backgroundColor: Colors.surfaceNormal,
  },
  scrollViewContent: {
    flexGrow: 1,
    justifyContent: "center",
  },
  thumbnailContainer: {
    flexDirection: "row",
    marginTop: 10,
  },
  thumbnail: {
    width: 60,
    height: 40,
    marginEnd: 4,
    borderRadius: 6,
  },
  navigationContainer: {
    position: "absolute",
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    top: "50%",
    transform: [{ translateY: -20 }],
    paddingHorizontal: 16,
  },
  navButton: {
    width: 40,
    height: 40,
    backgroundColor: Colors.opacityWhite60,
    borderRadius: 4,
    justifyContent: "center",
    alignItems: "center",
  },
})
