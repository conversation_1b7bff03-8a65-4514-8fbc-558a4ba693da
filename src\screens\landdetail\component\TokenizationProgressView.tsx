import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { CardView, EstateState, StepView } from "src/components"

interface TokenizationProgressViewProps {
  status: string
}

const TokenizationProgressView: React.FC<TokenizationProgressViewProps> = ({
  status,
}) => {
  const { t } = useTranslation()
  const progresses = [
    t("Validate"),
    t("Public Sale"),
    t("Transferring ownership"),
    t("Finish"),
  ]
  let currentStep = -1
  // TODO, wait confirm status
  switch (status) {
    case "VALIDATE":
      currentStep = 0
      break
    case "SELLING":
      currentStep = 1
      break
    case "TRANSFERRING_OWNERSHIP":
      currentStep = 2
      break
  }

  return currentStep < 0 ? (
    <EstateState state={status} />
  ) : (
    <CardView>
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={textStyles.bodyM}>{t("Tokenization Progress")}</Text>
          <EstateState state={status} color={Colors.blueLink} />
        </View>
        <StepView
          progress={progresses}
          currentStep={currentStep}
          style={styles.stepView}
        />
      </View>
    </CardView>
  )
}

const styles = StyleSheet.create({
  progressContainer: {
    padding: 12,
    marginVertical: 12,
  },
  progressHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  stepView: {
    marginTop: 12,
  },
})

export { TokenizationProgressView }
