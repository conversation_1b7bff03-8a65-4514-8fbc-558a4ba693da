import React from "react"
import { Image, StyleSheet, Text, View, ViewStyle } from "react-native"
import Colors, { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import {
  fixedPointMultiply,
  formatCurrency,
  formatNumericByDecimals,
} from "utils"
import { useTranslation } from "react-i18next"
import { TokenizationRequest } from "src/api"
import usdtIcon from "assets/images/ic_usdt.png"

interface TokenizationRequestValueViewProps {
  style?: ViewStyle
  tokenizationRequest: TokenizationRequest
}

const TokenizationRequestValueView: React.FC<
  TokenizationRequestValueViewProps
> = ({ style = styles.container, tokenizationRequest }) => {
  const { t } = useTranslation()

  const {
    decimals = 18,
    maxSellingAmount = 0,
    unitPrice = 0,
    totalSupply = 0,
    minSellingAmount = 0,
  } = tokenizationRequest

  const unitPriceFormatted = formatNumericByDecimals(
    unitPrice.toString(),
    decimals
  )
  const totalSupplyFormatted = formatNumericByDecimals(
    totalSupply.toString(),
    decimals
  )
  const maxSellingAmountFormatted = formatNumericByDecimals(
    maxSellingAmount.toString(),
    decimals
  )
  const minSellingAmountFormatted = formatNumericByDecimals(
    minSellingAmount.toString(),
    decimals
  )

  const totalValue = fixedPointMultiply(
    BigInt(unitPrice),
    BigInt(totalSupply),
    decimals
  )
  const totalValueFormatted = formatNumericByDecimals(
    totalValue.toString(),
    decimals
  )

  return (
    <View style={style}>
      <View style={styles.leftColumn}>
        <Text style={styles.label}>{t("Property value")}:</Text>
        <View style={styles.valueRow}>
          <Text style={styles.primaryValue}>
            {formatCurrency(totalValueFormatted)}
          </Text>
          <Image source={usdtIcon} style={viewStyles.tinyIcon} />
        </View>
        <Text
          style={styles.firstValue}
        >{`${unitPriceFormatted} ${t("USDT")}/${t("Unit")}`}</Text>
        <View style={{ justifyContent: "flex-end", flex: 1 }}>
          <Text style={styles.label}>{t("Real value of property")}</Text>
          <View style={styles.valueRow}>
            <Text style={styles.secondaryValue}>
              ~
              {formatCurrency(
                (BigInt(+totalValueFormatted) * BigInt(25000)).toString()
              )}
            </Text>
            <Text style={styles.currency}>{t("VND")}</Text>
          </View>
        </View>
      </View>
      <View style={styles.divider} />
      <View style={styles.rightColumn}>
        <Text style={styles.label}>{t("Minimum")}</Text>
        <View style={styles.secondRow}>
          <Text style={styles.secondaryValue}>
            {formatCurrency(minSellingAmountFormatted)}
          </Text>
          <Text style={styles.unit}>{t("NFT")}</Text>
        </View>
        <View style={styles.innerDivider} />
        <Text style={styles.label}>{t("Maximum")}</Text>
        <View style={styles.secondRow}>
          <Text style={styles.secondaryValue}>
            {formatCurrency(maxSellingAmountFormatted)}
          </Text>
          <Text style={styles.unit}>{t("NFT")}</Text>
        </View>
        <View style={styles.innerDivider} />
        <Text style={styles.label}>{t("Total supply")}</Text>
        <View style={styles.secondRow}>
          <Text style={styles.secondaryValue}>
            {formatCurrency(totalSupplyFormatted)}
          </Text>
          <Text style={styles.unit}>{t("NFT")}</Text>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    padding: 16,
    borderRadius: 8,
    backgroundColor: Colors.black3,
    flexDirection: "row",
  },
  leftColumn: {
    flex: 3,
  },
  rightColumn: {
    flex: 2,
    marginStart: 12,
  },
  label: {
    ...textStyles.labelM,
    color: Colors.black7,
    marginBottom: 4,
  },
  firstValue: {
    ...textStyles.labelM,
    color: textColors.textBlack,
  },
  primaryValue: {
    ...textStyles.titleL,
    color: Colors.blueLink,
  },
  secondaryValue: {
    ...textStyles.labelL,
    color: textColors.textBlack,
  },
  valueRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginEnd: 8,
  },
  divider: {
    width: 1,
    backgroundColor: Colors.black5,
  },
  secondRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    flex: 1,
    alignItems: "center",
  },
  innerDivider: {
    height: 1,
    backgroundColor: Colors.black5,
    width: "100%",
    marginVertical: 8,
  },
  unit: {
    ...textStyles.labelL,
    color: Colors.black7,
  },
  currency: {
    ...textStyles.labelL,
    color: Colors.black7,
  },
})

export { TokenizationRequestValueView }
