import React, { useState } from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import { CardView, GradientButton, SimpleLoadingView } from "components"
import { textColors } from "src/config/colors"
import { textStyles } from "src/config/styles"
import { formatCurrency } from "utils"
import { useTranslation } from "react-i18next"
import { CONTRACT_ADDRESS_ESTATE_TOKEN } from "src/config/env"
import {
  collectionAbi,
  useCollectionDepositedAmount,
  useCollectionHasWithdrawn,
} from "src/api/contracts"
import { ethers } from "ethers"
import { useAccount, useWriteContract } from "wagmi"
import { useEthersProvider } from "hooks"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "TokenizationWithdrawNftView" })

interface TokenizationWithdrawNftViewProps {
  style?: ViewStyle
  requestId: string
}

const TokenizationWithdrawNftView: React.FC<
  TokenizationWithdrawNftViewProps
> = ({ style, requestId }) => {
  const { t } = useTranslation()
  const ethersProvider = useEthersProvider()
  const [isLoading, setIsLoading] = useState(false)
  const [canCancelLoading, setCanCancelLoading] = useState(true)
  const { writeContractAsync } = useWriteContract()
  const { address } = useAccount()
  const userHasWithdrawn = useCollectionHasWithdrawn(
    requestId,
    address as `0x${string}`
  )
  const userDepositedAmount = useCollectionDepositedAmount(
    requestId,
    address as `0x${string}`
  )

  const handleWithdrawNft = async () => {
    if (!ethersProvider) return
    try {
      setIsLoading(true)
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_ESTATE_TOKEN,
        abi: collectionAbi,
        functionName: "withdrawToken",
        args: [BigInt(requestId)],
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)

      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Withdraw NFT success") +
            ". " +
            t("Data will be updated in few seconds")
        )
      } else {
        showError(t("Withdraw NFT fail"))
      }
    } catch (e: any) {
      logger.error("Withdraw NFT failed", e)
      showError(t("Withdraw NFT failed"))
    } finally {
      setIsLoading(false)
    }
  }

  if (!address) {
    return null
  }

  return (
    <>
      <CardView style={style}>
        <View style={styles.container}>
          <View style={styles.infoRow}>
            <Text style={styles.infoText}>{t("You have deposited")}</Text>
            <Text style={styles.infoText}>
              {formatCurrency(ethers.utils.formatEther(userDepositedAmount))}
            </Text>
          </View>
          <View style={styles.marginTop}>
            {userHasWithdrawn ? (
              <Text>{t("You have already withdrawn this NFT")}</Text>
            ) : (
              <Text style={styles.normalText}>
                {t("Withdrawable amount")}:{" "}
                {userHasWithdrawn ? 0 : userDepositedAmount.toString()} tokens
              </Text>
            )}
          </View>
          <GradientButton
            onPress={handleWithdrawNft}
            enabled={!isLoading && !userHasWithdrawn}
            borderRadius={8}
            style={styles.marginTop}
            title={t("Withdraw NFT")}
          />
        </View>
      </CardView>
      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </>
  )
}

export { TokenizationWithdrawNftView }

const styles = StyleSheet.create({
  container: {
    padding: 12,
  },
  title: {
    ...textStyles.titleL,
    color: textColors.textGray600,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8,
  },
  infoText: {
    ...textStyles.labelL,
    color: textColors.textGray500,
  },
  normalText: {
    ...textStyles.bodyM,
  },
  marginTop: {
    marginTop: 8,
  },
})
