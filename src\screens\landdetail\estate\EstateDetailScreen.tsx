import React from "react"
import { StyleSheet, View } from "react-native"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { RouteProp } from "@react-navigation/native"
import { RootStackParamList } from "navigation"
import EstateDetailContext from "../context/EstateContext"
import { Background, EmptyView, LoadingView, TopBar } from "components"
import { EstateDetailView } from "./EstateDetailView"
import { useTranslation } from "react-i18next"
import QueryKeys from "src/config/queryKeys"
import { getEstateDetailById } from "src/api"

interface EstateDetailScreenProps {
  route: RouteProp<RootStackParamList, "EstateDetail">
}

const EstateDetailScreen: React.FC<EstateDetailScreenProps> = ({ route }) => {
  const { estateId } = route.params
  const { data: estateDetail, isLoading } = useQuery({
    queryKey: QueryKeys.ESTATE.DETAIL(estateId),
    queryFn: () => getEstateDetailById(estateId),
    refetchInterval: 10_000,
    refetchOnMount: true,
    refetchIntervalInBackground: true,
  })
  const queryClient = useQueryClient()
  const { t } = useTranslation()

  const onRefresh = async () => {
    await Promise.all([
      queryClient.invalidateQueries({
        queryKey: QueryKeys.ESTATE.DETAIL(estateId),
      }),
      queryClient.invalidateQueries({
        queryKey: QueryKeys.ESTATE.BALANCES(estateId),
      }),
      queryClient.invalidateQueries({
        queryKey: QueryKeys.MARKETPLACE.OFFERS(estateId),
      }),
      queryClient.invalidateQueries({
        queryKey: QueryKeys.CURRENCY.LIST,
      }),
      queryClient.invalidateQueries({
        queryKey: QueryKeys.ESTATE.DEPOSITORS(estateId),
      }),
      queryClient.invalidateQueries({
        queryKey: QueryKeys.MORTGAGE.LOANS(estateId),
      }),
    ])
  }

  return (
    <EstateDetailContext.Provider
      value={{
        estateDetail,
        onRefresh,
      }}
    >
      <Background>
        <View style={styles.container}>
          <TopBar enableBack={true} title={t("Estate detail").toUpperCase()} />
          {isLoading ? (
            <LoadingView />
          ) : estateDetail ? (
            <>
              <EstateDetailView />
            </>
          ) : (
            <EmptyView />
          )}
        </View>
      </Background>
    </EstateDetailContext.Provider>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 20,
  },
})

export { EstateDetailScreen }
