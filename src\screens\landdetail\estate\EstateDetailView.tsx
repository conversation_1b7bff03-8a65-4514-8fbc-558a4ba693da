import React, { useContext } from "react"
import { ScrollView, StyleSheet, Text, View } from "react-native"
import EstateDetailContext from "../context/EstateContext"
import { EstateState, RequesterView, TimeView } from "components"
import {
  LegalAspectView,
  TokenizationDescriptionView,
  TokenizationLoansView,
  TokenizationOffersView,
  TokenizationPhotosView,
} from "../component"
import { textStyles } from "src/config/styles"
import { EstateActivities, EstateHolders, EstateTradeInfo } from "./components"
import { useTranslation } from "react-i18next"
import { getFullAddress, TokenizationState } from "src/api/types"

const EstateDetailView: React.FC = () => {
  const { t } = useTranslation()
  const { estateDetail } = useContext(EstateDetailContext)

  if (!estateDetail) return null

  const {
    metadata: {
      metadata: { id: metadataId, name, updated_at, locale_detail, address },
    },
    tokenMintEventTxHash,
    createAtInSeconds,
    tokenizationRequest: { requester },
  } = estateDetail

  return (
    <ScrollView contentContainerStyle={styles.scrollViewContent}>
      <View style={styles.container}>
        <TokenizationPhotosView
          photos={estateDetail.metadata.estatePhotoUrls}
        />
        <Text style={styles.name}>{name}</Text>
        <TimeView
          title={t("Posted at")}
          time={(createAtInSeconds ?? updated_at) * 1000}
        />
        <EstateState
          state={TokenizationState.TOKENIZED}
          style={styles.marginTopDefault}
        />
        <RequesterView requester={requester} />
        <LegalAspectView
          metadataId={metadataId}
          tokenMintEventTxHash={tokenMintEventTxHash}
        />
        <EstateTradeInfo estate={estateDetail} />
        <TokenizationDescriptionView
          address={getFullAddress(address, locale_detail)}
          description={estateDetail.metadata.metadata.description}
          attributes={estateDetail.metadata.metadata.attributes}
          requestId={estateDetail.tokenizationRequest.id}
          uri={estateDetail.uri}
          area={estateDetail.metadata.metadata.area.area}
          areaUnit={estateDetail.metadata.metadata.area.unit}
        />
        <TokenizationLoansView />
        <TokenizationOffersView />
        <EstateHolders estate={estateDetail} />
        <EstateActivities estate={estateDetail} />
        <View style={{ height: 80 }} />
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 12,
  },
  marginTopDefault: {
    marginTop: 8,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 50,
  },
  name: {
    ...textStyles.titleL,
    marginTop: 12,
  },
})

export { EstateDetailView }
