import React from "react"
import { useTranslation } from "react-i18next"
import { formatNumericByDecimalsDisplay } from "src/utils"
import { Estate, EstateActivity, EstateActivityCategory } from "src/api/types"
import { useQuery } from "@tanstack/react-query"
import { getEstateActivities } from "src/api"
import { getElapsedTime } from "utils/timeExt"
import { Image, StyleSheet, Text, View } from "react-native"
import { CollapseWithHeaderView } from "screens/landdetail/component"
import { QueryKeys } from "src/config/queryKeys"
import Colors, { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { AddressView } from "components"
import swapIcon from "assets/images/ic_swap.png"
import saleIcon from "assets/images/ic_sale.png"
import transferIcon from "assets/images/ic_transfer.png"
import mortgageIcon from "assets/images/ic_mortgage.png"
import retriveIcon from "assets/images/ic_retrieve.png"
import foreClosedIcon from "assets/images/ic_fore_closed.png"

type ItemViewProps = {
  label: string
  valueView: React.ReactNode
}

const ItemView: React.FC<ItemViewProps> = ({ label, valueView }) => {
  return (
    <View style={styles.rowSpaceBetween}>
      <Text style={styles.title}>{label}: </Text>
      {valueView}
    </View>
  )
}

type ActivityItemProps = {
  activity: EstateActivity
  decimals: number
}

const ActivityItem: React.FC<ActivityItemProps> = ({ activity, decimals }) => {
  const { t } = useTranslation()

  const activityVisualElementsMap = {
    [EstateActivityCategory.SALE]: { icon: saleIcon, title: t("Sale") },
    [EstateActivityCategory.TRANSFER]: {
      icon: transferIcon,
      title: t("Transfer"),
    },
    [EstateActivityCategory.MORTGAGE]: {
      icon: mortgageIcon,
      title: t("Mortgage"),
    },
    [EstateActivityCategory.RETRIEVE]: {
      icon: retriveIcon,
      title: t("Retrieve"),
    },
    [EstateActivityCategory.FORECLOSED]: {
      icon: foreClosedIcon,
      title: t("Foreclosed"),
    },
  }

  const activityVisualElements = activityVisualElementsMap[
    activity.category
  ] || {
    icon: swapIcon,
    title: t("Activity"),
  }

  return (
    <View style={styles.itemContainer}>
      <View style={styles.row}>
        <Image source={activityVisualElements.icon} style={viewStyles.icon} />
        <Text style={styles.label}>{activityVisualElements.title}</Text>
      </View>
      <ItemView
        label={t("From")}
        valueView={<AddressView address={activity.from.address} copy={false} />}
      />

      <ItemView
        label={t("To")}
        valueView={<AddressView address={activity.to.address} copy={false} />}
      />
      <ItemView
        label={t("Time")}
        valueView={
          <Text style={textStyles.bodyM}>
            {getElapsedTime(activity.blockTimestamp, t)}
          </Text>
        }
      />
      <ItemView
        label={t("Transaction hash")}
        valueView={<AddressView address={activity.txHash} copy={false} />}
      />
      <ItemView
        label={t("Number of NFTs")}
        valueView={
          <Text style={textStyles.bodyM}>
            {`${formatNumericByDecimalsDisplay(activity.amount, decimals)} ${t("NFT")}`}
          </Text>
        }
      />
    </View>
  )
}

type EstateTransactionsProps = {
  estate: Estate
}

export const EstateActivities: React.FunctionComponent<
  EstateTransactionsProps
> = ({ estate }) => {
  const { t } = useTranslation()
  const { decimals } = estate

  const { data } = useQuery({
    queryKey: QueryKeys.ESTATE.ACTIVITIES(estate.id),
    queryFn: () => getEstateActivities(`${estate.id}`),
    refetchInterval: 10_000,
  })

  const offers = data?.list || []

  return (
    <CollapseWithHeaderView
      title={t("Activities")}
      showEmpty={offers.length === 0}
      emptyTitle={t("No activities")}
      headerIconUri={swapIcon}
    >
      <View style={styles.container}>
        {offers.map((activity, index) => (
          <ActivityItem key={index} activity={activity} decimals={decimals} />
        ))}
      </View>
    </CollapseWithHeaderView>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    paddingHorizontal: 12,
    marginBottom: 50,
  },
  itemContainer: {
    borderWidth: 1,
    borderColor: Colors.black5,
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  label: {
    ...textStyles.bodyM,
    color: textColors.textBlack,
    marginStart: 4,
  },
  title: {
    ...textStyles.labelL,
    color: Colors.black7,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
})
