import { Estate } from "src/api/types"
import React from "react"
import { formatNumericByDecimalsDisplay, shortenAddress } from "src/utils"
import { useQuery } from "@tanstack/react-query"
import { getEstateBalances } from "src/api"
import icMedal1 from "assets/images/ic_medal1.png"
import icMedal2 from "assets/images/ic_medal2.png"
import icMedal3 from "assets/images/ic_medal3.png"
import { Image, Linking, StyleSheet, Text, View } from "react-native"
import { CustomPressable } from "components"
import { CollapseWithHeaderView } from "screens/landdetail/component"
import { useTranslation } from "react-i18next"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { QueryKeys } from "src/config/queryKeys"
import usersIcon from "assets/images/ic_users.png"

type EstateHoldersProps = {
  estate: Estate
}

const medals = [icMedal1, icMedal2, icMedal3]

export const EstateHolders: React.FunctionComponent<EstateHoldersProps> = ({
  estate,
}) => {
  const { t } = useTranslation()
  const { data: holders = [] } = useQuery({
    queryKey: QueryKeys.ESTATE.BALANCES(estate.id),
    queryFn: () => getEstateBalances(estate.id),
    refetchInterval: 10_000,
    refetchOnMount: true,
    refetchIntervalInBackground: true,
  })

  return (
    <CollapseWithHeaderView
      title={t("Holders")}
      showEmpty={false}
      headerIconUri={usersIcon}
      emptyTitle={t("No holders")}
    >
      <View>
        {holders
          .sort((a, b) => Number(b.value) - Number(a.value))
          .filter((h) => Number(h.value) > 0)
          .map((holder, index) => (
            <View key={holder.account.address} style={styles.holderRow}>
              <View style={styles.leftSection}>
                <Text
                  style={[
                    textStyles.labelM,
                    { color: Colors.black7, width: 20, marginRight: 4 },
                  ]}
                >
                  {index + 1}
                </Text>
                <CustomPressable
                  onPress={() =>
                    Linking.openURL(
                      `https://bscscan.com/address/${holder.account.address}`
                    )
                  }
                >
                  <Text style={[textStyles.bodyM, { color: Colors.blueLink }]}>
                    {shortenAddress(holder.account.address)}
                  </Text>
                </CustomPressable>
              </View>
              <Text style={textStyles.labelM}>
                {formatNumericByDecimalsDisplay(holder.value, estate.decimals)}{" "}
                {t("NFT")}
              </Text>
              {index < 3 && (
                <Image source={medals[index]} style={styles.medalIcon} />
              )}
            </View>
          ))}
      </View>
    </CollapseWithHeaderView>
  )
}

const styles = StyleSheet.create({
  holderRow: {
    flexDirection: "row",
    alignItems: "center",
    padding: 10,
    width: "100%",
  },
  leftSection: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  medalIcon: {
    width: 32,
    height: 32,
    marginLeft: 8,
  },
})
