import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { Estate } from "src/api"
import { WithdrawNFTView } from "./WithdrawNFTView"
import { useTranslation } from "react-i18next"
import { formatMoney, formatNumericByDecimals } from "utils"
import { CardView, MoneyWithCurrency } from "components"
import { CurrencySymbol } from "components/estates/CurrencySymbol"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { format } from "date-fns"

interface EstateTradeInfoProps {
  estate: Estate
}

export const EstateTradeInfo: React.FC<EstateTradeInfoProps> = ({ estate }) => {
  const { t } = useTranslation()
  const {
    decimals,
    totalSupply,
    tokenizationRequest: { unitPrice, currency, publicSaleEndsAtInSeconds },
  } = estate
  const unitPriceFormatted = formatNumericByDecimals(unitPrice, decimals)
  const valueFormatted = BigInt(+totalSupply) * BigInt(+unitPriceFormatted)
  return (
    <View style={{ marginTop: 12 }}>
      <CardView style={{ marginBottom: 12 }}>
        <View style={styles.container}>
          <View style={{ flex: 1 }}>
            <Text style={styles.labelText}>{t("Property value")}:</Text>
            <MoneyWithCurrency
              style={{ paddingEnd: 8 }}
              amount={valueFormatted.toString()}
              decimals={decimals}
            />
            <Text style={textStyles.labelM}>
              {unitPriceFormatted}{" "}
              <CurrencySymbol currency={currency} shouldShowIcon={false} />/
              {t("Unit")}
            </Text>
          </View>
          <View style={styles.separator} />
          <View style={styles.rightContainer}>
            <View>
              <Text style={styles.labelText}>{t("Total supply")}</Text>
              <View style={styles.rowSpaceBetween}>
                <Text style={textStyles.labelM}>
                  {formatMoney(
                    formatNumericByDecimals(totalSupply, decimals)
                  )}{" "}
                </Text>
                <Text style={styles.nftText}>{t("NFT")}</Text>
              </View>
            </View>
          </View>
        </View>
      </CardView>
      <View style={styles.tokenizedContainer}>
        <Text style={styles.tokenizedText}>
          {t("Tokenized successfully from")}{" "}
          {format(
            new Date(publicSaleEndsAtInSeconds * 1000),
            "dd/MM/yyyy HH:mm"
          )}
        </Text>
      </View>
      <CardView>
        <WithdrawNFTView request={estate.tokenizationRequest} />
      </CardView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: "white",
    borderRadius: 8,
    padding: 12,
  },
  labelText: {
    ...textStyles.labelM,
    color: Colors.black7,
  },
  nftText: {
    ...textStyles.labelL,
    color: Colors.black7,
  },
  rightContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingStart: 8,
    flex: 1,
  },
  separator: {
    width: 1,
    backgroundColor: Colors.black5,
  },
  rowSpaceBetween: {
    justifyContent: "space-between",
    flexDirection: "row",
    width: "100%",
  },
  tokenizedContainer: {
    backgroundColor: Colors.secondaryLight,
    padding: 12,
    borderRadius: 8,
    height: 44,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  tokenizedText: {
    ...textStyles.bodyM,
  },
})
