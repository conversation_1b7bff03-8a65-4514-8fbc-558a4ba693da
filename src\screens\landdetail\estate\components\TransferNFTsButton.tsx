import { PrimaryButton } from "components"
import React, { useState } from "react"
import { TransferNFTsModal } from "./TransferNFTsModal"

interface TransferNFTsButtonProps {
  estateId: string
}

export const TransferNFTsButton: React.FC<TransferNFTsButtonProps> = ({
  estateId,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <PrimaryButton title="Transfer NFTs" onPress={() => setIsOpen(true)} />
      <TransferNFTsModal
        estateId={estateId}
        visible={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  )
}
