import React, { useState } from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import { ethers } from "ethers"
import { InputField, PrimaryButton } from "components"
import { formatNumber } from "utils/numberExt"
import {
  estateTokenAbi,
  useEstateTokenBalance,
  useEstateTokenData,
} from "src/api/contracts"
import { useEthersProvider } from "hooks"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import { textStyles } from "src/config/styles"
import { CONTRACT_ADDRESS_ESTATE_TOKEN } from "src/config/env"
import { queryClient } from "src/api/query"
import { BaseModal } from "components/common/BaseModal"
import { z } from "zod"
import { Controller, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { isValidWalletAddress } from "utils"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "TransferNFTsModal" })

interface Props {
  estateId: string
  visible: boolean
  onClose: () => void
}

export const TransferNFTsModal: React.FC<Props> = ({
  estateId,
  visible,
  onClose,
}) => {
  const { t } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)

  const { address } = useAccount()
  const { value: nftBalance, queryKey } = useEstateTokenBalance(
    address as `0x${string}`,
    estateId
  )
  const { decimals: estateDecimals } = useEstateTokenData(estateId)
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const formSchema = z.object({
    toAddress: z
      .string()
      .refine((val) => isValidWalletAddress(val), {
        message: t("Invalid wallet address"),
      })
      .refine((val) => val.toLowerCase() !== address?.toLowerCase(), {
        message: t("Cannot transfer to your own address"),
      }),
    amount: z
      .number()
      .min(1, t("Amount must be greater than 0"))
      .refine((val) => nftBalance && val <= Number(nftBalance), {
        message: nftBalance
          ? t("Amount must not exceed your balance")
          : t("Please wait for balance to load"),
      }),
  })

  type FormValues = z.infer<typeof formSchema>

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      toAddress: "",
      amount: 0,
    },
  })

  const closeAndReset = () => {
    onClose()
    form.reset()
  }

  const handleSubmit = async (values: FormValues) => {
    if (isLoading) return
    if (!ethersProvider) return
    setIsLoading(true)

    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_ESTATE_TOKEN,
        abi: estateTokenAbi,
        functionName: "safeTransferFrom",
        args: [
          address,
          values.toAddress as `0x${string}`,
          BigInt(estateId),
          BigInt(+values.amount) * BigInt(Math.pow(10, estateDecimals)),
          ethers.utils.hexlify(
            ethers.utils.toUtf8Bytes(
              "Transfer via Briky Finance Decentralized Application"
            )
          ),
        ],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status !== 1) {
        showError(t("Fail to transfer NFT"))
      } else {
        await queryClient.invalidateQueries({ queryKey })
        showSuccessWhenCallContract(t("Transfer successfully"))
        onClose()
      }
    } catch (error) {
      logger.error("Failed to transfer NFT", error)
      showError(t("Fail to transfer NFT"))
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <BaseModal
      visible={visible}
      onClose={closeAndReset}
      title={t("Transfer NFT")}
      isShowCloseIcon={true}
    >
      <View style={styles.container}>
        <Text style={styles.label}>{t("Address")}</Text>
        <Controller
          control={form.control}
          name="toAddress"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <InputField
              value={value}
              onChangeText={onChange}
              placeholder={t("Input Wallet Address")}
              style={styles.input}
              error={error?.message}
            />
          )}
        />

        <Text style={styles.label}>{t("NFTs")}(18):</Text>
        <Controller
          control={form.control}
          name="amount"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <InputField
              value={value}
              onChangeText={onChange}
              type={"number"}
              inputMode={"decimal"}
              placeholder="0"
              style={styles.input}
              error={error?.message}
            />
          )}
        />

        <View style={styles.balanceRow}>
          <Text style={styles.balanceText}>
            {t("Balance")}: {formatNumber(nftBalance)}
          </Text>
          <PrimaryButton
            title={t("Maximum")}
            onPress={() => form.setValue("amount", Number(nftBalance))}
            style={styles.maxButton}
          />
        </View>

        <PrimaryButton
          title={isLoading ? t("Transferring...") : t("Transfer")}
          onPress={form.handleSubmit(handleSubmit)}
          enabled={!isLoading}
          style={styles.submitButton}
        />
      </View>
    </BaseModal>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    width: "100%",
  },
  label: {
    ...textStyles.labelM,
    marginBottom: 8,
  },
  input: {
    marginBottom: 16,
    width: "100%",
  },
  balanceRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 24,
  },
  balanceText: {
    ...textStyles.bodyM,
  },
  maxButton: {
    paddingHorizontal: 12,
  },
  submitButton: {
    marginTop: 8,
  },
})
