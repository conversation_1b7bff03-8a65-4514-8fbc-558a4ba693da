import { useEthersProvider } from "hooks"
import React, { useState } from "react"
import { Image, StyleSheet, Text, TextStyle, View } from "react-native"
import {
  getDepositorsByEstateRequestId,
  TokenizationRequest,
  TokenizationRequestState,
} from "src/api"
import {
  collectionAbi,
  useCollectionDepositedAmount,
  useCollectionHasWithdrawn,
  useEstateTokenBalance,
} from "src/api/contracts"
import { useAccount, useWriteContract } from "wagmi"
import { CONTRACT_ADDRESS_ESTATE_TOKEN } from "src/config/env"
import { showError, showSuccessWhenCallContract } from "utils/toast"
import { useTranslation } from "react-i18next"
import {
  formatCurrency,
  formatNumericByDecimals,
  formatNumericByDecimalsDisplay,
} from "utils/format"
import { GradientButton, SimpleLoadingView } from "components"
import { textStyles, viewStyles } from "src/config/styles"
import { textColors } from "src/config/colors"
import usdtIcon from "assets/images/ic_usdt.png"
import { useQuery } from "@tanstack/react-query"
import QueryKeys from "src/config/queryKeys"
import { queryClient } from "src/api/query"
import { formatNumber } from "utils/numberExt"
import { TransferNFTsButton } from "./TransferNFTsButton"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "WithdrawNFTView" })

interface LabelValueRowProps {
  label: string
  value?: string
  textStyle?: TextStyle
}

const LabelValueRow = ({ label, value, textStyle }: LabelValueRowProps) => {
  return (
    <View style={styles.row}>
      <Text style={[textStyles.titleM, textStyle]}>{label}</Text>
      {typeof value === "string" ? (
        <Text style={[textStyles.titleM, textStyle]}>{value}</Text>
      ) : (
        value
      )}
    </View>
  )
}

export const WithdrawNFTView = ({
  request,
}: {
  request: TokenizationRequest
}) => {
  const { t } = useTranslation()
  const ethersProvider = useEthersProvider()
  const { unitPrice, id: requestId, state, decimals } = request

  const [isLoading, setIsLoading] = useState(false)
  const [canCancelLoading, setCanCancelLoading] = useState(true)
  const { writeContractAsync } = useWriteContract()
  const { address } = useAccount()
  const { value: nftBalance, queryKey } = useEstateTokenBalance(
    address as `0x${string}`,
    request.estateId
  )

  const { data: depositors = [], refetch } = useQuery({
    queryKey: [...QueryKeys.ESTATE.DEPOSITORS(request.id)],
    queryFn: () => getDepositorsByEstateRequestId(request.id),
  })

  const meDepositor = depositors.find(
    (i) => i.depositor.address.toLowerCase() === address?.toLowerCase()
  )

  const userDepositedAmount = useCollectionDepositedAmount(
    requestId,
    address as `0x${string}`
  )

  const userHasWithdrawn = useCollectionHasWithdrawn(
    requestId,
    address as `0x${string}`
  )

  const handleWithdrawNft = async () => {
    if (!ethersProvider) return
    try {
      setIsLoading(true)

      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_ESTATE_TOKEN,
        abi: collectionAbi,
        functionName: "withdrawToken",
        args: [BigInt(requestId)],
      })
      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Withdraw NFT(s) success") +
            ". " +
            t("Data will be updated in few seconds")
        )
        refetch()
        queryClient.invalidateQueries({ queryKey }).then()
      } else {
        throw new Error(t("Withdraw NFT(s) failed"))
      }
    } catch (e: any) {
      logger.error("Withdraw NFT(s) failed", e)
      showError(e.message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <View>
      {nftBalance && Number(nftBalance) > 0 && (
        <View style={styles.container}>
          <LabelValueRow
            label={`${t("Your NFTs")}:`}
            value={formatNumber(nftBalance)}
            textStyle={{
              ...textStyles.titleM,
              color: textColors.textBlack11,
            }}
          />
          <View style={{ marginTop: 12 }} />
          <TransferNFTsButton estateId={request.estateId} />
        </View>
      )}

      {meDepositor &&
        state == TokenizationRequestState.CONFIRMED &&
        !userHasWithdrawn && (
          <View>
            <View style={styles.container}>
              <Text style={styles.title}>{t("Withdraw NFT")}</Text>
              <View style={styles.infoContainer}>
                <LabelValueRow
                  label={t("Number of NFTs owned")}
                  value={formatCurrency(
                    formatNumericByDecimals(
                      meDepositor?.tokenAmount ?? "0",
                      decimals
                    )
                  )}
                />

                <LabelValueRow
                  label={`${t("Deposited NFT number")}:`}
                  value={formatCurrency(
                    formatNumericByDecimals(
                      meDepositor?.depositAmount ?? "0",
                      decimals
                    )
                  )}
                />

                <View style={styles.row}>
                  <Text
                    style={textStyles.bodyM}
                  >{`${t("Deposit price")}:`}</Text>
                  <View style={styles.priceContainer}>
                    <Text style={textStyles.bodyM}>
                      {formatNumericByDecimalsDisplay(unitPrice, decimals)}
                    </Text>
                    <Image
                      source={usdtIcon}
                      style={[viewStyles.tinyIcon, { marginStart: 4 }]}
                    />
                    <Text style={textStyles.bodyM}>{` /1 ${t("NFT")}`}</Text>
                  </View>
                </View>
              </View>

              <GradientButton
                borderRadius={8}
                title={t("Withdraw NFT")}
                onPress={handleWithdrawNft}
                style={styles.marginTopDefault}
                enabled={
                  !userHasWithdrawn && !isLoading && userDepositedAmount > 0
                }
              />
            </View>
            <SimpleLoadingView
              visible={isLoading}
              onCancel={
                canCancelLoading ? () => setIsLoading(false) : undefined
              }
            />
          </View>
        )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    backgroundColor: "white",
    padding: 12,
  },
  title: {
    ...textStyles.titleS,
    color: textColors.textBlack11,
  },
  marginTopDefault: {
    marginTop: 8,
  },
  infoContainer: {
    marginTop: 12,
  },
  row: {
    flexDirection: "row",
    marginTop: 4,
    justifyContent: "space-between",
  },
  tinyIcon: {
    width: 16,
    height: 16,
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  errorText: {
    ...textStyles.titleS,
    color: "red",
  },
})
