import { useState } from "react"
import { useTranslation } from "react-i18next"
import {
  getLegalRequirements,
  LandRegistryOfficeType,
  LegalRequirement,
  RequirementType,
} from "src/api"
import { useQuery } from "@tanstack/react-query"
import QueryKeys from "src/config/queryKeys"

const ESTATE_RECORD_GROUP = [RequirementType.OWNERSHIP_CERTIFICATE]
const COMPANY_PROFILE_GROUP = [
  RequirementType.COMPANY_IDENTITY_VERIFICATION,
  RequirementType.LEGAL_REPRESENTATIVE_VERIFICATION,
  RequirementType.PARENT_COMPANY_RELATIONSHIP_VERIFICATION,
]
const PROPERTY_OWNER_PROFILE_GROUP = [
  RequirementType.OWNER_IDENTITY_VERIFICATION,
]
const TRANSFER_GROUP = [
  RequirementType.TRANSFER_FROM_OWNER,
  RequirementType.PROPERTY_SEALING,
]
const APPRAISAL_GROUP = [
  RequirementType.LEGAL_ASSESSMENT,
  RequirementType.MARITAL_STATUS,
]
const PRICING_GROUP = [RequirementType.ESTATE_VALUATION]
const PAY_TAX_GROUP = [
  RequirementType.INCOME_TAX,
  RequirementType.VAT,
  RequirementType.REGISTRATION_FEE,
  RequirementType.RESOURCE_TAX,
]
const TRANSFER_COMPLETED_GROUP = [RequirementType.CERTIFICATE_UPDATE]
const DIGITALIZATION_GROUP = [RequirementType.MORTGAGE_STATUS]

const LEGAL_REQUIRE_FILE_GROUP = [
  RequirementType.OWNERSHIP_CERTIFICATE,
  RequirementType.COMPANY_IDENTITY_VERIFICATION,
  RequirementType.LEGAL_REPRESENTATIVE_VERIFICATION,
  RequirementType.OWNER_IDENTITY_VERIFICATION,
  RequirementType.TRANSFER_FROM_OWNER,
  RequirementType.PROPERTY_SEALING,
  RequirementType.LEGAL_ASSESSMENT,
  RequirementType.ESTATE_VALUATION,
  RequirementType.INCOME_TAX,
  RequirementType.VAT,
  RequirementType.REGISTRATION_FEE,
  RequirementType.CERTIFICATE_UPDATE,
  RequirementType.MORTGAGE_STATUS,
]

const initialLegalRequirement = (requirementType: RequirementType) => ({
  requirementType,
  fileUrl: "",
  fileName: "",
  id: 0,
  metadataID: 0,
  issuer: {
    id: 0,
    address: "",
    name: "",
    type: LandRegistryOfficeType.LAWYER,
  },
})

const initialData = [
  initialLegalRequirement(RequirementType.OWNERSHIP_CERTIFICATE),
  initialLegalRequirement(RequirementType.COMPANY_IDENTITY_VERIFICATION),
  initialLegalRequirement(RequirementType.LEGAL_REPRESENTATIVE_VERIFICATION),
  initialLegalRequirement(
    RequirementType.PARENT_COMPANY_RELATIONSHIP_VERIFICATION
  ),
  initialLegalRequirement(RequirementType.OWNER_IDENTITY_VERIFICATION),
  initialLegalRequirement(RequirementType.TRANSFER_FROM_OWNER),
  initialLegalRequirement(RequirementType.PROPERTY_SEALING),
  initialLegalRequirement(RequirementType.LEGAL_ASSESSMENT),
  initialLegalRequirement(RequirementType.MARITAL_STATUS),
  initialLegalRequirement(RequirementType.ESTATE_VALUATION),
  initialLegalRequirement(RequirementType.INCOME_TAX),
  initialLegalRequirement(RequirementType.VAT),
  initialLegalRequirement(RequirementType.REGISTRATION_FEE),
  initialLegalRequirement(RequirementType.RESOURCE_TAX),
  initialLegalRequirement(RequirementType.CERTIFICATE_UPDATE),
  initialLegalRequirement(RequirementType.MORTGAGE_STATUS),
  initialLegalRequirement(RequirementType.TRANSFER_FROM_MORTGAGEE),
  initialLegalRequirement(RequirementType.MORTGAGE_RELEASE),
]

export type LegalRequirementGroup = {
  title: string
  legalRequirements: LegalRequirement[]
}

export type DocumentFile = {
  fileName: string
  fileUrl: string
}

export const useLegalRequirement = (
  metadataId: number,
  tokenMintEventTxHash: string
) => {
  const { t } = useTranslation()

  const [isOpenModal, setIsOpenModal] = useState(false)
  const [showingIssuerName, setShowingIssuerName] = useState<string>("")
  const [showingDocumentFiles, setShowingDocumentFiles] = useState<
    DocumentFile[]
  >([])
  const [selectedLegalRequirement, setSelectedLegalRequirement] = useState<
    LegalRequirement | undefined
  >(undefined)

  const { data } = useQuery({
    queryKey: [QueryKeys.ESTATE.LEGAL_REQUIREMENTS, metadataId.toString()],
    queryFn: () => getLegalRequirements(`${metadataId}`),
  })
  const realLegalRequirements = data?.list || []

  const legalRequirements = initialData.map((initialItem) => {
    const matchingRealItem = realLegalRequirements.find(
      (realItem) => realItem.requirementType === initialItem.requirementType
    )
    if (initialItem.requirementType === RequirementType.MORTGAGE_STATUS) {
      return { ...initialItem, fileUrl: tokenMintEventTxHash }
    }
    return matchingRealItem && matchingRealItem.fileUrl
      ? { ...initialItem, fileUrl: matchingRealItem.fileUrl }
      : initialItem
  })

  const initialLegalRequirementGroup = (
    title: string,
    requirementTypeGroup: RequirementType[]
  ) => ({
    title,
    legalRequirements: legalRequirements.filter((item) =>
      requirementTypeGroup.includes(item.requirementType)
    ),
  })

  const legalRequirementGroups = [
    initialLegalRequirementGroup(
      t("Real Estate Documents"),
      ESTATE_RECORD_GROUP
    ),
    initialLegalRequirementGroup(t("Company Documents"), COMPANY_PROFILE_GROUP),
    initialLegalRequirementGroup(
      t("Property Owner Documents"),
      PROPERTY_OWNER_PROFILE_GROUP
    ),
    initialLegalRequirementGroup(t("Transfer"), TRANSFER_GROUP),
    initialLegalRequirementGroup(t("Legal Evaluation"), APPRAISAL_GROUP),
    initialLegalRequirementGroup(t("Valuation"), PRICING_GROUP),
    initialLegalRequirementGroup(t("Tax Filing"), PAY_TAX_GROUP),
    initialLegalRequirementGroup(
      t("Transfer Completion"),
      TRANSFER_COMPLETED_GROUP
    ),
    initialLegalRequirementGroup(
      t("Digitization Process"),
      DIGITALIZATION_GROUP
    ),
  ]

  const selectLegalRequirement = (requirementType: RequirementType) => {
    const allowShowLoadingModal =
      realLegalRequirements.find(
        (item) => item.requirementType === requirementType
      ) != undefined
    setIsOpenModal(allowShowLoadingModal)
    const issuerName =
      realLegalRequirements.find(
        (item) => item.requirementType === requirementType
      )?.issuer?.name || ""
    setShowingIssuerName(issuerName)
    const documentFiles = realLegalRequirements
      .filter(
        (item) => item.requirementType === requirementType && item.fileUrl
      )
      .map((item) => {
        return { fileName: item.fileName, fileUrl: item.fileUrl }
      })
    setShowingDocumentFiles(documentFiles)
  }

  return {
    showingIssuerName,
    showingDocumentFiles,
    selectLegalRequirement,
    realLegalRequirements,
    legalRequirementGroups,
    isOpenModal,
    setIsOpenModal,
    selectedLegalRequirement,
    setSelectedLegalRequirement,
  }
}

export const getIsEnoughRequireData = (
  legalRequirements: LegalRequirement[]
) => {
  return !legalRequirements.find(
    (item) =>
      LEGAL_REQUIRE_FILE_GROUP.includes(item.requirementType) && !item.fileUrl
  )
}

export const updateDocumentFiles = (
  requirementType: RequirementType,
  legalRequirements: LegalRequirement[]
) => {
  const documentFiles = legalRequirements
    .filter((item) => item.requirementType === requirementType && item.fileUrl)
    .map((item) => {
      return { fileName: item.fileName, fileUrl: item.fileUrl }
    })
  return documentFiles
}
