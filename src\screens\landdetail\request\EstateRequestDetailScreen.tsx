import React from "react"
import { StyleSheet, View } from "react-native"
import { RouteProp } from "@react-navigation/native"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { RootStackParamList } from "src/navigation"
import { Background, EmptyView, LoadingView, TopBar } from "components"
import EstateRequestDetailContext from "../context/EstateRequestContext"
import { EstateRequestDetailView } from "./EstateRequestDetailView"
import QueryKeys from "src/config/queryKeys"
import { useTranslation } from "react-i18next"
import { getEstateRequestDetailById } from "src/api"

interface EstateRequestDetailScreenProps {
  route: RouteProp<RootStackParamList, "EstateRequestDetail">
}

const EstateRequestDetailScreen: React.FC<EstateRequestDetailScreenProps> = ({
  route,
}) => {
  const { estateRequestId } = route.params
  const { t } = useTranslation()
  const { data: tokenizationRequest, isLoading } = useQuery({
    queryKey: QueryKeys.ESTATE.REQUEST_DETAIL(estateRequestId),
    queryFn: () => getEstateRequestDetailById(estateRequestId),
    refetchOnMount: true,
    refetchInterval: 10_000,
  })
  const queryClient = useQueryClient()

  const onRefresh = async () => {
    await Promise.all([
      queryClient.invalidateQueries({
        queryKey: QueryKeys.ESTATE.REQUEST_DETAIL(estateRequestId),
      }),
      queryClient.invalidateQueries({
        queryKey: QueryKeys.ESTATE.DEPOSITORS(estateRequestId),
      }),
    ])
  }

  return (
    <EstateRequestDetailContext.Provider
      value={{
        tokenizationRequest,
        onRefresh,
      }}
    >
      <Background>
        <View style={styles.container}>
          <TopBar enableBack={true} title={t("Estate detail").toUpperCase()} />
          {isLoading ? (
            <LoadingView />
          ) : tokenizationRequest ? (
            <EstateRequestDetailView />
          ) : (
            <EmptyView />
          )}
        </View>
      </Background>
    </EstateRequestDetailContext.Provider>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 20,
  },
})

export default EstateRequestDetailScreen
