import React, { useContext } from "react"
import { <PERSON>rollView, StyleSheet, Text, View } from "react-native"
import EstateRequestDetailContext from "../context/EstateRequestContext"
import { textStyles } from "src/config/styles"
import {
  DepositListView,
  LegalAspectView,
  TokenizationDepositorsView,
  TokenizationDepositView,
  TokenizationDescriptionView,
  TokenizationPhotosView,
  TokenizationRequestValueView,
} from "../component"
import {
  CustomCountDownTimer,
  RequesterView,
  TimeView,
  TokenizationRequestState,
} from "components"
import { useTranslation } from "react-i18next"
import { WithdrawNFTView } from "../estate/components"
import {
  getFullAddress,
  TokenizationRequestState as EstateRequestState,
} from "src/api/types"

const EstateRequestDetailView: React.FC = () => {
  const { t } = useTranslation()
  const { tokenizationRequest } = useContext(EstateRequestDetailContext)

  if (!tokenizationRequest) return null

  const {
    id,
    requester,
    uri,
    tokenMintEventTxHash,
    metadata: {
      estatePhotoUrls,
      metadata: {
        id: metadatId,
        locale_detail,
        attributes,
        description,
        name,
        address,
        area: { area, unit },
        created_at,
      },
    },
    state,
  } = tokenizationRequest

  const currentTimestampInSeconds = Math.floor(Date.now() / 1000)
  const isSelling = state === EstateRequestState.SELLING
  const countDownTime =
    tokenizationRequest.publicSaleEndsAtInSeconds - currentTimestampInSeconds

  return (
    <ScrollView contentContainerStyle={styles.scrollViewContent}>
      <View style={styles.container}>
        <TokenizationPhotosView photos={estatePhotoUrls} />
        <Text style={styles.name}>{name}</Text>
        <TimeView
          title={t("Posted at")}
          time={new Date(created_at).getTime()}
        />
        <TokenizationRequestState state={state} style={{ marginTop: 8 }} />
        <RequesterView requester={requester} />
        <LegalAspectView
          metadataId={metadatId}
          tokenMintEventTxHash={tokenMintEventTxHash}
        />
        <TokenizationRequestValueView
          tokenizationRequest={tokenizationRequest}
        />
        {isSelling && (
          <>
            {countDownTime > 0 && (
              <CustomCountDownTimer
                duration={countDownTime}
                title={t("Remaining open time")}
              />
            )}
            <TokenizationDepositView
              style={{ marginTop: 16 }}
              request={tokenizationRequest}
            />
          </>
        )}
        <WithdrawNFTView request={tokenizationRequest} />
        <TokenizationDescriptionView
          address={getFullAddress(address, locale_detail)}
          description={description}
          attributes={attributes}
          requestId={id}
          uri={uri}
          area={area}
          areaUnit={unit}
        />

        <DepositListView request={tokenizationRequest} />
        <TokenizationDepositorsView request={tokenizationRequest} />
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 12,
  },
  name: {
    ...textStyles.titleL,
    marginTop: 12,
  },
  thumbnail: {
    width: 60,
    height: 40,
    marginEnd: 4,
    borderRadius: 6,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 50,
  },
})

export { EstateRequestDetailView }
