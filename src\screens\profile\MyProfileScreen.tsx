import React from "react"
import { MyProfileView } from "./MyProfileView"
import ProfileContext from "./context/ProfileContext"
import {
  useApplyLoanFilter,
  useApplyOfferFilter,
} from "./hooks/useFetchTokenization"

const MyProfileScreen: React.FC = () => {
  const { offerFilter, setOfferFilter } = useApplyOfferFilter()
  const { loanFilter, setLoanFilter } = useApplyLoanFilter()

  return (
    <ProfileContext.Provider
      value={{
        offerFilter,
        setOfferFilter,
        loanFilter,
        setLoanFilter,
      }}
    >
      <MyProfileView />
    </ProfileContext.Provider>
  )
}

export default MyProfileScreen
