import { Background } from "src/components"
import React, { useCallback } from "react"
import { RefreshControl, ScrollView, View } from "react-native"
import { MyProfileWallet } from "./components/MyProfileWallet"
import { profileAtom } from "src/context/AuthContext"
import MyProfileDetail from "./components/MyProfileDetail"
import { getMyProfile, User } from "src/api"
import { useAccount } from "wagmi"
import { useAtom } from "jotai"
import { useMutation } from "@tanstack/react-query"

export const MyProfileView: React.FC = () => {
  const { address } = useAccount()
  const [, setProfile] = useAtom(profileAtom)

  const mutationGetProfile = useMutation({
    mutationFn: () => {
      return getMyProfile(address)
    },
    onSuccess: (userProfile: User) => {
      setProfile(userProfile)
    },
  })

  const onRefresh = useCallback(() => {
    mutationGetProfile.mutate()
  }, [])

  return (
    <ScrollView
      contentContainerStyle={{
        flexGrow: 1,
      }}
      refreshControl={
        <RefreshControl
          refreshing={mutationGetProfile.isPending}
          onRefresh={onRefresh}
        />
      }
    >
      <Background>
        <View
          style={{
            flex: 1,
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <MyProfileDetail />
          <MyProfileWallet />
        </View>
      </Background>
    </ScrollView>
  )
}
