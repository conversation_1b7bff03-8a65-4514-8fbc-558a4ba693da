import React from "react"
import { getMyActivities } from "src/api"
import { useQuery } from "@tanstack/react-query"
import { MyActivityItemView } from "./MyActivityItemView"
import QueryKeys from "src/config/queryKeys"
import { useAccount } from "wagmi"
import { SimpleListView } from "./SimpleListView"

export const ActivitiesTab: React.FC = () => {
  const { address } = useAccount()
  const { data: activities = [], isLoading } = useQuery({
    queryKey: QueryKeys.PROFILE.MY_ACTIVITIES(address),
    queryFn: () => getMyActivities(address),
  })

  return (
    <SimpleListView
      data={activities}
      isLoading={isLoading}
      renderItem={(activity) => <MyActivityItemView activity={activity} />}
    />
  )
}
