import React, { useState } from "react"
import { LoanFilterModal } from "./LoanFilterModal"
import { FilterView } from "components/FilterView"

const LoanFilterButton: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <FilterView onClickFilter={() => setIsOpen(true)} />
      <LoanFilterModal isOpen={isOpen} setIsOpen={setIsOpen} />
    </>
  )
}

export { LoanFilterButton }
