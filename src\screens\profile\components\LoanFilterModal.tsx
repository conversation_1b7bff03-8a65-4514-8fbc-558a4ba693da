import React, { use<PERSON><PERSON>back, useContext, useEffect } from "react"
import { CustomCheckbox, PrimaryButton } from "components"
import { StyleSheet, View } from "react-native"
import { BaseModal } from "components/common/BaseModal"
import { useTranslation } from "react-i18next"
import Colors, { textColors } from "src/config/colors"
import ProfileContext from "../context/ProfileContext"
import { LoanFilter } from "../types/index"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { Controller, useForm } from "react-hook-form"

interface LoanFilterModalProps {
  isOpen: boolean
  setIsOpen: (value: boolean) => void
}

const LoanFilterModal: React.FC<LoanFilterModalProps> = ({
  isOpen,
  setIsOpen,
}) => {
  const { t } = useTranslation()
  const { loanFilter, setLoanFilter } = useContext(ProfileContext)
  const formSchema = z.object({
    isWaitingHandle: z.boolean(),
    isSupplied: z.boolean(),
    isRefunded: z.boolean(),
    isRecalled: z.boolean(),
    isCancelled: z.boolean(),
    isExpired: z.boolean(),
    isNotExpire: z.boolean(),
  })

  type Payload = z.infer<typeof formSchema>

  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      isWaitingHandle: false,
      isSupplied: false,
      isRefunded: false,
      isRecalled: false,
      isCancelled: false,
      isExpired: false,
      isNotExpire: false,
    },
  })

  useEffect(() => {
    if (isOpen) {
      form.setValue("isWaitingHandle", loanFilter.isWaitingHandle || false)
      form.setValue("isSupplied", loanFilter.isSupplied || false)
      form.setValue("isRefunded", loanFilter.isRefunded || false)
      form.setValue("isRecalled", loanFilter.isRecalled || false)
      form.setValue("isCancelled", loanFilter.isCancelled || false)
      form.setValue("isExpired", loanFilter.isExpired || false)
      form.setValue("isNotExpire", loanFilter.isNotExpire || false)
    }
  }, [isOpen, loanFilter, form])

  const updateSameValueForAllFilterState = (state: boolean) => {
    form.setValue("isWaitingHandle", state)
    form.setValue("isSupplied", state)
    form.setValue("isRefunded", state)
    form.setValue("isRecalled", state)
    form.setValue("isCancelled", state)
    form.setValue("isExpired", state)
    form.setValue("isNotExpire", state)
  }

  const onClose = useCallback(() => {
    setIsOpen(false)
  }, [setIsOpen])

  const onSubmit = (data: Payload) => {
    const newLoanFilter: LoanFilter = {
      isWaitingHandle: data.isWaitingHandle,
      isSupplied: data.isSupplied,
      isRefunded: data.isRefunded,
      isRecalled: data.isRecalled,
      isCancelled: data.isCancelled,
      isExpired: data.isExpired,
      isNotExpire: data.isNotExpire,
    }
    setLoanFilter(newLoanFilter)
    onClose()
  }

  const isWaitingHandle = form.watch("isWaitingHandle")
  const isSupplied = form.watch("isSupplied")
  const isRefunded = form.watch("isRefunded")
  const isRecalled = form.watch("isRecalled")
  const isCancelled = form.watch("isCancelled")
  const isExpired = form.watch("isExpired")
  const isNotExpire = form.watch("isNotExpire")

  const isAll =
    isWaitingHandle &&
    isSupplied &&
    isRefunded &&
    isRecalled &&
    isCancelled &&
    isExpired &&
    isNotExpire

  const handleAllStatesChange = () => {
    updateSameValueForAllFilterState(!isAll)
  }

  return (
    <BaseModal isDisableClose={false} visible={isOpen} onClose={onClose}>
      <View style={styles.container}>
        <CustomCheckbox
          label={t("All")}
          isChecked={isAll}
          onToggle={handleAllStatesChange}
        />
        <View style={styles.divider} />
        <Controller
          control={form.control}
          name="isWaitingHandle"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Waiting handle")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="isSupplied"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Supplied")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="isRefunded"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Refunded")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="isRecalled"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Recalled")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="isCancelled"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Cancelled")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <View style={styles.divider} />
        <Controller
          control={form.control}
          name="isExpired"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Expired")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="isNotExpire"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Not expire")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />

        <View style={styles.divider} />
        <View style={styles.buttonRow}>
          <PrimaryButton
            style={{ flex: 1 }}
            title={t("Cancel")}
            color={Colors.surfaceNormal}
            contentColor={textColors.textBlack11}
            onPress={onClose}
          />
          <PrimaryButton
            style={{ flex: 1, marginStart: 8 }}
            title={t("Apply")}
            onPress={form.handleSubmit(onSubmit)}
          />
        </View>
      </View>
    </BaseModal>
  )
}

export { LoanFilterModal }

const styles = StyleSheet.create({
  container: {
    alignContent: "center",
    width: "100%",
  },
  divider: {
    height: 1,
    backgroundColor: Colors.black4,
    marginVertical: 8,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    height: 40,
    marginTop: 16,
  },
})
