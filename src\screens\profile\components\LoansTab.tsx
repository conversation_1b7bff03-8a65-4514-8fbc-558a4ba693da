import React from "react"
import { getMyMortgageLoans } from "src/api"
import { MyLoanItemView } from "./MyLoanItemView"
import { useQuery } from "@tanstack/react-query"
import { useAccount } from "wagmi"
import QueryKeys from "src/config/queryKeys"
import { SimpleListView } from "./SimpleListView"

const LoansTab: React.FC = () => {
  const { address } = useAccount()
  const { data: loans = [], isLoading } = useQuery({
    queryKey: QueryKeys.PROFILE.MY_LOANS(address),
    queryFn: () => getMyMortgageLoans(address),
    refetchInterval: 10_000,
  })

  return (
    <SimpleListView
      data={loans}
      isLoading={isLoading}
      renderItem={(loan) => <MyLoanItemView loan={loan} />}
    />
  )
}

export { LoansTab }
