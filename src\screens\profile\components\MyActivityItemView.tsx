import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { EstateActivity, EstateActivityCategory } from "src/api/types"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import { AddressView, CustomPressable } from "components"
import { getElapsedTime } from "utils/timeExt"
import { formatCurrency, formatNumericByDecimals } from "utils"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { Router } from "navigation/Router"
import openNewIcon from "assets/images/ic_open_new.png"
import swapIcon from "assets/images/ic_swap.png"
import saleIcon from "assets/images/ic_sale.png"
import transferIcon from "assets/images/ic_transfer.png"
import mortgageIcon from "assets/images/ic_mortgage.png"
import retriveIcon from "assets/images/ic_retrieve.png"
import foreClosedIcon from "assets/images/ic_fore_closed.png"
import { textColors } from "src/config/colors"

interface MyActivityItemViewProps {
  activity: EstateActivity
}

const MyActivityItemView: React.FC<MyActivityItemViewProps> = ({
  activity,
}) => {
  const { t } = useTranslation()
  const {
    amount,
    category,
    to: { address: buyerAddress },
    from: { address: sellerAddress },
    blockTimestamp,
    estate: {
      id,
      decimals,
      metadata: {
        metadata: { name },
        imageUrl,
      },
    },
  } = activity

  const elapsedTime = getElapsedTime(blockTimestamp, t)
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const navigateToEstateDetail = () => {
    navigation.navigate(Router.EstateDetail, { estateId: id })
  }

  const activityVisualElementsMap = {
    [EstateActivityCategory.SALE]: { icon: saleIcon, title: t("Sale") },
    [EstateActivityCategory.TRANSFER]: {
      icon: transferIcon,
      title: t("Transfer"),
    },
    [EstateActivityCategory.MORTGAGE]: {
      icon: mortgageIcon,
      title: t("Mortgage"),
    },
    [EstateActivityCategory.RETRIEVE]: {
      icon: retriveIcon,
      title: t("Retrieve"),
    },
    [EstateActivityCategory.FORECLOSED]: {
      icon: foreClosedIcon,
      title: t("Foreclosed"),
    },
  }

  const activityVisualElements = activityVisualElementsMap[category] || {
    icon: swapIcon,
    title: t("Activity"),
  }

  return (
    <View style={styles.itemContainer}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <View style={styles.titltRow}>
        <Text style={styles.title}>{name}</Text>
        <CustomPressable onPress={navigateToEstateDetail}>
          <Image source={openNewIcon} style={styles.openNew} />
        </CustomPressable>
      </View>
      <View style={styles.row}>
        <Image source={activityVisualElements.icon} style={viewStyles.icon} />
        <Text style={styles.label}>{activityVisualElements.title}</Text>
      </View>
      <View style={styles.rowSpaceBetween}>
        <Text style={textStyles.bodyM}>{`${t("From")}:`}</Text>
        <AddressView address={sellerAddress} />
      </View>
      <View style={styles.rowSpaceBetween}>
        <Text style={textStyles.bodyM}>{`${t("To")}:`}</Text>
        <AddressView address={buyerAddress} />
      </View>
      <Text style={styles.time}>{`${t("Date")}: ${elapsedTime}`}</Text>
      <Text
        style={textStyles.bodyM}
      >{`${t("Numer of NFTs")}: ${formatCurrency(formatNumericByDecimals(amount, decimals))}`}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    paddingBottom: 12,
  },
  image: {
    width: "100%",
    aspectRatio: 16 / 9,
    borderRadius: 8,
    marginBottom: 8,
  },
  title: {
    flex: 1,
    ...textStyles.titleS,
    marginRight: 12,
  },
  time: {
    ...textStyles.bodyM,
    marginBottom: 4,
  },
  marginBottomDefault: {
    marginBottom: 4,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    marginBottom: 4,
    alignItems: "center",
    justifyContent: "space-between",
  },
  titltRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  openNew: {
    width: 24,
    height: 24,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  label: {
    ...textStyles.bodyM,
    color: textColors.textBlack,
    marginStart: 4,
  },
})

export { MyActivityItemView }
