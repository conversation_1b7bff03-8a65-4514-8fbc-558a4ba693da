import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { MyEstate, TokenizationState } from "src/api"
import { useTranslation } from "react-i18next"
import { formatCurrency, formatNumericByDecimals } from "utils"
import { textStyles, viewStyles } from "src/config/styles"
import { CustomPressable, EstateState } from "components"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { Router } from "navigation/Router"
import openNewIcon from "assets/images/ic_open_new.png"

interface MyEstateItemViewProps {
  myEstate: MyEstate
}

const MyEstateItemView: React.FC<MyEstateItemViewProps> = ({ myEstate }) => {
  const { t } = useTranslation()
  const {
    id,
    totalSupply,
    imageUrl,
    initialUnitPrice,
    name,
    balance,
    decimals,
  } = myEstate
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const navigateToEstateDetail = () => {
    navigation.navigate(Router.EstateDetail, { estateId: id })
  }

  return (
    <View style={styles.itemContainer}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <View style={styles.rowSpaceBetween}>
        <EstateState state={TokenizationState.TOKENIZED} />
        <CustomPressable onPress={navigateToEstateDetail}>
          <Image source={openNewIcon} style={viewStyles.icon} />
        </CustomPressable>
      </View>

      <Text style={styles.title}>{name}</Text>
      <Text
        style={styles.amount}
      >{`${t("Market Price")}: ${formatCurrency(formatNumericByDecimals(initialUnitPrice, decimals))}`}</Text>
      <Text
        style={styles.amount}
      >{`${t("Your number of NFTs")}: ${formatCurrency(formatNumericByDecimals(balance, decimals))}`}</Text>
      <Text
        style={styles.amount}
      >{`${t("Total supply")}: ${formatCurrency(formatNumericByDecimals(totalSupply, decimals))} ${t("NFT")}`}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    paddingBottom: 12,
  },
  rowSpaceBetween: {
    alignItems: "center",
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  title: {
    ...textStyles.titleS,
    marginBottom: 8,
  },
  image: {
    width: "100%",
    aspectRatio: 16 / 9,
    borderRadius: 8,
    marginBottom: 8,
  },
  amount: {
    ...textStyles.bodyM,
    marginBottom: 4,
  },
})

export { MyEstateItemView }
