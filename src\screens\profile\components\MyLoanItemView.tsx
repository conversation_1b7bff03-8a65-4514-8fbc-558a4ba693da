import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { MortgageTokenLoan, MortgageTokenLoanState } from "src/api"
import { useTranslation } from "react-i18next"
import { formatCurrency, formatNumericByDecimals } from "utils"
import { convertDateFromTimeStamp, DateTimeFormat } from "utils/timeExt"
import { textStyles, viewStyles } from "src/config/styles"
import { LoanState } from "components/estates/LoanState"
import { Router } from "navigation/Router"
import { CustomPressable, LoanActionButton } from "components"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import usdtIcon from "assets/images/ic_usdt.png"
import openNewIcon from "assets/images/ic_open_new.png"

interface MyLoanItemViewProps {
  loan: MortgageTokenLoan
}

const MyLoanItemView: React.FC<MyLoanItemViewProps> = ({ loan }) => {
  const { t } = useTranslation()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const {
    state,
    mortgageAmount,
    principal,
    repayment,
    dueInSeconds,
    estate: {
      id,
      decimals,
      metadata: {
        metadata: { name },
        imageUrl,
      },
    },
  } = loan

  const navigateToEstateDetail = () => {
    navigation.navigate(Router.EstateDetail, { estateId: id })
  }

  const overdue =
    loan.state !== MortgageTokenLoanState.PENDING &&
    loan.state !== MortgageTokenLoanState.CANCELLED &&
    new Date().getTime() / 1000 > loan.dueInSeconds

  return (
    <View style={styles.itemContainer}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <View style={styles.rowSpaceBetween}>
        <LoanState state={state} overdue={overdue} />
        <CustomPressable onPress={navigateToEstateDetail}>
          <Image source={openNewIcon} style={viewStyles.icon} />
        </CustomPressable>
      </View>
      <Text style={styles.title}>{name}</Text>
      <Text
        style={textStyles.bodyM}
      >{`${t("Expired at")}: ${convertDateFromTimeStamp(dueInSeconds * 1000, DateTimeFormat.HHMM)}`}</Text>
      <View style={styles.rowSpaceBetween}>
        <Text
          style={textStyles.bodyM}
        >{`${t("NFT Mortgage")}: ${formatCurrency(formatNumericByDecimals(mortgageAmount, decimals))}`}</Text>
        <Image style={viewStyles.tinyIcon} source={usdtIcon} />
      </View>
      <Text
        style={textStyles.bodyM}
      >{`${t("Tokens to borrow")}: ${formatCurrency(formatNumericByDecimals(principal, decimals))}`}</Text>
      <Text
        style={textStyles.bodyM}
      >{`${t("Tokens to repay")}: ${formatCurrency(formatNumericByDecimals(repayment, decimals))}`}</Text>
      <LoanActionButton loan={loan} />
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    paddingBottom: 12,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  title: {
    ...textStyles.titleS,
    marginTop: 4,
    marginBottom: 8,
  },
  image: {
    width: "100%",
    aspectRatio: 16 / 9,
    borderRadius: 8,
    marginBottom: 8,
  },
  status: {
    marginVertical: 8,
    marginTop: 8,
  },
})

export { MyLoanItemView }
