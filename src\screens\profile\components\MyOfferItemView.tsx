import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { MarketplaceOffer } from "src/api"
import { useTranslation } from "react-i18next"
import { formatNumericByDecimals } from "utils"
import { CustomPressable } from "components"
import { OfferActionButton } from "components/estates/OfferActionButton"
import { textStyles, viewStyles } from "src/config/styles"
import { OfferState } from "components/estates/OfferState"
import { Router } from "navigation/Router"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import usdtIcon from "assets/images/ic_usdt.png"
import successIcon from "assets/images/ic_success.png"
import failureIcon from "assets/images/ic_failure.png"
import openNewIcon from "assets/images/ic_open_new.png"

interface MyOfferItemViewProps {
  offer: MarketplaceOffer
}

const MyOfferItemView: React.FC<MyOfferItemViewProps> = ({ offer }) => {
  const { t } = useTranslation()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const {
    state,
    sellingAmount,
    soldAmount,
    unitPrice,
    isDivisible,
    estate: {
      id,
      decimals,
      metadata: {
        metadata: { name },
        imageUrl,
      },
    },
  } = offer

  const navigateToEstateDetail = () => {
    navigation.navigate(Router.EstateDetail, { estateId: id })
  }

  return (
    <View style={styles.itemContainer}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <View style={styles.rowSpaceBetween}>
        <OfferState state={state} />
        <CustomPressable onPress={navigateToEstateDetail}>
          <Image source={openNewIcon} style={viewStyles.icon} />
        </CustomPressable>
      </View>
      <Text style={styles.title}>{name}</Text>
      <View style={styles.rowSpaceBetween}>
        <Text
          style={textStyles.bodyM}
        >{`${t("Asking price of a NFT")}: ${formatNumericByDecimals(unitPrice, decimals)}`}</Text>
        <Image style={viewStyles.tinyIcon} source={usdtIcon} />
      </View>
      <Text
        style={textStyles.bodyM}
      >{`${t("Total NFTs")}: ${formatNumericByDecimals(sellingAmount, decimals)}`}</Text>
      <Text
        style={textStyles.bodyM}
      >{`${t("NFTs sold")}: ${formatNumericByDecimals(soldAmount, decimals)}`}</Text>
      <View style={styles.rowSpaceBetween}>
        <Text style={textStyles.bodyM}>{t("Partial match")}</Text>
        <Image
          style={viewStyles.tinyIcon}
          source={isDivisible ? successIcon : failureIcon}
        />
      </View>
      <OfferActionButton offer={offer} />
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    paddingBottom: 12,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  title: {
    ...textStyles.titleS,
    marginTop: 4,
    marginBottom: 8,
  },
  image: {
    width: "100%",
    aspectRatio: 16 / 9,
    borderRadius: 8,
    marginBottom: 8,
  },
  status: {
    marginVertical: 8,
    marginTop: 8,
  },
})

export { MyOfferItemView }
