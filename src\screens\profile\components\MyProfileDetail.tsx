import React, { useCallback, useState } from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import Colors, { textColors } from "src/config/colors"
import {
  AddressView,
  AvatarView,
  CardView,
  CustomPressable,
  PrimaryButton,
} from "src/components"
import { textStyles, viewStyles } from "src/config/styles"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { convertDateFromTimeStamp } from "utils/timeExt"
import { useTranslation } from "react-i18next"
import { useAtom } from "jotai"
import { profileAtom } from "src/context/AuthContext"
import { UpdateContactButton } from "./UpdateContactButton"
import { useAccount } from "wagmi"
import privacyKeyIcon from "assets/images/ic_privacy_key.png"
import verifiedIcon from "assets/images/ic_verified.png"
import bnbIcon from "assets/images/ic_bnb.png"
import upIcon from "assets/images/ic_up.png"
import downIcon from "assets/images/ic_down.png"

const InformationView: React.FC<{ label: string; value: string }> = ({
  label,
  value,
}) => {
  return (
    <View style={styles.rowSpacettween}>
      <Text style={styles.label}>{label}</Text>
      <Text style={styles.value}>{value}</Text>
    </View>
  )
}

const PersonalInformationView: React.FC = () => {
  const [expanded, setExpanded] = useState(false)
  const { t } = useTranslation()
  const [profile] = useAtom(profileAtom)

  return (
    <View style={styles.informationContainer}>
      <CustomPressable
        style={styles.headerInformation}
        onPress={() => setExpanded(!expanded)}
      >
        {!expanded && <Image source={downIcon} style={viewStyles.icon} />}
      </CustomPressable>
      {expanded && (
        <View>
          <Text style={styles.title}>{`${t("Personal Information")}:`}</Text>
          {profile?.alias && (
            <InformationView
              label={`${t("Full name")}:`}
              value={profile.alias}
            />
          )}
          {profile?.dob != null && (
            <InformationView
              label={`${t("Date of birth")}:`}
              value={convertDateFromTimeStamp(profile.dob)}
            />
          )}
          {profile?.phone && (
            <InformationView label={`${t("Phone")}:`} value={profile.phone} />
          )}
          {profile?.email && (
            <InformationView label={`${t("Email")}:`} value={profile.email} />
          )}
          {profile?.nationality && (
            <InformationView
              label={`${t("Country")}:`}
              value={profile.nationality}
            />
          )}
          <CustomPressable
            style={styles.headerInformation}
            onPress={() => setExpanded(!expanded)}
          >
            <Image source={upIcon} style={viewStyles.icon} />
          </CustomPressable>
        </View>
      )}
    </View>
  )
}

const MyProfileDetail: React.FC = () => {
  const { t } = useTranslation()
  const [profile] = useAtom(profileAtom)
  const { address } = useAccount()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const navigateToVerifyAccount = useCallback(() => {
    navigation.navigate("VerifyAccount")
  }, [])

  return (
    <CardView style={styles.card}>
      <View style={styles.centered}>
        <AvatarView size={100} avatarUrl={profile?.avatarUrl ?? ""} />
        <View style={styles.verificationRow}>
          <Image source={privacyKeyIcon} style={viewStyles.icon} />
          <Text style={styles.unverifiedText}>
            {profile?.status === "VERIFIED"
              ? t("Verified")
              : profile?.status === "UNVERIFIED"
                ? t("Unverified")
                : t("Verifying")}
          </Text>
        </View>
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.nameText}>{profile?.alias}</Text>
        <View style={styles.row}>
          <Image source={bnbIcon} style={viewStyles.tinyIcon} />
          <AddressView address={address} style={styles.addressView} />
        </View>
        {profile?.status === "UNVERIFIED" && (
          <PrimaryButton
            style={styles.verify}
            title={t("Verify account")}
            onPress={navigateToVerifyAccount}
            borderRadius={8}
            height={36}
            icon={<Image source={verifiedIcon} style={viewStyles.icon} />}
          />
        )}
        <UpdateContactButton />
      </View>
      <PersonalInformationView />
    </CardView>
  )
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 16,
    padding: 16,
    alignSelf: "stretch",
  },
  centered: {
    alignItems: "center",
  },
  verificationRow: {
    flexDirection: "row",
    marginTop: 16,
    alignItems: "center",
  },
  unverifiedText: {
    ...textStyles.titleL,
    color: Colors.primary,
    marginStart: 4,
  },
  row: {
    flexDirection: "row",
  },
  rowSpacettween: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  informationContainer: {
    marginTop: 8,
    padding: 16,
  },
  infoContainer: {
    marginHorizontal: 16,
    alignItems: "center",
  },
  verify: {
    width: "100%",
  },
  nameText: {
    ...textStyles.titleL,
  },
  addressView: {
    alignSelf: "center",
    marginBottom: 16,
    marginStart: 8,
  },
  flexGrow: {
    flex: 1,
    minHeight: 50,
  },
  headerInformation: {
    flexDirection: "row",
    justifyContent: "center",
    width: "100%",
    marginTop: 4,
  },
  title: {
    ...textStyles.titleS,
    color: textColors.textBlack,
    marginBottom: 8,
  },
  secondaryButton: {
    marginTop: 4,
  },
  label: {
    ...textStyles.labelM,
    color: Colors.black7,
  },
  value: {
    ...textStyles.labelM,
    color: textColors.textBlack,
  },
})

export default MyProfileDetail
