import React, { useState } from "react"
import { StyleSheet, View, ViewStyle } from "react-native"
import { CardView, GroupButtonTitleView } from "components"
import { ActivitiesTab } from "./ActivitiesTab"
import {
  LoansTab,
  MyEstatesTab,
  MyTokenizationsTab,
  MyWalletTab,
  OffersTab,
} from "./index"
import { useTranslation } from "react-i18next"

interface MyProfileWalletProps {
  style?: ViewStyle
}

export const MyProfileWallet: React.FC<MyProfileWalletProps> = ({ style }) => {
  const { t } = useTranslation()
  const [buttonIndex, setButtonIndex] = useState(0)
  const buttonTitles = [
    t("My wallet"),
    t("My Estates"),
    t("Tokenization Requests"),
    t("Activities"),
    t("Offers"),
    t("Loans"),
  ]
  return (
    <CardView style={{ margin: 16, padding: 16, marginBottom: 40 }}>
      <View style={[style, styles.container]}>
        <GroupButtonTitleView
          buttonTitles={buttonTitles}
          selectedIndex={buttonIndex}
          setButtonIndex={setButtonIndex}
        />
        <View style={styles.contentContainer}>
          {buttonIndex === 0 && <MyWalletTab />}
          {buttonIndex === 1 && <MyEstatesTab />}
          {buttonIndex === 2 && <MyTokenizationsTab />}
          {buttonIndex === 3 && <ActivitiesTab />}
          {buttonIndex === 4 && <OffersTab />}
          {buttonIndex === 5 && <LoansTab />}
        </View>
      </View>
    </CardView>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  tabContainer: {
    flexDirection: "row",
    width: "100%",
  },
  contentContainer: {
    flex: 1,
  },
})
