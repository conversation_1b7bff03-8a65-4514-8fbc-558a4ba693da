import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { MyTokenization } from "src/api"
import { useTranslation } from "react-i18next"
import { formatCurrency, formatNumericByDecimals } from "utils"
import { textStyles, viewStyles } from "src/config/styles"
import { CustomPressable } from "components"
import { TokenizationStatus } from "components/estates/TokenizationStatus"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { Router } from "navigation/Router"
import usdtIcon from "assets/images/ic_usdt.png"
import openNewIcon from "assets/images/ic_open_new.png"

interface MyTokenizationItemViewProps {
  tokenization: MyTokenization
}

const MyTokenizationItemView: React.FC<MyTokenizationItemViewProps> = ({
  tokenization,
}) => {
  const { t } = useTranslation()
  const {
    id,
    totalSupply,
    decimals,
    status,
    name,
    imageUrl,
    maxSellingAmount,
    minSellingAmount,
    soldAmount,
    initialPrice,
  } = tokenization
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const navigateToEstateRequestDetail = () => {
    navigation.navigate(Router.EstateRequestDetail, { estateRequestId: id })
  }

  return (
    <View style={styles.itemContainer}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <View style={styles.rowSpaceBetween}>
        <TokenizationStatus status={status} />
        <CustomPressable onPress={navigateToEstateRequestDetail}>
          <Image source={openNewIcon} style={viewStyles.icon} />
        </CustomPressable>
      </View>

      <Text style={styles.title}>{name}</Text>
      <Text
        style={styles.amount}
      >{`${t("Total supply")}: ${formatCurrency(formatNumericByDecimals(totalSupply, decimals))} ${t("NFT")}`}</Text>
      <View style={styles.row}>
        <Text style={textStyles.bodyM}>
          {`${t("Starting price")} ${t("NFT")}: ${formatCurrency(formatNumericByDecimals(initialPrice, decimals))}`}{" "}
        </Text>
        <Image style={viewStyles.tinyIcon} source={usdtIcon} />
        <Text style={textStyles.bodyM}>{`/1${t("NFT")}`}</Text>
      </View>
      <Text
        style={styles.amount}
      >{`${t("Maximum NFTs deposit")}: ${formatCurrency(formatNumericByDecimals(maxSellingAmount, decimals))}`}</Text>
      <Text
        style={styles.amount}
      >{`${t("Minimum NFTs to deposit")}: ${formatCurrency(formatNumericByDecimals(minSellingAmount, decimals))}`}</Text>
      <Text
        style={styles.amount}
      >{`${t("Deposited NFTs")}: ${formatCurrency(formatNumericByDecimals(soldAmount, decimals))}`}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    paddingBottom: 8,
  },
  rowSpaceBetween: {
    alignItems: "center",
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  title: {
    ...textStyles.titleS,
    marginBottom: 8,
  },
  image: {
    width: "100%",
    aspectRatio: 16 / 9,
    borderRadius: 8,
    marginBottom: 8,
  },
  amount: {
    ...textStyles.bodyM,
    marginBottom: 4,
  },
  status: {
    marginVertical: 8,
    marginTop: 8,
  },
})

export { MyTokenizationItemView }
