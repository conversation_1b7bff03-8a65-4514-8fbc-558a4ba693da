import React from "react"
import { Image, ImageRequireSource, StyleSheet, Text, View } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import { useErc20Balance, useErc20Formatter } from "src/api/contracts/erc20"
import {
  CONTRACT_ADDRESS_AUCTION_CURRENCY,
  CONTRACT_ADDRESS_PRIMARY_TOKEN,
  CONTRACT_ADDRESS_STAKE_TOKEN,
} from "src/config/env"
import { parseCurrency } from "utils"
import { useAccount } from "wagmi"
import brikIcon from "assets/images/ic_brik.png"
import brikiIcon from "assets/images/ic_briki.png"
import usdtIcon from "assets/images/ic_usdt.png"
import Colors from "src/config/colors"

const MyWalletTab: React.FC = () => {
  const { address: userAddress } = useAccount()
  const currencyTokenBalance = useErc20Balance(
    userAddress,
    CONTRACT_ADDRESS_AUCTION_CURRENCY
  )
  const currencyFormatter = useErc20Formatter(CONTRACT_ADDRESS_AUCTION_CURRENCY)
  const primaryTokenBalance = useErc20Balance(
    userAddress,
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )
  const primaryTokenFormatter = useErc20Formatter(
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )
  const stakeTokenBalance = useErc20Balance(
    userAddress,
    CONTRACT_ADDRESS_STAKE_TOKEN
  )
  const stakeTokenFormatter = useErc20Formatter(CONTRACT_ADDRESS_STAKE_TOKEN)

  return (
    <View style={styles.container}>
      <WalletItem
        image={brikIcon}
        title={"BRIK"}
        value={parseCurrency(
          primaryTokenFormatter.formatFixed(primaryTokenBalance)
        )}
      />
      <WalletItem
        image={brikiIcon}
        title={"BRIKI"}
        value={parseCurrency(
          stakeTokenFormatter.formatFixed(stakeTokenBalance)
        )}
      />
      <WalletItem
        image={usdtIcon}
        title={"USDT"}
        value={parseCurrency(
          currencyFormatter.formatFixed(currencyTokenBalance)
        )}
      />
    </View>
  )
}

interface WalletItemProps {
  image: ImageRequireSource
  title: string
  value: string
}

const WalletItem: React.FC<WalletItemProps> = ({ image, title, value }) => {
  return (
    <View style={styles.walletItem}>
      <Image source={image} style={viewStyles.bigIcon} />
      <View style={styles.textContainer}>
        <Text style={styles.titleText}>{title}</Text>
        <Text style={styles.valueText}>{value}</Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    height: "100%",
  },
  walletItem: {
    flexDirection: "row",
    marginTop: 16,
    alignItems: "center",
    backgroundColor: Colors.surfaceNormal,
    borderRadius: 12,
    padding: 16,
  },
  textContainer: {
    marginLeft: 16,
  },
  titleText: {
    ...textStyles.titleL,
  },
  valueText: {
    ...textStyles.bodyM,
  },
})

export { MyWalletTab }
