import React, { useState } from "react"
import { OfferFilterModal } from "./OfferFilterModal"
import { FilterView } from "components"

const OfferFilterButton: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <FilterView onClickFilter={() => setIsOpen(true)} />
      <OfferFilterModal isOpen={isOpen} setIsOpen={setIsOpen} />
    </>
  )
}

export { OfferFilterButton }
