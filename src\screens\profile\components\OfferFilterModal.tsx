import React, { use<PERSON><PERSON>back, useContext, useEffect } from "react"
import { CustomCheckbox, PrimaryButton } from "components"
import { StyleSheet, View } from "react-native"
import { BaseModal } from "components/common/BaseModal"
import { useTranslation } from "react-i18next"
import Colors, { textColors } from "src/config/colors"
import ProfileContext from "../context/ProfileContext"
import { OfferFilter } from "../types/index"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { Controller, useForm } from "react-hook-form"

interface OfferFilterModalProps {
  isOpen: boolean
  setIsOpen: (value: boolean) => void
}

const OfferFilterModal: React.FC<OfferFilterModalProps> = ({
  isOpen,
  setIsOpen,
}) => {
  const { t } = useTranslation()
  const { setOfferFilter, offerFilter } = useContext(ProfileContext)
  const formSchema = z.object({
    isDone: z.boolean(),
    isPublicSale: z.boolean(),
    isCancelled: z.boolean(),
    isDeviable: z.boolean(),
  })

  type Payload = z.infer<typeof formSchema>

  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      isDone: false,
      isPublicSale: false,
      isCancelled: false,
      isDeviable: false,
    },
  })

  const isDone = form.watch("isDone")
  const isPublicSale = form.watch("isPublicSale")
  const isCancelled = form.watch("isCancelled")
  const isDeviable = form.watch("isDeviable")

  const isAll = isDone && isPublicSale && isCancelled && isDeviable

  const updateSameValueForAllFilterState = (state: boolean) => {
    form.setValue("isDone", state)
    form.setValue("isPublicSale", state)
    form.setValue("isCancelled", state)
    form.setValue("isDeviable", state)
  }

  const handleAllStatesChange = () => {
    updateSameValueForAllFilterState(!isAll)
  }

  useEffect(() => {
    if (isOpen) {
      form.setValue("isDone", offerFilter.isDone || false)
      form.setValue("isPublicSale", offerFilter.isPublicSale || false)
      form.setValue("isCancelled", offerFilter.isCancelled || false)
      form.setValue("isDeviable", offerFilter.isDeviable || false)
    }
  }, [isOpen, offerFilter, form])

  const onClose = useCallback(() => {
    setIsOpen(false)
  }, [setIsOpen])

  const onSubmit = (data: Payload) => {
    const newTokenizeFilter: OfferFilter = {
      isDone: data.isDone,
      isPublicSale: data.isPublicSale,
      isCancelled: data.isCancelled,
      isDeviable: data.isDeviable,
    }
    setOfferFilter(newTokenizeFilter)
    onClose()
  }

  return (
    <BaseModal isDisableClose={false} visible={isOpen} onClose={onClose}>
      <View style={styles.container}>
        <CustomCheckbox
          label={t("All")}
          isChecked={isAll}
          onToggle={handleAllStatesChange}
        />
        <View style={styles.divider} />
        <Controller
          control={form.control}
          name="isDone"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Done")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="isPublicSale"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Public selling")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="isCancelled"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Cancelled")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <View style={styles.divider} />
        <Controller
          control={form.control}
          name="isDeviable"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Diviable")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />

        <View style={styles.divider} />
        <View style={styles.buttonRow}>
          <PrimaryButton
            style={{ flex: 1 }}
            title={t("Cancel")}
            color={Colors.surfaceNormal}
            contentColor={textColors.textBlack11}
            onPress={onClose}
          />
          <PrimaryButton
            style={{ flex: 1, marginStart: 8 }}
            title={t("Apply")}
            onPress={form.handleSubmit(onSubmit)}
          />
        </View>
      </View>
    </BaseModal>
  )
}

export { OfferFilterModal }

const styles = StyleSheet.create({
  container: {
    alignContent: "center",
    width: "100%",
  },
  divider: {
    height: 1,
    backgroundColor: Colors.black4,
    marginVertical: 8,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    height: 40,
    marginTop: 16,
  },
})
