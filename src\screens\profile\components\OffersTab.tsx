import React from "react"
import { getMyMarketPlaceOffers } from "src/api"
import { MyOfferItemView } from "./MyOfferItemView"
import { useAccount } from "wagmi"
import { useQuery } from "@tanstack/react-query"
import QueryKeys from "src/config/queryKeys"
import { SimpleListView } from "./SimpleListView"

const OffersTab: React.FC = () => {
  const { address } = useAccount()
  const { data: offers = [], isLoading } = useQuery({
    queryKey: QueryKeys.PROFILE.MY_OFFERS(address),
    queryFn: () => getMyMarketPlaceOffers(address),
    refetchInterval: 10_000,
  })

  return (
    <SimpleListView
      data={offers}
      isLoading={isLoading}
      renderItem={(offer) => <MyOfferItemView offer={offer} />}
    />
  )
}

export { OffersTab }
