import React from "react"
import { ActivityIndicator, StyleSheet, View, ViewStyle } from "react-native"
import { EmptyView } from "components/common/EmptyView"
import Colors from "src/config/colors"

interface SimpleListViewProps<T> {
  data: T[]
  isLoading: boolean
  renderItem: (item: T) => React.ReactNode
  containerStyle?: ViewStyle
}

export function SimpleListView<T>({
  data,
  isLoading,
  renderItem,
  containerStyle,
}: SimpleListViewProps<T>) {
  return (
    <View style={[styles.container, containerStyle]}>
      {isLoading ? (
        <ActivityIndicator size="large" color={Colors.primary} />
      ) : data.length === 0 ? (
        <EmptyView />
      ) : (
        <View style={styles.list}>
          {data.map((item, index) => (
            <React.Fragment key={index}>{renderItem(item)}</React.Fragment>
          ))}
        </View>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  list: {
    paddingTop: 16,
  },
})
