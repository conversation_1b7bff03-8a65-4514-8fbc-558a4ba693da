import React, { useState } from "react"
import UpdateContactModal from "./UpdateContactModal"
import { SecondaryButton } from "components"
import { Image, StyleSheet } from "react-native"
import { useTranslation } from "react-i18next"
import accountOutlineIcon from "assets/images/ic_account_outline.png"
import { viewStyles } from "src/config/styles"

export const UpdateContactButton: React.FC = () => {
  const { t } = useTranslation()
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <SecondaryButton
        style={styles.secondaryButton}
        title={t("Update contact")}
        onPress={() => setIsOpen(true)}
        borderRadius={8}
        height={36}
        icon={<Image source={accountOutlineIcon} style={viewStyles.icon} />}
      />
      <UpdateContactModal isShow={isOpen} onClose={() => setIsOpen(false)} />
    </>
  )
}

const styles = StyleSheet.create({
  secondaryButton: {
    marginTop: 4,
    width: "100%",
    flex: 1,
  },
})
