import React, { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { Image, StyleSheet, View } from "react-native"
import { AvatarView, InputField, PrimaryButton } from "components"
import { useTranslation } from "react-i18next"
import { BaseModal } from "components/common/BaseModal"
import { z } from "zod"
import { Controller, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { profileAtom } from "src/context/AuthContext"
import { useAtom } from "jotai"
import { getMyProfile, updateProfile, User } from "src/api"
import { useAccount } from "wagmi"
import { useMutation } from "@tanstack/react-query"
import Colors from "src/config/colors"
import icUpload from "assets/images/ic_upload.png"
import {
  getImageType,
  getPhotoFileName,
  onChoosePhotoFromGallery,
} from "utils/choosePhotoExt"
import { ImagePickerAsset } from "expo-image-picker"
import { useHandleError } from "src/api/errors/handleError"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "UpdateContactModal" })

interface UpdateContactModalProps {
  isShow: boolean
  onClose: () => void
}

const UpdateContactModal: React.FC<UpdateContactModalProps> = ({
  isShow,
  onClose,
}) => {
  const { t } = useTranslation()
  const { handleError } = useHandleError()
  const { address } = useAccount()
  const [profile, setProfile] = useAtom(profileAtom)
  const [avatar, setAvatar] = useState<ImagePickerAsset | null>(null)
  const formSchema = z.object({
    alias: z.string().min(1, t("Please input alias")),
    email: z.string().email(t("Invalid email")),
    phone: z
      .string()
      .min(10, t("Phone number must be at least 10 digits"))
      .max(12, t("Phone number must not exceed 12 digits")),
  })

  type PayLoad = z.infer<typeof formSchema>

  const form = useForm<PayLoad>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      alias: profile?.alias || "",
      email: profile?.email || "",
      phone: profile?.phone || "",
    },
  })

  useEffect(() => {
    if (profile) {
      form.setValue("email", profile.email || "")
      form.setValue("phone", profile.phone || "")
    }
    return () => {
      form.reset()
    }
  }, [profile])

  const closeAndReset = () => {
    form.reset()
    setIsLoading(false)
    setAvatar(null)
    onClose()
  }

  const mutationGetProfile = useMutation({
    mutationFn: () => {
      return getMyProfile(address)
    },
    onSuccess: (userProfile: User) => {
      setProfile(userProfile)
    },
    onSettled() {
      closeAndReset()
    },
  })

  const pickAvatar = useCallback(() => {
    onChoosePhotoFromGallery(
      (files) => {
        const image = files[0]
        setAvatar(image)
      },
      false,
      t
    )
  }, [])

  const [isLoading, setIsLoading] = useState(false)

  const updateContact = useCallback(
    async (data: PayLoad) => {
      setIsLoading(true)
      try {
        const formData = new FormData()
        formData.append("phone", data.phone)
        formData.append("email", data.email)
        formData.append("alias", data.alias)
        logger.debug("Avatar information", {
          avatar,
          hasAvatar: avatar !== null,
        })
        if (avatar !== null) {
          const fileName = getPhotoFileName(avatar.fileName || null, avatar.uri)
          const type = getImageType(fileName)
          // @ts-ignore
          formData.append("avatarImage", {
            uri: avatar.uri,
            type: type,
            name: fileName,
          })
        }
        await updateProfile(formData)
        await mutationGetProfile.mutateAsync()
        updateProfile(formData).then((data) => {
          if (data) mutationGetProfile.mutate()
        })
      } catch (e) {
        handleError(e, t("Update contact failed"))
      } finally {
        setIsLoading(false)
      }
    },
    [address, avatar]
  )

  return (
    <BaseModal
      visible={isShow}
      isDisableClose={isLoading}
      isShowCloseIcon={true}
      title={t("Update contact")}
      onClose={closeAndReset}
    >
      <View style={styles.modalContainer}>
        <AvatarView
          avatarUrl={avatar?.uri || profile?.avatarUrl}
          size={80}
          enableEdit={false}
          style={{ alignSelf: "center" }}
        />
        <PrimaryButton
          title={t("Upload avatar")}
          onPress={pickAvatar}
          width={"100%"}
          height={40}
          color={Colors.surface}
          style={{ marginVertical: 16 }}
          icon={<Image source={icUpload} style={{ width: 24, height: 24 }} />}
        />
        <Controller
          control={form.control}
          name="alias"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputField
              label={t("Alias")}
              value={value}
              onBlur={onBlur}
              onChangeText={onChange}
              style={{ marginTop: 12 }}
              error={
                form.formState.errors.alias?.message &&
                String(form.formState.errors.alias?.message)
              }
            />
          )}
        />
        <Controller
          control={form.control}
          name="phone"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputField
              label={t("Phone")}
              value={value}
              onBlur={onBlur}
              onChangeText={onChange}
              style={{ marginTop: 12 }}
              error={
                form.formState.errors.phone?.message &&
                String(form.formState.errors.phone?.message)
              }
            />
          )}
        />
        <Controller
          control={form.control}
          name="email"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputField
              label={t("Email")}
              value={value}
              onBlur={onBlur}
              onChangeText={onChange}
              style={{ marginTop: 12 }}
              error={
                form.formState.errors.email?.message &&
                String(form.formState.errors.email?.message)
              }
            />
          )}
        />
        <PrimaryButton
          width={"100%"}
          title={t("Confirm")}
          onPress={form.handleSubmit(updateContact)}
          style={styles.saveButton}
          isLoading={isLoading}
        />
      </View>
    </BaseModal>
  )
}

const styles = StyleSheet.create({
  modalContainer: {
    width: "100%",
    borderRadius: 12,
    padding: 12,
  },
  saveButton: {
    marginTop: 12,
  },
})

export default UpdateContactModal
