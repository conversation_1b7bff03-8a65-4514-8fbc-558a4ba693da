import { createContext } from "react"
import {
  Loan<PERSON>ilter,
  LoanFilterInitial,
  OfferFilter,
  OfferFilterInitial,
} from "../types/index"

interface ProfileType {
  offerFilter: OfferFilter
  loanFilter: LoanFilter
  setOfferFilter: React.Dispatch<React.SetStateAction<OfferFilter>>
  setLoanFilter: React.Dispatch<React.SetStateAction<LoanFilter>>
}

const ProfileContext = createContext<ProfileType>({
  offerFilter: OfferFilterInitial,
  loanFilter: LoanFilterInitial,
  setOfferFilter: () => {},
  setLoanFilter: () => {},
})

export default ProfileContext
