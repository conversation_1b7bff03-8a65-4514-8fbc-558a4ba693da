import { useState } from "react"
import {
  LoanFilter,
  LoanFilterInitial,
  OfferFilter,
  OfferFilterInitial,
} from "../types/index"
import { useQuery } from "@tanstack/react-query"
import { getMarketPlaceOffers, getMortgageLoans } from "src/api"
import QueryKeys from "src/config/queryKeys"

export const useApplyOfferFilter = (initialOfferFilter?: OfferFilter) => {
  const [offerFilter, setOfferFilter] = useState<OfferFilter>(
    initialOfferFilter ?? OfferFilterInitial
  )

  const {
    data: offers = [],
    isLoading,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: [...QueryKeys.MARKETPLACE.OFFERS(), offerFilter],
    queryFn: () => getMarketPlaceOffers(),

    refetchOnMount: true,
  })

  return {
    offers,
    offerFilter,
    setOfferFilter,
    isLoading: isLoading || isFetching,
    refetch,
  }
}

export const useApplyLoanFilter = (initialLoanFilter?: LoanFilter) => {
  //TODO: wait BE to send filter to api
  const [loanFilter, setLoanFilter] = useState<LoanFilter>(
    initialLoanFilter ?? LoanFilterInitial
  )
  const {
    data: loans = [],
    isLoading,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: [...QueryKeys.MORTGAGE.LOANS(), loanFilter],
    queryFn: () => getMortgageLoans(),

    refetchOnMount: true,
  })

  return {
    loans,
    loanFilter,
    setLoanFilter,
    isLoading: isLoading || isFetching,
    refetch,
  }
}
