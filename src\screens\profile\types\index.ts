export type NativeLand = {
  owner: boolean
  nftOwn: boolean
  id: string
  title: string
  ownership: string
  uri: string
  date: string
  nftCount: number
  price: string
}

export type Transaction = {
  owner: boolean
  nftOwn: boolean
  id: string
  title: string
  uri: string
  ownership: string
  date: string
  nftCount: number
  price: string
}

export interface OfferFilter {
  isDone: boolean
  isPublicSale: boolean
  isCancelled: boolean
  isDeviable: boolean
}

export const OfferFilterInitial: OfferFilter = {
  isDone: false,
  isPublicSale: false,
  isCancelled: false,
  isDeviable: false,
}

export interface LoanFilter {
  isWaitingHandle: boolean
  isSupplied: boolean
  isRefunded: boolean
  isRecalled: boolean
  isCancelled: boolean
  isExpired: boolean
  isNotExpire: boolean
}

export const LoanFilterInitial: LoanFilter = {
  isWaitingHandle: false,
  isSupplied: false,
  isRefunded: false,
  isRecalled: false,
  isCancelled: false,
  isExpired: false,
  isNotExpire: false,
}
