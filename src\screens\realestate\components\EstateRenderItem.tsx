import React from "react"
import { Image, Pressable, StyleSheet, Text, View } from "react-native"
import { CardView, EstateState, TimeView } from "components"
import { textStyles, viewStyles } from "src/config/styles"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { Router } from "navigation/Router"
import { Estate } from "src/api"
import { LandInfo } from "components/estates/LandInfo"
import { getFullAddress, TokenizationState } from "src/api/types"
import locationIcon from "assets/images/ic_location.png"

const EstateRenderItem: React.FC<{
  item: Estate
  action?: React.ReactNode
}> = ({ item, action }) => {
  const {
    id,
    decimals,
    totalSupply,
    metadata: {
      imageUrl,
      metadata: {
        area: { area, unit },
        name,
        updated_at,
        address,
        locale_detail,
      },
    },
    tokenizationRequest: { unitPrice },
    createAtInSeconds,
  } = item
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const navigateToDetail = () => {
    navigation.navigate(Router.EstateDetail, {
      estateId: id,
    })
  }

  return (
    <CardView style={styles.card}>
      <Pressable onPress={navigateToDetail}>
        <View>
          <Image source={{ uri: imageUrl }} style={styles.image} />
          <View style={styles.content}>
            <Text style={styles.title}>{name}</Text>
            <View style={styles.statusRow}>
              <EstateState state={TokenizationState.TOKENIZED} />
              <TimeView
                style={styles.timeView}
                time={(createAtInSeconds ?? updated_at) * 1000}
              />
            </View>
            <AddressView address={getFullAddress(address, locale_detail)} />
            <LandInfo
              unitPrice={unitPrice}
              totalSupply={totalSupply}
              decimals={decimals}
              area={area}
              areaUnit={unit}
            />
            {action}
          </View>
        </View>
      </Pressable>
    </CardView>
  )
}

const AddressView: React.FC<{
  address?: string
}> = ({ address }) => (
  <View style={styles.addressView}>
    <Image source={locationIcon} style={viewStyles.icon} />
    <Text style={[textStyles.bodyM, styles.addressText]}>{address}</Text>
  </View>
)

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    overflow: "hidden",
    margin: 2,
  },
  image: {
    aspectRatio: 16 / 9,
  },
  content: {
    padding: 12,
  },
  title: {
    ...textStyles.titleL,
    fontWeight: "500",
  },
  addressView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    paddingEnd: 16,
  },
  addressText: {
    marginStart: 8,
  },
  statusRow: {
    marginTop: 8,
    flexDirection: "row",
    alignItems: "center",
  },
  timeView: {
    marginStart: 4,
  },
})

export { EstateRenderItem }
