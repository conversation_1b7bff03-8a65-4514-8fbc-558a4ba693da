import React, { useState } from "react"
import { FilterView } from "components/FilterView"
import { EstateRequestFilterModal } from "./EstateRequestFilterModal"

export const EstateRequestFilterButton: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <>
      <FilterView onClickFilter={() => setIsOpen(true)} />
      <EstateRequestFilterModal isOpen={isOpen} setIsOpen={setIsOpen} />
    </>
  )
}
