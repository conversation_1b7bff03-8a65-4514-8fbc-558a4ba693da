import React, { useCallback, useContext, useEffect } from "react"
import {
  CustomCheckbox,
  CustomPressable,
  GroupButtonTitleView,
  PrimaryButton,
} from "components"
import { Image, ScrollView, StyleSheet, Text, View } from "react-native"
import { BaseModal } from "components/common/BaseModal"
import { useTranslation } from "react-i18next"
import Colors, { textColors } from "src/config/colors"
import { z } from "zod"
import { textStyles, viewStyles } from "src/config/styles"
import { zodResolver } from "@hookform/resolvers/zod"
import { Controller, useForm } from "react-hook-form"
import EstateRequestContext from "../context/EstateRequestContext"
import { EstateRequestFilter, SortBy } from "../hooks"
import caretUpIcon from "assets/images/ic_caret_up.png"
import caretDownIcon from "assets/images/ic_caret_down.png"

const useSortByButtonTitles = () => {
  const { t } = useTranslation()
  return [t("Time"), t("Unit price"), t("Total Supply")]
}
const estateTypeSchema = z.object({
  isResidential: z.boolean(),
  isAgricultural: z.boolean(),
  isNonAgricultural: z.boolean(),
  isForest: z.boolean(),
  isPerennialCrop: z.boolean(),
  isProduction: z.boolean(),
  isIndustrial: z.boolean(),
})

const statusSchema = z.object({
  isSelling: z.boolean(),
  isTransferringOwnerShip: z.boolean(),
  isInsufficientSoldAmount: z.boolean(),
  isCancelled: z.boolean(),
  isExpired: z.boolean(),
})

const sortBySchema = z.object({
  type: z.nativeEnum(SortBy),
  isDescending: z.boolean(),
})

const formSchema = z.object({
  estateType: estateTypeSchema,
  status: statusSchema,
  isMyEstate: z.boolean(),
  isEstateHaveSellNFTOrder: z.boolean(),
  isEsateHaveLoan: z.boolean(),
  sortBy: sortBySchema,
})

type Payload = z.infer<typeof formSchema>

const useFormData = () => {
  return useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      estateType: {
        isResidential: false,
        isAgricultural: false,
        isNonAgricultural: false,
        isForest: false,
        isPerennialCrop: false,
        isProduction: false,
        isIndustrial: false,
      },
      status: {
        isSelling: false,
        isTransferringOwnerShip: false,
        isInsufficientSoldAmount: false,
        isCancelled: false,
        isExpired: false,
      },
      isMyEstate: false,
      isEstateHaveSellNFTOrder: false,
      isEsateHaveLoan: false,
      sortBy: {
        type: SortBy.TIMESTAMP,
        isDescending: false,
      },
    },
  })
}

interface EstateRequestFilterModalProps {
  isOpen: boolean
  setIsOpen: (value: boolean) => void
}

const EstateRequestFilterModal: React.FC<EstateRequestFilterModalProps> = ({
  isOpen,
  setIsOpen,
}) => {
  const { t } = useTranslation()
  const { estateRequestFilter, setEstateRequestFilter } =
    useContext(EstateRequestContext)
  const sortByButtonTitles = useSortByButtonTitles()
  const form = useFormData()

  // const isResidential = form.watch("estateType.isResidential")
  // const isAgricultural = form.watch("estateType.isAgricultural")
  // const isNonAgricultural = form.watch("estateType.isNonAgricultural")
  // const isForest = form.watch("estateType.isForest")
  // const isPerennialCrop = form.watch("estateType.isPerennialCrop")
  // const isProduction = form.watch("estateType.isProduction")
  // const isIndustrial = form.watch("estateType.isIndustrial")

  const isSelling = form.watch("status.isSelling")
  const isTransferringOwnerShip = form.watch("status.isTransferringOwnerShip")
  const isInsufficientSoldAmount = form.watch("status.isInsufficientSoldAmount")
  const isCancelled = form.watch("status.isCancelled")
  const isExpired = form.watch("status.isExpired")
  // const isAllEstateType =
  //   isResidential &&
  //   isAgricultural &&
  //   isNonAgricultural &&
  //   isForest &&
  //   isPerennialCrop &&
  //   isProduction &&
  //   isIndustrial

  const isAllStatus =
    isSelling &&
    isTransferringOwnerShip &&
    isInsufficientSoldAmount &&
    isCancelled &&
    isExpired

  useEffect(() => {
    if (isOpen) {
      form.setValue(
        "estateType.isResidential",
        estateRequestFilter?.estateType?.isResidential || false
      )
      form.setValue(
        "estateType.isAgricultural",
        estateRequestFilter?.estateType?.isAgricultural || false
      )
      form.setValue(
        "estateType.isNonAgricultural",
        estateRequestFilter?.estateType?.isNonAgricultural || false
      )
      form.setValue(
        "estateType.isForest",
        estateRequestFilter?.estateType?.isForest || false
      )
      form.setValue(
        "estateType.isPerennialCrop",
        estateRequestFilter?.estateType?.isPerennialCrop || false
      )
      form.setValue(
        "estateType.isProduction",
        estateRequestFilter?.estateType?.isProduction || false
      )
      form.setValue(
        "estateType.isIndustrial",
        estateRequestFilter?.estateType?.isIndustrial || false
      )

      form.setValue(
        "status.isSelling",
        estateRequestFilter?.status?.isSelling || false
      )
      form.setValue(
        "status.isTransferringOwnerShip",
        estateRequestFilter?.status?.isTransferringOwnerShip || false
      )
      form.setValue(
        "status.isInsufficientSoldAmount",
        estateRequestFilter?.status?.isInsufficientSoldAmount || false
      )
      form.setValue(
        "status.isCancelled",
        estateRequestFilter?.status?.isCancelled || false
      )
      form.setValue(
        "status.isExpired",
        estateRequestFilter?.status?.isExpired || false
      )
      form.setValue("isMyEstate", estateRequestFilter?.isMyEstate || false)
      form.setValue(
        "isEstateHaveSellNFTOrder",
        estateRequestFilter?.isEstateHaveSellNFTOrder || false
      )
      form.setValue(
        "isEsateHaveLoan",
        estateRequestFilter?.isEsateHaveLoan || false
      )

      form.setValue(
        "sortBy.type",
        estateRequestFilter?.sortBy?.type || SortBy.TIMESTAMP
      )
      form.setValue(
        "sortBy.isDescending",
        estateRequestFilter?.sortBy?.isDescending || false
      )
    }
  }, [isOpen, estateRequestFilter, form])

  // const toggleAllEstateTypes = (state: boolean) => {
  //   form.setValue("estateType.isResidential", state)
  //   form.setValue("estateType.isAgricultural", state)
  //   form.setValue("estateType.isNonAgricultural", state)
  //   form.setValue("estateType.isForest", state)
  //   form.setValue("estateType.isPerennialCrop", state)
  //   form.setValue("estateType.isProduction", state)
  //   form.setValue("estateType.isIndustrial", state)
  // }

  const toggleAllStatuses = (state: boolean) => {
    form.setValue("status.isSelling", state)
    form.setValue("status.isTransferringOwnerShip", state)
    form.setValue("status.isInsufficientSoldAmount", state)
    form.setValue("status.isCancelled", state)
    form.setValue("status.isExpired", state)
  }

  const handleAllStatusesChange = () => {
    toggleAllStatuses(!isAllStatus)
  }

  // const handleAllEstateTypesChange = () => {
  //   toggleAllEstateTypes(!isAllEstateType)
  // }

  const onSubmit = (data: Payload) => {
    const newFilter: EstateRequestFilter = {
      estateType: {
        isResidential: data.estateType.isResidential,
        isAgricultural: data.estateType.isAgricultural,
        isNonAgricultural: data.estateType.isNonAgricultural,
        isForest: data.estateType.isForest,
        isPerennialCrop: data.estateType.isPerennialCrop,
        isProduction: data.estateType.isProduction,
        isIndustrial: data.estateType.isIndustrial,
      },
      status: {
        isSelling: data.status.isSelling,
        isTransferringOwnerShip: data.status.isTransferringOwnerShip,
        isInsufficientSoldAmount: data.status.isInsufficientSoldAmount,
        isCancelled: data.status.isCancelled,
        isExpired: data.status.isExpired,
      },
      isMyEstate: data.isMyEstate,
      isEstateHaveSellNFTOrder: data.isEstateHaveSellNFTOrder,
      isEsateHaveLoan: data.isEsateHaveLoan,
      sortBy: {
        type: data.sortBy.type,
        isDescending: data.sortBy.isDescending,
      },
    }
    setEstateRequestFilter?.(newFilter)
    onClose()
  }

  const onClose = useCallback(() => {
    setIsOpen(false)
  }, [setIsOpen])

  return (
    <BaseModal isDisableClose={false} visible={isOpen} onClose={onClose}>
      <View>
        <ScrollView style={styles.container}>
          <View style={styles.viewContainer}>
            {/* <Text style={styles.title}>{t("Real estate type")}</Text>
            <CustomCheckbox
              label={t("All")}
              isChecked={isAllEstateType}
              onToggle={handleAllEstateTypesChange}
            />
            <Controller
              control={form.control}
              name="estateType.isResidential"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllEstateType}
                  label={t("Residential")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <Controller
              control={form.control}
              name="estateType.isAgricultural"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllEstateType}
                  label={t("Agricultural")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <Controller
              control={form.control}
              name="estateType.isNonAgricultural"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllEstateType}
                  label={t("Non agricultural")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <Controller
              control={form.control}
              name="estateType.isForest"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllEstateType}
                  label={t("Forest")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <Controller
              control={form.control}
              name="estateType.isPerennialCrop"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllEstateType}
                  label={t("Perennial Crop")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <Controller
              control={form.control}
              name="estateType.isProduction"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllEstateType}
                  label={t("Production")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <Controller
              control={form.control}
              name="estateType.isIndustrial"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllEstateType}
                  label={t("Industrial")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            /> */}
            <Text style={styles.title}>{t("Status")}</Text>
            <CustomCheckbox
              label={t("All")}
              isChecked={isAllStatus}
              onToggle={handleAllStatusesChange}
            />
            <Controller
              control={form.control}
              name="status.isSelling"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllStatus}
                  label={t("Public sale")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <Controller
              control={form.control}
              name="status.isTransferringOwnerShip"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllStatus}
                  label={t("Transferring ownership")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <Controller
              control={form.control}
              name="status.isInsufficientSoldAmount"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllStatus}
                  label={t("Insufficient sold amount")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <Controller
              control={form.control}
              name="status.isCancelled"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllStatus}
                  label={t("Cancelled")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <Controller
              control={form.control}
              name="status.isExpired"
              render={({ field: { onChange, value } }) => (
                <CustomCheckbox
                  disabled={isAllStatus}
                  label={t("Expired")}
                  isChecked={value}
                  onToggle={onChange}
                />
              )}
            />
            <View style={styles.divider} />
            <Text style={styles.title}>{t("Sort By")}</Text>
            <Controller
              control={form.control}
              name="sortBy.type"
              render={({ field: { onChange, value } }) => (
                <GroupButtonTitleView
                  buttonTitles={sortByButtonTitles}
                  selectedIndex={value}
                  setButtonIndex={(index) => {
                    onChange(index)
                  }}
                />
              )}
            />
            <Text style={styles.title}>{t("Arrangement")}</Text>
            <Controller
              control={form.control}
              name="sortBy.isDescending"
              render={({ field: { onChange, value } }) => (
                <View
                  style={{
                    flexDirection: "row",
                    width: "100%",
                  }}
                >
                  <CustomPressable
                    style={[
                      !value ? styles.activeSortButton : styles.sortButton,
                      { marginEnd: 8 },
                    ]}
                    onPress={() => {
                      onChange(false)
                    }}
                  >
                    <Image source={caretUpIcon} style={viewStyles.icon} />
                  </CustomPressable>
                  <CustomPressable
                    style={value ? styles.activeSortButton : styles.sortButton}
                    onPress={() => {
                      onChange(true)
                    }}
                  >
                    <Image source={caretDownIcon} style={viewStyles.icon} />
                  </CustomPressable>
                </View>
              )}
            />
          </View>
        </ScrollView>
        <View style={styles.buttonRow}>
          <PrimaryButton
            style={{ flex: 1 }}
            title={t("Cancel")}
            color={Colors.surfaceNormal}
            contentColor={textColors.textBlack11}
            onPress={onClose}
          />
          <PrimaryButton
            style={{ flex: 1, marginStart: 8 }}
            title={t("Apply")}
            onPress={form.handleSubmit(onSubmit)}
          />
        </View>
      </View>
    </BaseModal>
  )
}

const styles = StyleSheet.create({
  container: {
    maxHeight: 500,
  },
  viewContainer: {
    flex: 1,
  },
  title: {
    ...textStyles.titleS,
    marginBottom: 8,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.black4,
    marginVertical: 8,
  },
  activeSortButton: {
    backgroundColor: Colors.primaryLight,
    borderRadius: 4,
    padding: 8,
  },
  sortButton: {
    borderRadius: 4,
    padding: 8,
  },
  icon: {
    width: 16,
    height: 16,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    height: 40,
    marginTop: 16,
  },
})

export { EstateRequestFilterModal }
