import React from "react"
import { Image, Pressable, StyleSheet, Text, View } from "react-native"
import { CardView, TimeView, TokenizationRequestState } from "components"
import { textStyles, viewStyles } from "src/config/styles"
import { textColors } from "src/config/colors"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { Router } from "navigation/Router"
import { TokenizationRequest } from "src/api"
import { LandInfo } from "components/estates/LandInfo"
import locationIcon from "assets/images/ic_location.png"
import { getFullAddress } from "src/api/types"

const EstateRequestRenderItem: React.FC<{
  item: TokenizationRequest
  action?: React.ReactNode
}> = ({ item, action }) => {
  const {
    metadata: {
      metadata: {
        name,
        address,
        locale_detail,
        area: { area, unit },
        created_at,
      },
      imageUrl,
    },
    id,
    decimals,
    state,
    totalSupply,
    unitPrice,
  } = item

  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const navigateToDetail = () => {
    navigation.navigate(Router.EstateRequestDetail, {
      estateRequestId: id,
    })
  }

  return (
    <CardView style={styles.card}>
      <Pressable onPress={navigateToDetail}>
        <View>
          <Image source={{ uri: imageUrl }} style={styles.image} />
          <View style={styles.content}>
            <Text style={styles.title}>{name}</Text>
            <View style={styles.statusRow}>
              <TokenizationRequestState state={state} />
              <TimeView
                style={styles.timeView}
                time={new Date(created_at).getTime()}
              />
            </View>
            <AddressView address={getFullAddress(address, locale_detail)} />
            <LandInfo
              unitPrice={unitPrice}
              totalSupply={totalSupply}
              decimals={decimals}
              area={area}
              areaUnit={unit}
            />
            {action}
          </View>
        </View>
      </Pressable>
    </CardView>
  )
}

const AddressView: React.FC<{
  address?: string
}> = ({ address }) => (
  <View style={styles.addressView}>
    <Image source={locationIcon} style={viewStyles.icon} />
    <Text style={styles.addressText}>{address}</Text>
  </View>
)

const styles = StyleSheet.create({
  card: {
    marginTop: 20,
    overflow: "hidden",
    padding: 12,
    margin: 2,
  },
  image: {
    width: "100%",
    aspectRatio: 16 / 9,
    borderRadius: 8,
  },
  content: {
    marginTop: 12,
  },
  title: {
    ...textStyles.titleS,
    color: textColors.textBlack11,
    marginBottom: 4,
  },
  statusRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  addressView: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 8,
    paddingEnd: 16,
  },
  addressText: {
    ...textStyles.bodyM,
    marginStart: 8,
  },
  timeView: {
    marginStart: 4,
  },
})

export { EstateRequestRenderItem }
