import React, { useState } from "react"
import { FilterView } from "components/FilterView"
import { MarketPlaceOfferFilterModal } from "./MarketPlaceOfferFilterModal"
import { View, ViewStyle } from "react-native"

interface MarketPlaceOfferFilterButtonProps {
  style?: ViewStyle
}

export const MarketPlaceOfferFilterButton: React.FC<
  MarketPlaceOfferFilterButtonProps
> = ({ style }) => {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <View style={style}>
      <FilterView onClickFilter={() => setIsOpen(true)} />
      <MarketPlaceOfferFilterModal isOpen={isOpen} setIsOpen={setIsOpen} />
    </View>
  )
}
