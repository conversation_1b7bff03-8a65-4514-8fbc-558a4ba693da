import React, { use<PERSON><PERSON>back, useContext, useEffect } from "react"
import { CustomCheckbox, PrimaryButton } from "components"
import { StyleSheet, Text, View } from "react-native"
import { BaseModal } from "components/common/BaseModal"
import { useTranslation } from "react-i18next"
import Colors, { textColors } from "src/config/colors"
import { z } from "zod"
import { textStyles } from "src/config/styles"
import { zodResolver } from "@hookform/resolvers/zod"
import { Controller, useForm } from "react-hook-form"
import MarketPlaceOfferContext from "../context/MarketPlaceOfferContext"
import { MarketPlaceOfferFilter } from "../hooks"

const statusSchema = z.object({
  isSelling: z.boolean(),
  isSold: z.boolean(),
  isCancelled: z.boolean(),
})

const formSchema = z.object({
  status: statusSchema,
})

type Payload = z.infer<typeof formSchema>

const useFormData = () => {
  return useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      status: {
        isSelling: false,
        isSold: false,
        isCancelled: false,
      },
    },
  })
}

interface MarketPlaceOfferFilterModalProps {
  isOpen: boolean
  setIsOpen: (value: boolean) => void
}

const MarketPlaceOfferFilterModal: React.FC<
  MarketPlaceOfferFilterModalProps
> = ({ isOpen, setIsOpen }) => {
  const { t } = useTranslation()
  const { marketPlaceOfferFilter, setMarketPlaceOfferFilter } = useContext(
    MarketPlaceOfferContext
  )
  const form = useFormData()

  const isSelling = form.watch("status.isSelling")
  const isSold = form.watch("status.isSold")
  const isCancelled = form.watch("status.isCancelled")

  const isAll = isSelling && isSold && isCancelled

  useEffect(() => {
    if (isOpen) {
      form.setValue(
        "status.isSelling",
        marketPlaceOfferFilter?.status?.isSelling || false
      )
      form.setValue(
        "status.isSold",
        marketPlaceOfferFilter?.status?.isSold || false
      )
      form.setValue(
        "status.isCancelled",
        marketPlaceOfferFilter?.status?.isCancelled || false
      )
    }
  }, [isOpen, marketPlaceOfferFilter, form])

  const toggleAllStatuses = (state: boolean) => {
    form.setValue("status.isSelling", state)
    form.setValue("status.isSold", state)
    form.setValue("status.isCancelled", state)
  }

  const handleAllStatusesChange = () => {
    toggleAllStatuses(!isAll)
  }

  const onSubmit = (data: Payload) => {
    const newFilter: MarketPlaceOfferFilter = {
      status: {
        isSelling: data.status.isSelling,
        isSold: data.status.isSold,
        isCancelled: data.status.isCancelled,
      },
    }
    setMarketPlaceOfferFilter?.(newFilter)
    onClose()
  }

  const onClose = useCallback(() => {
    setIsOpen(false)
  }, [setIsOpen])

  return (
    <BaseModal isDisableClose={false} visible={isOpen} onClose={onClose}>
      <View>
        <Text style={styles.title}>{t("Status")}</Text>
        <CustomCheckbox
          label={t("All")}
          isChecked={isAll}
          onToggle={handleAllStatusesChange}
        />
        <Controller
          control={form.control}
          name="status.isSelling"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Selling")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="status.isSold"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Sold")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="status.isCancelled"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Cancelled")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <View style={styles.buttonRow}>
          <PrimaryButton
            style={{ flex: 1 }}
            title={t("Cancel")}
            color={Colors.surfaceNormal}
            contentColor={textColors.textBlack11}
            onPress={onClose}
          />
          <PrimaryButton
            style={{ flex: 1, marginStart: 8 }}
            title={t("Apply")}
            onPress={form.handleSubmit(onSubmit)}
          />
        </View>
      </View>
    </BaseModal>
  )
}

const styles = StyleSheet.create({
  title: {
    ...textStyles.titleS,
    marginBottom: 8,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    height: 40,
    marginTop: 16,
  },
})

export { MarketPlaceOfferFilterModal }
