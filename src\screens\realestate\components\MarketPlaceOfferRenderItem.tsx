import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { CustomPressable, OfferState } from "components"
import { OfferActionButton } from "components/estates/OfferActionButton"
import { textStyles, viewStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import { formatCurrencyByDecimals } from "utils"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { Router } from "navigation/Router"
import { MarketplaceOffer } from "src/api"
import usdtIcon from "assets/images/ic_usdt.png"
import successIcon from "assets/images/ic_success.png"
import failureIcon from "assets/images/ic_failure.png"
import openNewIcon from "assets/images/ic_open_new.png"

const MarketPlaceOfferRenderItem: React.FC<{
  item: MarketplaceOffer
}> = ({ item }) => {
  const { t } = useTranslation()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const {
    state,
    sellingAmount,
    soldAmount,
    unitPrice,
    isDivisible,
    estate: {
      decimals,
      metadata: {
        metadata: { name },
        imageUrl,
      },
    },
  } = item

  const navigateToEstateDetail = () => {
    navigation.navigate(Router.EstateDetail, {
      estateId: item.estate.id,
    })
  }

  return (
    <View style={styles.itemContainer}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <View style={styles.rowSpaceBetween}>
        <OfferState state={state} />
        <CustomPressable onPress={navigateToEstateDetail}>
          <Image source={openNewIcon} style={viewStyles.icon} />
        </CustomPressable>
      </View>
      <Text style={styles.title}>{name}</Text>
      <View style={styles.rowSpaceBetween}>
        <Text
          style={textStyles.bodyM}
        >{`${t("Price")} ${t("NFT")}: ${formatCurrencyByDecimals(unitPrice, decimals)}`}</Text>
        <Image style={viewStyles.tinyIcon} source={usdtIcon} />
      </View>
      <Text
        style={textStyles.bodyM}
      >{`${t("Selling NFT number")}: ${formatCurrencyByDecimals(sellingAmount, decimals)}`}</Text>
      <Text
        style={textStyles.bodyM}
      >{`${t("Matched NFT number")}: ${formatCurrencyByDecimals(soldAmount, decimals)}`}</Text>
      <View style={styles.rowSpaceBetween}>
        <Text style={textStyles.bodyM}>{t("Diviable")}</Text>
        <Image
          style={viewStyles.tinyIcon}
          source={isDivisible ? successIcon : failureIcon}
        />
      </View>
      <OfferActionButton offer={item} />
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    paddingBottom: 12,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  title: {
    ...textStyles.labelL,
    marginTop: 4,
    marginBottom: 8,
  },
  image: {
    width: "100%",
    aspectRatio: 16 / 9,
    borderRadius: 8,
    marginBottom: 8,
  },
  status: {
    marginVertical: 8,
    marginTop: 8,
  },
  flex: {
    flex: 1,
  },
  areaUnit: {
    marginBottom: 4,
    fontSize: 8,
  },
})

export { MarketPlaceOfferRenderItem }
