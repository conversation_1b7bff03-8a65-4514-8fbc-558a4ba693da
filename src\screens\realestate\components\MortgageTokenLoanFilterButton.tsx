import React, { useState } from "react"
import { FilterView } from "components/FilterView"
import { MortgageTokenLoanFilterModal } from "./MortgageTokenLoanFilterModal"
import { View, ViewStyle } from "react-native"

interface MortgageTokenLoanFilterButtonProps {
  style?: ViewStyle
}

export const MortgageTokenLoanFilterButton: React.FC<
  MortgageTokenLoanFilterButtonProps
> = ({ style }) => {
  const [isOpen, setIsOpen] = useState(false)
  return (
    <View style={style}>
      <FilterView onClickFilter={() => setIsOpen(true)} />
      <MortgageTokenLoanFilterModal isOpen={isOpen} setIsOpen={setIsOpen} />
    </View>
  )
}
