import React, { useC<PERSON>back, useContext, useEffect } from "react"
import { CustomCheckbox, PrimaryButton } from "components"
import { StyleSheet, Text, View } from "react-native"
import { BaseModal } from "components/common/BaseModal"
import { useTranslation } from "react-i18next"
import Colors, { textColors } from "src/config/colors"
import { z } from "zod"
import { textStyles } from "src/config/styles"
import { zodResolver } from "@hookform/resolvers/zod"
import { Controller, useForm } from "react-hook-form"
import MortgageHubLoanContext from "../context/MortgageHubLoanContext"
import { MortgageTokenLoanFilter } from "../hooks"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "MortgageTokenLoanFilterModal" })

const statusSchema = z.object({
  isPending: z.boolean(),
  isSupplied: z.boolean(),
  isOverdue: z.boolean(),
  isRepaid: z.boolean(),
  isForeclosed: z.boolean(),
  isCancelled: z.boolean(),
})

const formSchema = z.object({
  status: statusSchema,
})

type Payload = z.infer<typeof formSchema>

const useFormData = () => {
  return useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      status: {
        isPending: false,
        isSupplied: false,
        isOverdue: false,
        isRepaid: false,
        isForeclosed: false,
        isCancelled: false,
      },
    },
  })
}

interface MortgageTokenLoanFilterModalProps {
  isOpen: boolean
  setIsOpen: (value: boolean) => void
}

const MortgageTokenLoanFilterModal: React.FC<
  MortgageTokenLoanFilterModalProps
> = ({ isOpen, setIsOpen }) => {
  const { t } = useTranslation()
  const { mortgageTokenLoanFilter, setMortgageTokenLoanFilter } = useContext(
    MortgageHubLoanContext
  )
  const form = useFormData()

  const isPending = form.watch("status.isPending")
  const isSupplied = form.watch("status.isSupplied")
  const isOverdue = form.watch("status.isOverdue")
  const isRepaid = form.watch("status.isRepaid")
  const isForeclosed = form.watch("status.isForeclosed")
  const isCancelled = form.watch("status.isCancelled")

  const isAll =
    isPending &&
    isSupplied &&
    isOverdue &&
    isRepaid &&
    isForeclosed &&
    isCancelled

  useEffect(() => {
    if (isOpen) {
      form.setValue(
        "status.isPending",
        mortgageTokenLoanFilter?.status?.isPending || false
      )
      form.setValue(
        "status.isSupplied",
        mortgageTokenLoanFilter?.status?.isSupplied || false
      )
      form.setValue(
        "status.isOverdue",
        mortgageTokenLoanFilter?.status?.isOverdue || false
      )
      form.setValue(
        "status.isRepaid",
        mortgageTokenLoanFilter?.status?.isRepaid || false
      )
      form.setValue(
        "status.isForeclosed",
        mortgageTokenLoanFilter?.status?.isForeclosed || false
      )
      form.setValue(
        "status.isCancelled",
        mortgageTokenLoanFilter?.status?.isCancelled || false
      )
    }
  }, [isOpen, mortgageTokenLoanFilter, form])

  const toggleAllStatuses = (state: boolean) => {
    form.setValue("status.isPending", state)
    form.setValue("status.isSupplied", state)
    form.setValue("status.isOverdue", state)
    form.setValue("status.isRepaid", state)
    form.setValue("status.isForeclosed", state)
    form.setValue("status.isCancelled", state)
  }

  const handleAllStatusesChange = () => {
    toggleAllStatuses(!isAll)
  }

  const onSubmit = (data: Payload) => {
    const newFilter: MortgageTokenLoanFilter = {
      status: {
        isPending: data.status.isPending,
        isSupplied: data.status.isSupplied,
        isOverdue: data.status.isOverdue,
        isRepaid: data.status.isRepaid,
        isForeclosed: data.status.isForeclosed,
        isCancelled: data.status.isCancelled,
      },
    }
    setMortgageTokenLoanFilter?.(newFilter)
    onClose()
  }

  const onClose = useCallback(() => {
    logger.debug("Closing mortgage token loan filter modal")
    setIsOpen(false)
  }, [setIsOpen])

  return (
    <BaseModal isDisableClose={false} visible={isOpen} onClose={onClose}>
      <View>
        <Text style={styles.title}>{t("Status")}</Text>
        <CustomCheckbox
          label={t("All")}
          isChecked={isAll}
          onToggle={handleAllStatusesChange}
        />
        <Controller
          control={form.control}
          name="status.isPending"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Mortgage Open")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="status.isSupplied"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Mortgage Lent")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="status.isOverdue"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Mortgage Overdue")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="status.isRepaid"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Mortgage Repaid")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="status.isForeclosed"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Mortgage Foreclosed")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <Controller
          control={form.control}
          name="status.isCancelled"
          render={({ field: { onChange, value } }) => (
            <CustomCheckbox
              disabled={isAll}
              label={t("Mortgage Cancelled")}
              isChecked={value}
              onToggle={onChange}
            />
          )}
        />
        <View style={styles.buttonRow}>
          <PrimaryButton
            style={{ flex: 1 }}
            title={t("Cancel")}
            color={Colors.surfaceNormal}
            contentColor={textColors.textBlack11}
            onPress={onClose}
          />
          <PrimaryButton
            style={{ flex: 1, marginStart: 8 }}
            title={t("Apply")}
            onPress={form.handleSubmit(onSubmit)}
          />
        </View>
      </View>
    </BaseModal>
  )
}

const styles = StyleSheet.create({
  title: {
    ...textStyles.titleS,
    marginBottom: 8,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
    height: 40,
    marginTop: 16,
  },
})

export { MortgageTokenLoanFilterModal }
