import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { CustomPressable, LoanActionButton, LoanState } from "components"
import { textStyles, viewStyles } from "src/config/styles"

import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { formatCurrency, formatNumericByDecimals } from "utils"
import { convertSecondsToTime } from "utils/timeExt"
import { Router } from "navigation/Router"
import { useTranslation } from "react-i18next"
import { MortgageTokenLoan, MortgageTokenLoanState } from "src/api"
import usdtIcon from "assets/images/ic_usdt.png"
import { textColors } from "src/config/colors"
import openNewIcon from "assets/images/ic_open_new.png"

interface LabelViewProps {
  label: string
  value: string
}

const LabelView: React.FC<LabelViewProps> = ({ label, value }) => (
  <View style={styles.labelView}>
    <Text style={styles.label}>{label}</Text>
    <Text style={styles.value}>{value}</Text>
  </View>
)

const MortgageTokenLoanRenderItem: React.FC<{
  item: MortgageTokenLoan
  action?: React.ReactNode
}> = ({ item }) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const navigateToEstateDetail = () => {
    navigation.navigate(Router.EstateDetail, {
      estateId: item.estate.id,
    })
  }
  const { t } = useTranslation()
  const {
    state,
    mortgageAmount,
    principal,
    repayment,
    durationInSeconds,
    estate: {
      decimals,
      metadata: {
        metadata: { name },
        imageUrl,
      },
    },
  } = item

  const overdue =
    item.state !== MortgageTokenLoanState.PENDING &&
    item.state !== MortgageTokenLoanState.CANCELLED &&
    new Date().getTime() / 1000 > item.dueInSeconds

  return (
    <View style={styles.itemContainer}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <Text style={styles.title}>{name}</Text>
      <View style={styles.rowSpaceBetween}>
        <LoanState state={state} overdue={overdue} />
        <CustomPressable onPress={navigateToEstateDetail}>
          <Image source={openNewIcon} style={viewStyles.icon} />
        </CustomPressable>
      </View>
      <LabelView
        label={`${t("Duration")}:`}
        value={convertSecondsToTime(durationInSeconds)}
      />
      <View style={styles.rowSpaceBetween}>
        <LabelView
          label={`${t("Mortgage amount")}:`}
          value={formatCurrency(
            formatNumericByDecimals(mortgageAmount, decimals)
          )}
        />
        <Image style={viewStyles.tinyIcon} source={usdtIcon} />
      </View>
      <LabelView
        label={`${t("Principal")}:`}
        value={`${formatCurrency(formatNumericByDecimals(principal, decimals))} ${t("USDT")}`}
      />
      <LabelView
        label={`${t("Repayment")}:`}
        value={`${formatCurrency(formatNumericByDecimals(repayment, decimals))} ${t("USDT")}`}
      />
      <LoanActionButton loan={item} />
    </View>
  )
}

const styles = StyleSheet.create({
  itemContainer: {
    paddingBottom: 12,
    marginHorizontal: 2,
  },
  marginBottomDefault: {
    marginBottom: 8,
  },
  labelView: {
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    ...textStyles.labelL,
    color: textColors.textBlack8,
    marginEnd: 4,
  },
  value: {
    ...textStyles.bodyM,
    color: textColors.textBlack11,
  },
  rowSpaceBetween: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  title: {
    ...textStyles.titleS,
    marginTop: 4,
    marginBottom: 8,
  },
  image: {
    width: "100%",
    aspectRatio: 16 / 9,
    borderRadius: 8,
    marginBottom: 8,
  },
  status: {
    marginVertical: 8,
    marginTop: 8,
  },
})

export { MortgageTokenLoanRenderItem }
