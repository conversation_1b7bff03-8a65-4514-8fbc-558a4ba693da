import { create<PERSON>ontex<PERSON>, <PERSON><PERSON><PERSON>, SetStateAction } from "react"
import { EstateRequestFilter, EstateRequestFilterInitial } from "../hooks"
import { TokenizationRequest } from "src/api"

interface EstateRequestType {
  estateRequests?: TokenizationRequest[]
  isLoading: boolean
  estateRequestFilter?: EstateRequestFilter
  setEstateRequestFilter?: Dispatch<SetStateAction<EstateRequestFilter>>
  onRefresh?: () => void
}

const EstateRequestContext = createContext<EstateRequestType>({
  isLoading: false,
  estateRequestFilter: EstateRequestFilterInitial,
  estateRequests: undefined,
  setEstateRequestFilter: () => {},
  onRefresh: () => {},
})

export default EstateRequestContext
