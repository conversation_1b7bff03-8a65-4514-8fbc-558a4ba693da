import { createContex<PERSON>, <PERSON><PERSON><PERSON>, SetStateAction } from "react"
import { MarketPlaceOfferFilter, MarketPlaceOfferFilterInitial } from "../hooks"
import { MarketplaceOffer } from "src/api"

interface MarketPlaceOfferType {
  offers?: MarketplaceOffer[]
  isLoading: boolean
  marketPlaceOfferFilter?: MarketPlaceOfferFilter
  setMarketPlaceOfferFilter?: Dispatch<SetStateAction<MarketPlaceOfferFilter>>
  onRefresh?: () => void
}

const MarketPlaceOfferContext = createContext<MarketPlaceOfferType>({
  isLoading: false,
  marketPlaceOfferFilter: MarketPlaceOfferFilterInitial,
  offers: undefined,
  setMarketPlaceOfferFilter: () => {},
  onRefresh: () => {},
})

export default MarketPlaceOfferContext
