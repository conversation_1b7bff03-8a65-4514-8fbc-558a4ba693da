import { createContext, <PERSON><PERSON>atch, SetStateAction } from "react"
import {
  MortgageTokenLoanFilter,
  MortgageTokenLoanFilterInitial,
} from "../hooks"
import { MortgageTokenLoan } from "src/api"

interface MortgageHubLoanType {
  mortgageTokenLoans?: MortgageTokenLoan[]
  isLoading: boolean
  mortgageTokenLoanFilter?: MortgageTokenLoanFilter
  setMortgageTokenLoanFilter?: Dispatch<SetStateAction<MortgageTokenLoanFilter>>
  onRefresh?: () => void
}

const MortgageHubLoanContext = createContext<MortgageHubLoanType>({
  isLoading: false,
  mortgageTokenLoanFilter: MortgageTokenLoanFilterInitial,
  mortgageTokenLoans: undefined,
  setMortgageTokenLoanFilter: () => {},
  onRefresh: () => {},
})

export default MortgageHubLoanContext
