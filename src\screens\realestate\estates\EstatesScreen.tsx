import React, { useState } from "react"
import EstateContext from "../context/EstateContext"
import { EstateRequestFilterInitial, useFetchEstates } from "../hooks"
import { EstatesView } from "./EstatesView"

const EstatesScreen: React.FC = () => {
  const [estateRequestFilter] = useState(EstateRequestFilterInitial)

  const {
    estates = [],
    isLoading,
    refresh,
  } = useFetchEstates(estateRequestFilter)

  return (
    <EstateContext.Provider
      value={{
        estates,
        onRefresh: refresh,
        isLoading: isLoading,
      }}
    >
      <EstatesView />
    </EstateContext.Provider>
  )
}

export default EstatesScreen
