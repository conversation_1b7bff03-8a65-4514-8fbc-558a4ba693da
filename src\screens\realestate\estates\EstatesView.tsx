import React, { useCallback, useContext } from "react"
import { FlatList, StyleSheet, View } from "react-native"
import { Background, EmptyView, LoadingView, TopBar } from "components"
import { EstateRenderItem } from "../components"
import { useTranslation } from "react-i18next"
import EstateContext from "screens/realestate/context/EstateContext"
import { Estate } from "src/api"

const EstatesView: React.FC = () => {
  const { t } = useTranslation()
  const { estates = [], isLoading } = useContext(EstateContext)
  const renderEstateItem = useCallback(
    ({ item }: { item: Estate }) => <EstateItem estate={item} />,
    []
  )

  return (
    <Background>
      <View style={styles.container}>
        <TopBar title={t("Estate").toUpperCase()} />
        {isLoading ? (
          <LoadingView />
        ) : estates.length === 0 ? (
          <EmptyView />
        ) : (
          <ListEstateView estates={estates} renderItem={renderEstateItem} />
        )}
      </View>
    </Background>
  )
}

const ListEstateView: React.FC<{
  estates: Estate[]
  renderItem: ({ item }: { item: Estate }) => React.ReactElement
}> = ({ estates, renderItem }) => {
  const keyExtractor = useCallback((item: Estate) => item.id.toString(), [])

  return (
    <FlatList
      style={styles.flatList}
      data={estates}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
    />
  )
}

const EstateItem: React.FC<{ estate: Estate }> = ({ estate }) => (
  <EstateRenderItem item={estate} />
)

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  flatList: {
    width: "100%",
  },
})

export { EstatesView }
