enum SortBy {
  TIMESTAMP,
  UNIT_PRICE,
  TOTAL_SUPPLY,
  AREA,
}

export interface EstateRequestFilter {
  //estate type
  estateType: {
    isResidential: boolean
    isAgricultural: boolean
    isNonAgricultural: boolean
    isForest: boolean
    isPerennialCrop: boolean
    isProduction: boolean
    isIndustrial: boolean
  }
  // status
  status: {
    isSelling: boolean
    isTransferringOwnerShip: boolean
    isInsufficientSoldAmount: boolean
    isCancelled: boolean
    isExpired: boolean
  }
  isMyEstate: boolean
  isEstateHaveSellNFTOrder: boolean
  isEsateHaveLoan: boolean
  sortBy: {
    type: SortBy
    isDescending: boolean
  }
}

const EstateRequestFilterInitial: EstateRequestFilter = {
  //estate type
  estateType: {
    isResidential: false,
    isAgricultural: false,
    isNonAgricultural: false,
    isForest: false,
    isPerennialCrop: false,
    isProduction: false,
    isIndustrial: false,
  },
  // status
  status: {
    isSelling: true,
    isTransferringOwnerShip: true,
    isInsufficientSoldAmount: false,
    isCancelled: false,
    isExpired: false,
  },
  isMyEstate: false,
  isEstateHaveSellNFTOrder: false,
  isEsateHaveLoan: false,
  sortBy: {
    type: SortBy.TIMESTAMP,
    isDescending: true,
  },
}

export { EstateRequestFilterInitial, SortBy }

export interface MarketPlaceOfferFilter {
  //status
  status: {
    isSelling: boolean
    isSold: boolean
    isCancelled: boolean
  }
}

//initial market place offer filter
const MarketPlaceOfferFilterInitial: MarketPlaceOfferFilter = {
  status: {
    isSelling: true,
    isSold: false,
    isCancelled: false,
  },
}

export { MarketPlaceOfferFilterInitial }

export interface MortgageTokenLoanFilter {
  //status
  status: {
    isPending: boolean
    isSupplied: boolean
    isOverdue: boolean
    isRepaid: boolean
    isForeclosed: boolean
    isCancelled: boolean
  }
}

//initial mortgage token loan filter
const MortgageTokenLoanFilterInitial: MortgageTokenLoanFilter = {
  status: {
    isPending: true,
    isSupplied: false,
    isOverdue: false,
    isRepaid: false,
    isForeclosed: false,
    isCancelled: false,
  },
}

export { MortgageTokenLoanFilterInitial }
