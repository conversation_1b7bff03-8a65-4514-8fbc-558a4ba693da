import {
  Estate,
  getEstateRequests,
  getEstates,
  getMarketPlaceOffersFilter,
  getMortgageLoansFilter,
  MarketplaceOffer,
  MortgageTokenLoan,
  TokenizationRequest,
} from "src/api"
import QueryKeys from "src/config/queryKeys"
import {
  EstateRequestFilter,
  MarketPlaceOfferFilter,
  MortgageTokenLoanFilter,
  SortBy,
} from "./types"
import { useQueryWithErrorHandling } from "src/api/query"

const sortValues = {
  [SortBy.TIMESTAMP]: "id",
  [SortBy.UNIT_PRICE]: "unit_price",
  [SortBy.TOTAL_SUPPLY]: "total_supply",
  [SortBy.AREA]: "area",
}

const getFilterParams = (filterEstateRequest: EstateRequestFilter) => {
  return {
    sortDescending: filterEstateRequest.sortBy.isDescending,
    sort: sortValues[filterEstateRequest.sortBy.type],
    itemsPerPage: 100,
    currentPage: 0,
  }
}

const getEstateRequestsParams = (filterEstateRequest: EstateRequestFilter) => {
  const states = []
  if (filterEstateRequest.status.isSelling) {
    states.push("SELLING")
  }
  if (filterEstateRequest.status.isTransferringOwnerShip) {
    states.push("TRANSFERRING_OWNERSHIP")
  }
  if (filterEstateRequest.status.isInsufficientSoldAmount) {
    states.push("INSUFFICIENT_SOLD_AMOUNT")
  }
  if (filterEstateRequest.status.isCancelled) {
    states.push("CANCELLED")
  }
  if (filterEstateRequest.status.isExpired) {
    states.push("EXPIRED")
  }

  return {
    sortDescending: filterEstateRequest.sortBy.isDescending,
    sort: sortValues[filterEstateRequest.sortBy.type],
    itemsPerPage: 100,
    currentPage: 0,
    states: states.join(","),
  }
}

export const useFetchEstateRequests = (
  filterEstateRequest: EstateRequestFilter
) => {
  const {
    data: estateRequests,
    isLoading,
    isFetching,
    refetch,
  } = useQueryWithErrorHandling<TokenizationRequest[]>({
    queryKey: [...QueryKeys.ESTATE.REQUESTS, filterEstateRequest],
    queryFn: () =>
      getEstateRequests(getEstateRequestsParams(filterEstateRequest)),
    refetchOnMount: true,
  })

  return {
    estateRequests,
    isLoading: isLoading || isFetching,
    refresh: refetch,
  }
}

export const useFetchEstates = (filterEstate: EstateRequestFilter) => {
  const {
    data: estates,
    isLoading,
    isFetching,
    refetch,
  } = useQueryWithErrorHandling<Estate[]>({
    queryKey: [...QueryKeys.ESTATE.LIST, filterEstate],
    queryFn: () => getEstates(getFilterParams(filterEstate)),
    refetchOnMount: true,
  })

  return {
    estates,
    isLoading: isLoading || isFetching,
    refresh: refetch,
  }
}

const getMortgageTokenLoanParams = (
  filterMortgageTokenLoan: MortgageTokenLoanFilter
) => {
  const states = []
  if (filterMortgageTokenLoan.status.isPending) {
    states.push("PENDING")
  }
  if (filterMortgageTokenLoan.status.isSupplied) {
    states.push("SUPPLIED")
  }
  if (filterMortgageTokenLoan.status.isOverdue) {
    states.push("OVERDUE")
  }
  if (filterMortgageTokenLoan.status.isRepaid) {
    states.push("REPAID")
  }
  if (filterMortgageTokenLoan.status.isForeclosed) {
    states.push("FORECLOSED")
  }
  if (filterMortgageTokenLoan.status.isCancelled) {
    states.push("CANCELLED")
  }
  return {
    states: states.join(","),
  }
}

export const useFetchMortgageTokenLoan = (
  filterMortgageTokenLoan: MortgageTokenLoanFilter
) => {
  const {
    data: mortgageTokenLoans,
    isLoading,
    isFetching,
    refetch,
  } = useQueryWithErrorHandling<MortgageTokenLoan[]>({
    queryKey: [
      ...QueryKeys.MORTGAGE.LOANS(),
      JSON.stringify(filterMortgageTokenLoan),
    ],
    queryFn: () =>
      getMortgageLoansFilter(
        getMortgageTokenLoanParams(filterMortgageTokenLoan)
      ),
    refetchOnMount: true,
  })

  return {
    mortgageTokenLoans,
    isLoading: isLoading || isFetching,
    refresh: refetch,
  }
}

const getMarketPlaceOffersParams = (
  filterMarketPlaceOffers: MarketPlaceOfferFilter
) => {
  const states = []
  if (filterMarketPlaceOffers.status.isSelling) {
    states.push("SELLING")
  }
  if (filterMarketPlaceOffers.status.isSold) {
    states.push("SOLD")
  }
  if (filterMarketPlaceOffers.status.isCancelled) {
    states.push("CANCELLED")
  }
  return {
    states: states.join(","),
  }
}

export const useFetchFatOffers = (
  filterMarketPlaceOffers: MarketPlaceOfferFilter
) => {
  const {
    data: offers,
    isLoading,
    isFetching,
    refetch,
  } = useQueryWithErrorHandling<MarketplaceOffer[]>({
    queryKey: [
      ...QueryKeys.MARKETPLACE.OFFERS(),
      JSON.stringify(filterMarketPlaceOffers),
    ],
    queryFn: () =>
      getMarketPlaceOffersFilter(
        getMarketPlaceOffersParams(filterMarketPlaceOffers)
      ),
    refetchOnMount: true,
  })

  return {
    offers,
    isLoading: isLoading || isFetching,
    refresh: refetch,
  }
}
