import React, { useState } from "react"
import MortgageHubLoansView from "./MortgageHubLoansView"
import { useFetchMortgageTokenLoan } from "screens/realestate/hooks/useFetchRealEstates"
import MortgageHubLoanContext from "screens/realestate/context/MortgageHubLoanContext"
import { MortgageTokenLoanFilterInitial } from "../hooks"

const MortgageHubLoansScreen: React.FC = () => {
  const [mortgageTokenLoanFilter, setMortgageTokenLoanFilter] = useState(
    MortgageTokenLoanFilterInitial
  )

  const { mortgageTokenLoans, isLoading, refresh } = useFetchMortgageTokenLoan(
    mortgageTokenLoanFilter
  )

  return (
    <MortgageHubLoanContext.Provider
      value={{
        mortgageTokenLoans: mortgageTokenLoans,
        mortgageTokenLoanFilter,
        setMortgageTokenLoanFilter,
        onRefresh: refresh,
        isLoading: isLoading,
      }}
    >
      <MortgageHubLoansView />
    </MortgageHubLoanContext.Provider>
  )
}

export default MortgageHubLoansScreen
