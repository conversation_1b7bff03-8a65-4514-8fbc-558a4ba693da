import React, { useCallback, useContext, useMemo } from "react"
import { FlatList, StyleSheet, View } from "react-native"
import { Background, EmptyView, LoadingView, TopBar } from "components"
import {
  MortgageTokenLoanFilterButton,
  MortgageTokenLoanRenderItem,
} from "../components"
import { useTranslation } from "react-i18next"
import MortgageHubLoanContext from "../context/MortgageHubLoanContext"
import { MortgageTokenLoan } from "src/api"

const MortgageHubLoansView: React.FC = () => {
  const { isLoading } = useContext(MortgageHubLoanContext)
  const { t } = useTranslation()

  return (
    <Background>
      <View style={styles.container}>
        <TopBar title={t("Mortgage Hub Loans").toUpperCase()} />
        {isLoading ? <LoadingView /> : <ListLoanView />}
      </View>
    </Background>
  )
}

const ListLoanView: React.FC = () => {
  const { mortgageTokenLoans } = useContext(MortgageHubLoanContext)

  const keyExtractor = useCallback(
    (item: MortgageTokenLoan) => item.id.toString(),
    []
  )

  const renderItem = useCallback(
    ({ item }: { item: MortgageTokenLoan }) => (
      <MortgageTokenLoanRenderItem item={item} />
    ),
    []
  )

  const headerComponent = useMemo(
    () => <HeaderComponent isEmpty={mortgageTokenLoans?.length == 0} />,
    [mortgageTokenLoans?.length]
  )

  return (
    <FlatList
      style={styles.flatList}
      data={mortgageTokenLoans}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      ListHeaderComponent={headerComponent}
    />
  )
}

const HeaderComponent: React.FC<{ isEmpty: boolean }> = ({ isEmpty }) => {
  return (
    <View>
      <MortgageTokenLoanFilterButton style={styles.headerButtonContainer} />
      {isEmpty && <EmptyView style={{ marginTop: 200 }} />}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  flatList: {
    width: "100%",
  },
  headerButtonContainer: {
    marginTop: 10,
    marginBottom: 10,
  },
})

export default MortgageHubLoansView
