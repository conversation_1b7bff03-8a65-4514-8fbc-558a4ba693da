import React, { useState } from "react"
import { MarketPlaceOffersView } from "./MarketPlaceOffersView"
import MarketPlaceOfferContext from "../context/MarketPlaceOfferContext"
import { useFetchFatOffers } from "screens/realestate/hooks/useFetchRealEstates"
import { MarketPlaceOfferFilterInitial } from "../hooks"

const MarketPlaceOffersScreen: React.FC = () => {
  const [marketPlaceOfferFilter, setMarketPlaceOfferFilter] = useState(
    MarketPlaceOfferFilterInitial
  )

  const { offers, isLoading, refresh } = useFetchFatOffers(
    marketPlaceOfferFilter
  )

  return (
    <MarketPlaceOfferContext.Provider
      value={{
        offers: offers,
        marketPlaceOfferFilter,
        setMarketPlaceOfferFilter,
        onRefresh: refresh,
        isLoading: isLoading,
      }}
    >
      <MarketPlaceOffersView />
    </MarketPlaceOfferContext.Provider>
  )
}

export default MarketPlaceOffersScreen
