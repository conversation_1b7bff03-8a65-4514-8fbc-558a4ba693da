import React, { useCallback, useContext, useMemo } from "react"
import { FlatList, StyleSheet, View } from "react-native"
import { useTranslation } from "react-i18next"

import { Background, EmptyView, LoadingView, TopBar } from "components"
import {
  MarketPlaceOfferFilterButton,
  MarketPlaceOfferRenderItem,
} from "../components"
import { MarketplaceOffer } from "src/api"
import MarketPlaceOfferContext from "../context/MarketPlaceOfferContext"

const MarketPlaceOffersView: React.FC = () => {
  const { t } = useTranslation()
  const { isLoading } = useContext(MarketPlaceOfferContext)

  return (
    <Background>
      <View style={styles.container}>
        <TopBar title={t("Marketplace Offers").toUpperCase()} />
        {isLoading ? <LoadingView /> : <ListMarketPlaceOfferView />}
      </View>
    </Background>
  )
}

const ListMarketPlaceOfferView: React.FC = () => {
  const { offers } = useContext(MarketPlaceOfferContext)

  const keyExtractor = useCallback(
    (item: MarketplaceOffer) => item.id.toString(),
    []
  )

  const renderItem = useCallback(
    ({ item }: { item: MarketplaceOffer }) => (
      <MarketPlaceOfferRenderItem item={item} />
    ),
    []
  )

  const headerComponent = useMemo(
    () => <HeaderComponent isEmpty={offers?.length == 0} />,
    [offers?.length]
  )

  return (
    <FlatList
      style={styles.flatList}
      data={offers}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      ListHeaderComponent={headerComponent}
    />
  )
}

const HeaderComponent: React.FC<{ isEmpty: boolean }> = ({ isEmpty }) => {
  return (
    <View>
      <MarketPlaceOfferFilterButton style={styles.headerButtonContainer} />
      {isEmpty && <EmptyView style={{ marginTop: 200 }} />}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  flatList: {
    width: "100%",
  },
  icon: {
    width: 24,
    height: 24,
  },
  headerButtonContainer: {
    marginTop: 10,
    marginBottom: 10,
  },
})

export { MarketPlaceOffersView }
