import React, { useState } from "react"
import EstateRequestsView from "./EstateRequestsView"
import { useFetchEstateRequests } from "screens/realestate/hooks/useFetchRealEstates"
import { EstateRequestFilterInitial } from "../hooks"
import EstateRequestContext from "../context/EstateRequestContext"

const EstateRequestsScreen: React.FC = () => {
  const [estateRequestFilter, setEstateRequestFilter] = useState(
    EstateRequestFilterInitial
  )

  const {
    estateRequests = [],
    isLoading,
    refresh,
  } = useFetchEstateRequests(estateRequestFilter)

  return (
    <EstateRequestContext.Provider
      value={{
        estateRequests: estateRequests,
        estateRequestFilter,
        isLoading: isLoading,
        setEstateRequestFilter,
        onRefresh: refresh,
      }}
    >
      <EstateRequestsView />
    </EstateRequestContext.Provider>
  )
}

export default EstateRequestsScreen
