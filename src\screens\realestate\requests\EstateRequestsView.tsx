import React, { useC<PERSON>back, useContext, useMemo } from "react"
import { FlatList, StyleSheet, View } from "react-native"
import {
  Background,
  EmptyView,
  LoadingView,
  PrimaryButton,
  TopBar,
} from "components"
import {
  EstateRequestFilterButton,
  EstateRequestRenderItem,
} from "../components"
import { useTranslation } from "react-i18next"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { TokenizationRequest } from "src/api"
import EstateRequestContext from "../context/EstateRequestContext"

const EstateRequestsView: React.FC = () => {
  const { t } = useTranslation()
  const { isLoading } = useContext(EstateRequestContext)

  return (
    <Background>
      <View style={styles.container}>
        <TopBar title={t("Real Estate Tokenization Requests").toUpperCase()} />
        {isLoading ? <LoadingView /> : <ListEstateRequestView />}
      </View>
    </Background>
  )
}

const ListEstateRequestView: React.FC = () => {
  const { estateRequests } = useContext(EstateRequestContext)

  const keyExtractor = useCallback(
    (item: TokenizationRequest) => item.id.toString(),
    []
  )

  const renderItem = useCallback(
    ({ item }: { item: TokenizationRequest }) => (
      <EstateRequestRenderItem item={item} />
    ),
    []
  )

  const headerComponent = useMemo(
    () => <HeaderComponent isEmpty={estateRequests?.length == 0} />,
    [estateRequests?.length]
  )

  return (
    <FlatList
      style={styles.flatList}
      data={estateRequests}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      ListHeaderComponent={headerComponent}
    />
  )
}

const HeaderComponent: React.FC<{ isEmpty: boolean }> = ({ isEmpty }) => {
  const { t } = useTranslation()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const onCreateNFT = useCallback(() => {
    navigation.navigate("CreateNFT")
  }, [navigation])

  return (
    <View>
      <View style={styles.headerButtonContainer}>
        <EstateRequestFilterButton />
        <PrimaryButton
          title={t("List Your Real Estate")}
          onPress={onCreateNFT}
          width={200}
          height={40}
        />
      </View>
      {isEmpty && <EmptyView style={{ marginTop: 200 }} />}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  flatList: {
    width: "100%",
  },
  headerButtonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 10,
  },
  footer: {
    marginTop: 100,
  },
})

export default EstateRequestsView
