import React, { useContext } from "react"
import { Image, Linking, StyleSheet, Text, View } from "react-native"
import { Background, SelectItemView, TopBar } from "components"
import { AuthContext } from "src/context/AuthContext"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { getLanguageLabel } from "./types"
import { Router } from "navigation/Router"
import languageIcon from "assets/images/ic_language.png"
import referencesIcon from "assets/images/ic_references.png"
import contactIcon from "assets/images/ic_contact.png"
import logoutIcon from "assets/images/ic_logout.png"
import highBuildingIcon from "assets/images/ic_high_building.png"
import { MaterialIcons } from "@expo/vector-icons"
import { BASE_WEB_URL } from "src/config/env"

const SettingsView: React.FC = () => {
  const { t, i18n } = useTranslation()
  const authContext = useContext(AuthContext)
  const navigation = useNavigation<NavigationProp<ParamListBase>>()

  const onGoHome = () => {
    navigation.navigate(Router.HomePath)
  }

  const onSelectLanguage = () => {
    navigation.navigate(Router.SelectLanguage, {
      selectedLanguage: i18n.language,
    })
  }
  const onSelectReferences = () => {
    navigation.navigate(Router.References)
  }

  const onSelectContact = () => {
    navigation.navigate(Router.Contact)
  }

  const onSelectLogout = () => {
    authContext.logout(true)
    onGoHome()
  }

  const onSelectDeleteAccount = async () => {
    onGoHome()
    Linking.openURL(`${BASE_WEB_URL}/delete-account`)
  }

  const onViewOffices = () => {
    navigation.navigate(Router.Offices)
  }

  return (
    <Background>
      <View style={styles.container}>
        <TopBar title={t("Settings").toUpperCase()} />
        <Text style={styles.label}>{t("Settings").toUpperCase()}</Text>
        <SelectItemView
          note={getLanguageLabel(i18n.language)}
          title={t("Language")}
          icon={<Image source={languageIcon} style={viewStyles.icon} />}
          onPress={onSelectLanguage}
        />
        <SelectItemView
          title={t("Logout")}
          icon={<Image source={logoutIcon} style={viewStyles.icon} />}
          onPress={onSelectLogout}
          showNextIcon={false}
        />
        <SelectItemView
          title={t("Delete Account")}
          icon={<MaterialIcons name="delete-outline" size={24} color="black" />}
          onPress={onSelectDeleteAccount}
          showNextIcon={false}
        />
        <Text style={styles.label}>{t("Informations").toUpperCase()}</Text>
        <SelectItemView
          title={t("References")}
          icon={<Image source={referencesIcon} style={viewStyles.icon} />}
          onPress={onSelectReferences}
        />
        <SelectItemView
          title={t("Contact")}
          icon={<Image source={contactIcon} style={viewStyles.icon} />}
          onPress={onSelectContact}
        />
        <SelectItemView
          title={t("Offices")}
          icon={<Image source={highBuildingIcon} style={viewStyles.icon} />}
          onPress={onViewOffices}
        />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    ...textStyles.titleS,
    color: Colors.black7,
    marginVertical: 12,
  },
  settingItem: {
    justifyContent: "space-between",
    flexDirection: "row",
    padding: 12,
    borderRadius: 8,
    marginTop: 4,
    backgroundColor: Colors.black2,
  },
  title: {
    ...textStyles.titleS,
    marginStart: 4,
    color: textColors.textBlack11,
  },
  language: {
    ...textStyles.bodyM,
    color: textColors.textBlack9,
    marginEnd: 4,
  },
})

export { SettingsView }
