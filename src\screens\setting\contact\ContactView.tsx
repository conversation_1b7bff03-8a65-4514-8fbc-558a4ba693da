import React from "react"
import { Image, Linking, StyleSheet, View } from "react-native"
import { Background, SelectItemView, TopBar } from "components"
import { useTranslation } from "react-i18next"
import twitterIcon from "assets/images/ic_twitter.png"
import telegramIcon from "assets/images/ic_tele.png"
import zaloIcon from "assets/images/ic_zalo.png"
import facebookIcon from "assets/images/ic_facebook.png"
import discordIcon from "assets/images/ic_discord.png"
import { viewStyles } from "src/config/styles"

const ContactView: React.FC = () => {
  const { t, i18n } = useTranslation()
  const isVietnamese = i18n.language === "vi"

  const onSelectX = () => {
    void Linking.openURL("https://x.com/BrikyLandGlobal?mx=2")
  }

  const onSelectTelegram = () => {
    void Linking.openURL("https://t.me/briky_land_community")
  }

  const onSelectDiscord = () => {
    void Linking.openURL("https://discord.gg/adnxzg3A")
  }

  const onSelectZalo = () => {
    if (isVietnamese) {
      void Linking.openURL("https://zalo.me/g/aflavt390")
    } else {
      void Linking.openURL("https://zalo.me/g/gysvxx420")
    }
  }

  const onSelectFacebook = () => {
    if (isVietnamese) {
      void Linking.openURL(
        "https://www.facebook.com/profile.php?id=61571968620170"
      )
    } else {
      void Linking.openURL("https://www.facebook.com/brikyland")
    }
  }

  return (
    <Background>
      <View style={styles.container}>
        <TopBar enableBack={true} title={t("Contact").toUpperCase()} />
        <SelectItemView
          title={t("Facebook")}
          icon={<Image source={facebookIcon} style={viewStyles.icon} />}
          onPress={onSelectFacebook}
          showNextIcon={false}
        />
        <SelectItemView
          title={t("Telegram")}
          icon={<Image source={telegramIcon} style={viewStyles.icon} />}
          onPress={onSelectTelegram}
          showNextIcon={false}
        />
        <SelectItemView
          title={t("Discord")}
          icon={<Image source={discordIcon} style={viewStyles.icon} />}
          onPress={onSelectDiscord}
          showNextIcon={false}
        />
        <SelectItemView
          title={t("X")}
          icon={<Image source={twitterIcon} style={viewStyles.icon} />}
          onPress={onSelectX}
          showNextIcon={false}
        />
        <SelectItemView
          title={t("Zalo")}
          icon={<Image source={zaloIcon} style={viewStyles.icon} />}
          onPress={onSelectZalo}
          showNextIcon={false}
        />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
})

export { ContactView }
