import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { Background, CustomPressable, TopBar } from "components"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { useTranslation } from "react-i18next"
import { Language } from "../types"
import vnIcon from "assets/images/ic_vi_flag.png"
import enIcon from "assets/images/ic_en_flag.png"
import Colors, { textColors } from "src/config/colors"
import { textStyles } from "src/config/styles"
import { showError } from "utils/toast"
import { saveLanguage } from "src/store/LocalAsyncStore"

interface LanguageItemViewProps {
  selectedLanguage: string
  title: string
  language: string
  flagIcon: React.ReactNode
  onPress: (language: string) => void
}

const LanguageItemView: React.FC<LanguageItemViewProps> = ({
  title,
  selectedLanguage,
  language,
  flagIcon,
  onPress,
}) => {
  return (
    <CustomPressable onPress={() => onPress(language)}>
      <View
        style={[
          styles.row,
          selectedLanguage === language && styles.selectedLanguage,
        ]}
      >
        <Text style={styles.languageTitle}>{title}</Text>
        {flagIcon}
      </View>
    </CustomPressable>
  )
}

interface SelectLanguageViewProps {
  selectedLanguage: string
}

const SelectLanguageView: React.FC<SelectLanguageViewProps> = ({
  selectedLanguage,
}) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const { t, i18n } = useTranslation()

  const onGoBack = () => {
    navigation.goBack()
  }

  const selectLanguage = (language: string) => {
    saveLanguage(language)
    i18n.changeLanguage(language).then(
      () => {
        onGoBack()
      },
      (err) => {
        showError(err)
      }
    )
  }

  const languages = [
    {
      title: t("English"),
      language: Language.en,
      flagIcon: <Image source={enIcon} style={styles.flagIcon} />,
    },
    {
      title: t("Tiếng Việt"),
      language: Language.vi,
      flagIcon: <Image source={vnIcon} style={styles.flagIcon} />,
    },
  ].sort((a, b) =>
    a.language === selectedLanguage
      ? -1
      : b.language === selectedLanguage
        ? 1
        : 0
  )

  return (
    <Background>
      <View style={styles.container}>
        <TopBar enableBack={true} title={t("Select Language").toUpperCase()} />
        {languages.map((item, index) => (
          <React.Fragment key={item.language}>
            <LanguageItemView
              title={item.title}
              language={item.language}
              onPress={selectLanguage}
              selectedLanguage={selectedLanguage}
              flagIcon={item.flagIcon}
            />
            {index < languages.length - 1 && <View style={styles.divider} />}
          </React.Fragment>
        ))}
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  icon: {
    width: 24,
    height: 24,
  },
  languageTitle: {
    ...textStyles.titleS,
    color: textColors.textBlack11,
  },
  row: {
    padding: 12,
    marginTop: 8,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  divider: {
    height: 1,
    backgroundColor: Colors.black5,
    marginVertical: 8,
  },
  selectedLanguage: {
    borderRadius: 100,
    backgroundColor: Colors.black3,
  },
  flagIcon: {
    width: 32,
    height: 24,
  },
})

export { SelectLanguageView }
