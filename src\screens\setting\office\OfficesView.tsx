import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { Background, TopBar } from "components"
import { useTranslation } from "react-i18next"
import homeIcon from "assets/images/ic_home.png"
import { Branch, Office } from "../types"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { ScrollView } from "react-native-gesture-handler"

const useOfficesData = () => {
  const { t } = useTranslation()

  const officesData: Office[] = [
    {
      nationalName: t("Viet Nam Office"),
      companyName: t(
        "Briky Land Real Estate Trading Floor Joint Stock Company"
      ),
      branches: [
        {
          headOfficeName: t("Tax registration office"),
          address: t(
            "No. 1A, Lane 57 Nguyen Khanh Toan, Dich Vong Ward, Cau Giay District, Hanoi City."
          ),
        },
        {
          headOfficeName: t("Hanoi Office 1 - real estate floor headquarters"),
          address: t(
            "Building N01-T2, Diplomatic Corps Urban Area, Hoang Minh <PERSON>hao Street, Xuan <PERSON> Ward, Bac Tu Liem District, Hanoi."
          ),
        },
        {
          headOfficeName: t("Hanoi Office 2"),
          address: t(
            "5th Floor, SME Royal Building, To Hieu Street, Quang Trung Ward, Ha Dong District, Hanoi."
          ),
        },
        {
          headOfficeName: t("Ho Chi Minh Office"),
          address: t(
            "178-180 Le Hong Phong Street, Ward 3, District 5, Ho Chi Minh City."
          ),
        },
      ],
    },
    {
      nationalName: t("Singapore Office"),
      companyName: t("Brikyland Holding Pte.Ltd"),
      branches: [
        {
          headOfficeName: "",
          address: t("114 Lavender Street, #11-83 CT HUB 2, Singapore."),
        },
      ],
    },
    {
      nationalName: t("Australia Office"),
      companyName: t("Brikyland Australia Pty.Ltd"),
      branches: [
        {
          headOfficeName: "",
          address: t(
            "Suit 886, 100 George Street, Parramatta NSW 2150 Australia."
          ),
        },
      ],
    },
    {
      nationalName: t("Dubai Office"),
      companyName: t(
        "Briky Land Virtual Assets Management Investment Services L.L.C"
      ),
    },
  ]
  return officesData
}

interface BranchViewProps {
  branch: Branch
}

const BranchView: React.FC<BranchViewProps> = ({ branch }) => {
  return (
    <Text style={styles.branchItem}>
      {branch.headOfficeName && (
        <Text style={styles.branchName}>{`${branch.headOfficeName}: `}</Text>
      )}
      {branch.address && (
        <Text
          style={
            branch.headOfficeName ? styles.branchAddress : styles.branchName
          }
        >
          {branch.address}
        </Text>
      )}
    </Text>
  )
}

interface OfficeViewProps {
  office: Office
}

const OfficeView: React.FC<OfficeViewProps> = ({ office }) => {
  const { nationalName, companyName, branches } = office

  return (
    <View>
      <Text style={styles.title}>{nationalName}</Text>
      <Text style={styles.officeName}>{companyName.toUpperCase()}</Text>
      <View style={styles.row}>
        {branches && branches.length > 0 && (
          <Image source={homeIcon} style={viewStyles.icon} />
        )}
        <View style={styles.listBranch}>
          {(branches || []).map((branch, index) => (
            <BranchView key={index} branch={branch} />
          ))}
        </View>
      </View>
    </View>
  )
}

const OfficesView: React.FC = () => {
  const { t } = useTranslation()

  const officesData = useOfficesData()

  return (
    <Background>
      <View style={styles.container}>
        <TopBar enableBack={true} title={t("Offices")} />
        <ScrollView style={styles.scrollViewContent}>
          {officesData.map((office, index) => (
            <View key={index}>
              <OfficeView office={office} key={index} />
              {index < officesData.length - 1 && (
                <View style={styles.divider} />
              )}
            </View>
          ))}
        </ScrollView>
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 50,
  },
  title: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
    marginBottom: 12,
  },
  row: {
    flexDirection: "row",
  },
  officeName: {
    ...textStyles.bodyM,
    color: textColors.textBlack11,
    marginBottom: 12,
  },
  listBranch: {
    flex: 1,
    marginStart: 8,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.black5,
    marginVertical: 8,
    marginBottom: 24,
  },
  branchItem: {
    marginBottom: 12,
  },
  branchName: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
  },
  branchAddress: {
    ...textStyles.bodyM,
    color: textColors.textBlack11,
  },
})

export { OfficesView }
