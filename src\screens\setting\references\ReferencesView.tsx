import React, { useCallback } from "react"
import { Image, Linking, StyleSheet, View } from "react-native"
import { Background, SelectItemView, TopBar } from "components"
import { BRIKY_URLS } from "src/config/consts"
import { useTranslation } from "react-i18next"
import documentIcon from "assets/images/ic_document.png"
import whitePaperIcon from "assets/images/ic_white_paper.png"
import verifiedIcon from "assets/images/ic_verified.png"
import { viewStyles } from "src/config/styles"

const ReferencesView: React.FC = () => {
  const openUrl = useCallback((url: string) => {
    void Linking.openURL(url)
  }, [])
  const { t } = useTranslation()

  const onSelectDocument = () => {
    openUrl(BRIKY_URLS.documentation)
  }

  const onSelectWhitePaper = () => {
    openUrl(BRIKY_URLS.whitepaper)
  }

  const onSelectSecurityReport = () => {
    openUrl(BRIKY_URLS.auditReport)
  }

  return (
    <Background>
      <View style={styles.container}>
        <TopBar enableBack={true} title={t("References").toUpperCase()} />
        <SelectItemView
          title={t("Documentation")}
          icon={<Image source={documentIcon} style={viewStyles.icon} />}
          onPress={onSelectDocument}
          showNextIcon={false}
        />
        <SelectItemView
          title={t("White paper")}
          icon={<Image source={whitePaperIcon} style={viewStyles.icon} />}
          onPress={onSelectWhitePaper}
          showNextIcon={false}
        />
        <SelectItemView
          title={t("Vertichain Security Report")}
          icon={<Image source={verifiedIcon} style={viewStyles.icon} />}
          onPress={onSelectSecurityReport}
          showNextIcon={false}
        />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
})

export { ReferencesView }
