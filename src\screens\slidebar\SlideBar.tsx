import React, { useContext } from "react"
import { Image, Pressable, StyleSheet, Text, View } from "react-native"
import {
  DrawerContentComponentProps,
  DrawerContentScrollView,
} from "@react-navigation/drawer"
import { AvatarView } from "src/components"
import { textStyles, viewStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import { useAtom } from "jotai"
import { AuthContext, profileAtom } from "src/context/AuthContext"
import { Router } from "navigation/Router"
import Colors from "src/config/colors"
import { useAccount } from "wagmi"
import { shortenAddress } from "utils"
import { IconButton } from "components/common/Button"
import iconForward from "assets/images/ic_forward.png"

import icHome from "assets/images/ic_home.png"
import icMenuProfile from "assets/images/ic_menu_profile.png"
import icMenuTokenization from "assets/images/ic_menu_tokenization.png"
import icMenuEstate from "assets/images/ic_menu_estate.png"
import icMenuMarketplace from "assets/images/ic_menu_marketplace.png"
import icMortgage from "assets/images/ic_mortgage.png"
import icInvestment from "assets/images/ic_investment.png"
import icStakingpool from "assets/images/ic_stakingpool.png"
import icTreasury from "assets/images/ic_treasury.png"
import icSetting from "assets/images/ic_setting.png"

interface DrawerItemProps {
  route: any
  focused: boolean
  navigation: any
}

const drawerIcons = {
  [Router.HomePath]: icHome,
  [Router.Profile]: icMenuProfile,
  [Router.Setting]: icSetting,
  [Router.Investment]: icInvestment,
  [Router.Tokenization]: icMenuTokenization,
  [Router.Estate]: icMenuEstate,
  [Router.MarketPlace]: icMenuMarketplace,
  [Router.MortgageHub]: icMortgage,
  [Router.StakingPool]: icStakingpool,
  [Router.Treasury]: icTreasury,
}

const useDrawerIcon = (route: string) => drawerIcons[route] || null

const CustomDrawerItem: React.FC<DrawerItemProps> = ({
  route,
  focused,
  navigation,
}) => {
  const { t } = useTranslation()
  const icon = useDrawerIcon(route.name)

  return (
    <Pressable
      style={[styles.drawerItem, focused && styles.drawerItemFocused]}
      onPress={() => navigation.navigate(route.name)}
    >
      <Image source={icon} style={viewStyles.icon} />
      <Text style={[textStyles.labelL, styles.drawerText]}>
        {t(route.name)}
      </Text>
    </Pressable>
  )
}

const SlideBar: React.FC<DrawerContentComponentProps> = ({
  navigation,
  state,
}) => {
  const { t } = useTranslation()
  const { isAuthenticated } = useContext(AuthContext)
  const { address } = useAccount()
  const [profile] = useAtom(profileAtom)

  const renderMenuItems = (start: number, end: number, offset = 0) =>
    state.routes.slice(start, end).map((route, index) => {
      const focused = index + offset === state.index
      if (route.name === Router.Profile && !isAuthenticated) return null
      return (
        <CustomDrawerItem
          key={route.key}
          route={route}
          focused={focused}
          navigation={navigation}
        />
      )
    })

  const renderMainMenuItems = () => renderMenuItems(0, 3)

  const renderNativeLandItems = () => (
    <>
      <Text style={styles.titleItem}>{t("Native Land")}</Text>
      {renderMenuItems(3, 7, 3)}
    </>
  )

  const renderBRIKItems = () => (
    <>
      <Text style={styles.titleItem}>{t("BRIK")}</Text>
      {renderMenuItems(7, 10, 7)}
    </>
  )

  return (
    <DrawerContentScrollView>
      <View style={styles.container}>
        <IconButton
          icon={<Image source={iconForward} style={viewStyles.icon} />}
          onPress={() => navigation.closeDrawer()}
          style={{ marginBottom: 16 }}
        />
        {isAuthenticated && (
          <View style={styles.profileContainer}>
            <AvatarView size={32} avatarUrl={profile?.avatarUrl ?? ""} />
            <Text style={[textStyles.labelL, styles.profileName]}>
              {profile?.alias || shortenAddress(address ?? "")}
            </Text>
          </View>
        )}

        {renderMainMenuItems()}
        {renderNativeLandItems()}
        {/* TODO: wait done to enable */}
        {renderBRIKItems()}
      </View>
    </DrawerContentScrollView>
  )
}

export default SlideBar

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    backgroundColor: Colors.white,
  },
  profileContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  profileName: {
    marginStart: 8,
  },
  drawerItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    height: 40,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: "transparent",
  },
  drawerItemFocused: {
    backgroundColor: Colors.primaryLight,
  },
  drawerText: {
    marginStart: 8,
  },
  titleItem: {
    ...textStyles.titleM,
    marginStart: 16,
    marginTop: 16,
    marginBottom: 12,
  },
})
