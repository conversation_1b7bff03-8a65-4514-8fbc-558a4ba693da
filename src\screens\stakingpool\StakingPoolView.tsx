import React, { useState } from "react"
import { ScrollView, StyleSheet, View } from "react-native"
import { Background, CustomCountDownTimer, TopBar } from "components"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import {
  BrikStakeView,
  StakeActionView,
  StakeToggleView,
  StakingWalletView,
  UnStakeActionView,
} from "./components"
import { TIME_TYPE } from "components/common/CustomCountDownTimer"
import { LAST_SECOND_TIME_STAMP_2025 } from "src/utils/timeExt"

const StakingPoolView: React.FC = () => {
  const { t } = useTranslation()
  const [isStaking, setIsStaking] = useState(true)
  const currentTimestampInSeconds = Math.floor(Date.now() / 1000)
  const countDownTime = LAST_SECOND_TIME_STAMP_2025 - currentTimestampInSeconds

  return (
    <Background>
      <View style={styles.container}>
        <TopBar title={t("Staking pool").toUpperCase()} />

        <ScrollView>
          <View style={{ flex: 1 }}>
            <StakeToggleView isStaking={isStaking} onToggle={setIsStaking} />
            {isStaking && countDownTime && (
              <CustomCountDownTimer
                duration={countDownTime}
                type={TIME_TYPE.BRIK}
                style={styles.countDown}
                title={`${t("The next staking time will be after")}:`}
              />
            )}
            <BrikStakeView />
            <StakingWalletView />
            {isStaking ? <StakeActionView /> : <UnStakeActionView />}
          </View>
        </ScrollView>
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  countDown: {
    borderWidth: 1,
    borderColor: Colors.black5,
    marginVertical: 16,
    padding: 16,
    flex: 1,
    paddingVertical: 12,
    paddingStart: 12,
    justifyContent: "center",
    alignItems: "flex-start",
    backgroundColor: Colors.white,
    borderRadius: 8,
  },
})

export { StakingPoolView }
