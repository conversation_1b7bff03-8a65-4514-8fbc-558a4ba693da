import brikIcon from "assets/images/ic_brik.png"
import brikiIcon from "assets/images/ic_briki.png"
import usdtIcon from "assets/images/ic_usdt.png"
import { GradientText } from "components"
import React from "react"
import { useTranslation } from "react-i18next"
import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  View,
} from "react-native"
import Colors, { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { formatCurrency } from "utils"

interface CardViewProps {
  label: string
  value: number
  icon: ImageSourcePropType
}

const CardView: React.FC<CardViewProps> = ({ label, value, icon }) => {
  return (
    <View style={styles.card}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.rowSpaceBetween}>
        <Text style={styles.value}>{formatCurrency(value)}</Text>
        <Image source={icon} style={viewStyles.tinyIcon} />
      </View>
    </View>
  )
}

const BrikStakeView: React.FC = () => {
  const { t } = useTranslation()
  // TODO: fake data, wait contract doc
  const brikSent = 10000
  const withdrawalFee = 20000
  const totalProfit = 5000
  const apy = 7000

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <Image source={brikiIcon} style={viewStyles.icon} />
        <Text style={styles.title}>{`${t("BrikStake").toUpperCase()}:`}</Text>
      </View>
      <View style={styles.content}>
        <CardView label={t("BRIK sent")} value={brikSent} icon={brikIcon} />
        <View style={styles.card}>
          <Text style={styles.label}>{t("Next daily interest")}</Text>
          <Text style={styles.nextDailyInterest}>{t("After Public Sale")}</Text>
        </View>
        <CardView
          label={t("Savings withdrawal fee")}
          value={withdrawalFee}
          icon={brikiIcon}
        />
        <CardView
          label={t("Total profit")}
          value={totalProfit}
          icon={brikiIcon}
        />
        <View style={[styles.card, { backgroundColor: Colors.white }]}>
          <GradientText style={styles.label} text={t("apy").toUpperCase()} />
          <View style={styles.rowSpaceBetween}>
            <Text style={styles.value}>{formatCurrency(apy)}</Text>
            <Image source={usdtIcon} style={viewStyles.tinyIcon} />
          </View>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 12,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  rowSpaceBetween: {
    minWidth: 100,
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginVertical: 12,
  },
  nextDailyInterest: {
    ...textStyles.labelL,
    color: Colors.secondaryNormal,
  },
  title: {
    ...textStyles.titleM,
    color: textColors.textBlack11,
    marginStart: 8,
  },
  card: {
    backgroundColor: Colors.black2,
    padding: 10,
    margin: 4,
    borderWidth: 1,
    borderColor: Colors.black4,
    borderRadius: 8,
  },
  label: {
    ...textStyles.labelL,
    color: Colors.black7,
    marginBottom: 4,
  },
  value: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
  },
})

export { BrikStakeView }
