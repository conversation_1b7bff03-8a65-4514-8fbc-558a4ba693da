import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { CardView } from "components"
import { textStyles } from "src/config/styles"
import clockImage from "assets/images/img_clock.png"

export const NoStaking: React.FunctionComponent = () => {
  const { t } = useTranslation()

  return (
    <CardView style={styles.card}>
      <View style={styles.container}>
        <Text style={styles.text}>{t("Staking pool")}</Text>
        <Image source={clockImage} style={styles.image} />
        <Text style={[textStyles.body1, { textAlign: "center" }]}>
          {t("Staking pool will be opened after BRIK Auction ends.")}
        </Text>
        <Text style={[textStyles.body1, { textAlign: "center" }]}>
          {t("Please stay tuned! We'll be back with more updates soon.")}
        </Text>
      </View>
    </CardView>
  )
}

const styles = StyleSheet.create({
  card: {
    padding: 16,
    backgroundColor: "white",
  },
  container: {
    justifyContent: "center",
    alignItems: "center",
    textAlign: "center",
    color: "#6B7280",
  },
  imageContainer: {
    width: 208,
    marginVertical: 32,
  },
  image: {
    width: 120,
    height: 120,
    marginVertical: 50,
  },
  text: {
    fontWeight: "600",
    color: "#4B5563",
    fontSize: 18,
    textAlign: "center",
  },
})
