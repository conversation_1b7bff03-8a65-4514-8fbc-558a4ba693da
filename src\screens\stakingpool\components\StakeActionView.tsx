import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { Controller, useForm } from "react-hook-form"
import { InputField, PrimaryButton } from "components"
import { z } from "zod"
import { useTranslation } from "react-i18next"
import { zodResolver } from "@hookform/resolvers/zod"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import icBrik from "assets/images/ic_brik.png"
import swapIcon from "assets/images/ic_swap.png"
import {
  getRemainingTime,
  LAST_SECOND_TIME_STAMP_2025,
} from "src/utils/timeExt"

const useFormSchema = () => {
  //TODO: wait doc to define validate
  const formSchema = z.object({
    price: z.number(),
    percent: z.number(),
  })

  return formSchema
}

const StakeActionView: React.FC = () => {
  const { t } = useTranslation()

  const formSchema = useFormSchema()
  type FormValues = z.infer<typeof formSchema>
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      price: 0,
      percent: 0,
    },
  })

  const remainingTime = getRemainingTime(LAST_SECOND_TIME_STAMP_2025, t)
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{`${t("Enter the deposit amount")}:`}</Text>
      <View style={styles.row}>
        <Controller
          control={form.control}
          name={"price"}
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={styles.priceInput}>
              <InputField
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
              />
              <Image source={icBrik} style={styles.brikPriceIcon} />
            </View>
          )}
        />
        <Image source={swapIcon} style={styles.swap} />
        <Controller
          control={form.control}
          name={"percent"}
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={styles.percentInput}>
              <InputField
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
              />
              <Text style={styles.percent}>%</Text>
            </View>
          )}
        />
      </View>
      <PrimaryButton
        title={
          remainingTime ? t("Stake after", { time: remainingTime }) : t("Stake")
        }
        onPress={() => {
          // TODO: do later
        }}
        borderRadius={8}
        width={"100%"}
        style={styles.button}
        contentColor={textColors.textBlack}
        enabled={!remainingTime}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 12,
    borderWidth: 1,
    borderColor: Colors.black5,
    borderRadius: 8,
    padding: 16,
  },
  priceInput: {
    flex: 2,
  },
  percentInput: {
    flex: 1,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  title: {
    ...textStyles.titleS,
    color: textColors.textBlack11,
    marginBottom: 16,
  },
  button: {
    marginTop: 12,
  },
  brikPriceIcon: {
    ...viewStyles.tinyIcon,
    position: "absolute",
    right: 8,
    top: "50%",
    transform: [{ translateY: -8 }],
  },
  percent: {
    position: "absolute",
    right: 8,
    textAlignVertical: "center",
    height: "100%",
  },
  swap: {
    ...viewStyles.icon,
    marginHorizontal: 4,
  },
})

export { StakeActionView }
