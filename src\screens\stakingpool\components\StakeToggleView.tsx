import React from "react"
import { StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { CustomPressable } from "components"
import { textStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"

interface StakeToggleViewProps {
  isStaking: boolean
  onToggle: (isStaking: boolean) => void
}

export const StakeToggleView: React.FC<StakeToggleViewProps> = ({
  isStaking,
  onToggle,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.container}>
      <CustomPressable
        style={[styles.button, isStaking && styles.activeButton]}
        onPress={() => onToggle(true)}
      >
        <Text style={[styles.text, isStaking && styles.activeText]}>
          {t("Stake")}
        </Text>
      </CustomPressable>

      <CustomPressable
        style={[styles.button, !isStaking && styles.activeButton]}
        onPress={() => onToggle(false)}
      >
        <Text style={[styles.text, !isStaking && styles.activeText]}>
          {t("Unstake")}
        </Text>
      </CustomPressable>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    marginHorizontal: 40,
    backgroundColor: Colors.black3,
    borderRadius: 8,
    overflow: "hidden",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginVertical: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
  },
  activeButton: {
    margin: 1,
    borderRadius: 8,
    backgroundColor: Colors.white,
    shadowColor: Colors.black12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  text: {
    ...textStyles.labelL,
    color: textColors.textBlack9,
  },
  activeText: {
    ...textStyles.labelL,
    color: Colors.blueLink,
  },
})
