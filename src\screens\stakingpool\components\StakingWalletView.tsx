import React from "react"
import {
  Image,
  ImageBackground,
  ImageSourcePropType,
  StyleSheet,
  Text,
  View,
} from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { formatCurrency } from "utils"
import { useTranslation } from "react-i18next"
import { useErc20Balance, useErc20Formatter } from "src/api/contracts"
import {
  CONTRACT_ADDRESS_AUCTION_CURRENCY,
  CONTRACT_ADDRESS_PRIMARY_TOKEN,
} from "src/config/env"
import { useAccount } from "wagmi"
import brikIcon from "assets/images/ic_brik.png"
import usdtIcon from "assets/images/ic_usdt.png"
import walletBlueIcon from "assets/images/ic_wallet_blue.png"
import walletBackground from "assets/images/wallet_background.png"

interface WalletCardViewProps {
  icon: ImageSourcePropType
  label: string
  value: string
}

const WalletCardView: React.FC<WalletCardViewProps> = ({
  icon,
  label,
  value,
}) => (
  <View style={styles.card}>
    <View style={styles.row}>
      <Image source={icon} style={viewStyles.icon} />
      <Text style={styles.label}>{label}</Text>
    </View>
    <Text style={styles.value}>{formatCurrency(value)}</Text>
  </View>
)

export const StakingWalletView: React.FC = () => {
  const { t } = useTranslation()

  const { address } = useAccount()
  const currencyTokenBalance = useErc20Balance(
    address,
    CONTRACT_ADDRESS_AUCTION_CURRENCY
  )
  const currencyFormatter = useErc20Formatter(CONTRACT_ADDRESS_AUCTION_CURRENCY)

  const primaryTokenFormatter = useErc20Formatter(
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )
  const primaryTokenBalance = useErc20Balance(
    address,
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )

  const usdtBalance = formatCurrency(
    currencyFormatter.formatFixed(currencyTokenBalance)
  )
  const brikBalance = formatCurrency(
    primaryTokenFormatter.formatFixed(primaryTokenBalance)
  )

  return (
    <>
      <View style={styles.row}>
        <Image source={walletBlueIcon} style={viewStyles.icon} />
        <Text style={styles.title}>{`${t("My Wallet")}:`}</Text>
      </View>
      <View style={styles.wallet}>
        <ImageBackground
          source={walletBackground}
          style={styles.walletContainer}
          imageStyle={styles.image}
        >
          <WalletCardView
            icon={brikIcon}
            label={t("BRIK")}
            value={brikBalance}
          />
          <WalletCardView
            icon={usdtIcon}
            label={t("USDT")}
            value={usdtBalance}
          />
          {/* TODO: update exactly when have contract doc */}
          <WalletCardView
            icon={usdtIcon}
            label={t("BRIKSTAKE")}
            value={brikBalance}
          />
        </ImageBackground>
      </View>
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 4,
  },
  title: {
    ...textStyles.titleM,
    color: textColors.textBlack11,
    marginStart: 4,
  },
  wallet: {
    borderRadius: 8,
    marginTop: 8,
  },
  walletContainer: {
    width: "100%",
    paddingTop: 12,
    paddingBottom: 4,
  },
  image: {
    borderRadius: 8,
  },
  rowSpaceBetween: {
    justifyContent: "space-between",
    flexDirection: "row",
    marginBottom: 8,
    alignItems: "center",
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 4,
  },
  card: {
    backgroundColor: Colors.white,
    borderRadius: 8,
    marginHorizontal: 12,
    marginBottom: 8,
    paddingVertical: 12,
    paddingStart: 16,
  },
  label: {
    ...textStyles.labelM,
    color: textColors.textBlack11,
    marginStart: 8,
  },
  value: {
    ...textStyles.bodyM,
    color: textColors.textBlack11,
  },
})
