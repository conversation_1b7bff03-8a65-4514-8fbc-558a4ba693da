import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { Controller, useForm } from "react-hook-form"
import { InputField, PrimaryButton } from "components"
import { z } from "zod"
import { useTranslation } from "react-i18next"
import { zodResolver } from "@hookform/resolvers/zod"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import icBrik from "assets/images/ic_brik.png"
import icBriki from "assets/images/ic_briki.png"
import swapIcon from "assets/images/ic_swap.png"

const useFormSchema = () => {
  //TODO: wait api to define validate
  const formSchema = z.object({
    price: z.number(),
    percent: z.number(),
  })

  return formSchema
}

interface InfoItemViewProps {
  label: string
  content: React.ReactNode
}

const InfoItemView: React.FC<InfoItemViewProps> = ({ label, content }) => (
  <View style={styles.itemInfo}>
    <Text style={styles.label}>{label}</Text>
    {content}
  </View>
)

const UnStakeValuesView: React.FC = () => {
  const { t } = useTranslation()
  // TODO: fake data
  const feeRate = 7
  const fee = 1
  const receivableAmount = 5
  return (
    <View style={styles.unstake}>
      <InfoItemView
        label={`${t("Unstaking fee rate")}:`}
        content={
          <Text style={styles.textValue}>
            {feeRate} {t("%")}
          </Text>
        }
      />
      <InfoItemView
        label={`${t("Unstaking fee")}:`}
        content={
          <View style={styles.row}>
            <Text style={styles.textValue}>{fee}</Text>
            <Image source={icBriki} style={viewStyles.tinyIcon} />
          </View>
        }
      />
      <InfoItemView
        label={`${t("Receivable amount")}:`}
        content={
          <View style={styles.row}>
            <Text style={styles.textValue}>{receivableAmount}</Text>
            <Image source={icBrik} style={viewStyles.tinyIcon} />
          </View>
        }
      />
    </View>
  )
}

const UnStakeActionView: React.FC = () => {
  const { t } = useTranslation()

  const formSchema = useFormSchema()
  type FormValues = z.infer<typeof formSchema>
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      price: 0,
      percent: 0,
    },
  })

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{`${t("Unstaking")}:`}</Text>
      <View style={styles.row}>
        <Controller
          control={form.control}
          name={"price"}
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={styles.priceInput}>
              <InputField
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
              />
              <Image source={icBriki} style={styles.brikPriceIcon} />
            </View>
          )}
        />
        <Image source={swapIcon} style={styles.swap} />
        <Controller
          control={form.control}
          name={"percent"}
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={styles.percentInput}>
              <InputField
                value={value}
                onBlur={onBlur}
                onChangeText={onChange}
              />
              <Text style={styles.percent}>%</Text>
            </View>
          )}
        />
      </View>
      <UnStakeValuesView />
      <PrimaryButton
        title={t("Unstake")}
        onPress={() => {
          // TODO: do later
        }}
        borderRadius={8}
        width={"100%"}
        style={styles.button}
        contentColor={textColors.textBlack}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 12,
    borderWidth: 1,
    borderColor: Colors.black5,
    borderRadius: 8,
    padding: 16,
  },
  itemInfo: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  label: {
    ...textStyles.labelL,
    color: Colors.black7,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  unstake: {
    marginVertical: 8,
    paddingHorizontal: 8,
  },
  priceInput: {
    flex: 2,
  },
  percentInput: {
    flex: 1,
  },
  title: {
    ...textStyles.titleS,
    color: textColors.textBlack11,
    marginBottom: 16,
  },
  textValue: {
    ...textStyles.labelL,
    marginEnd: 4,
    color: textColors.textBlack,
  },
  button: {
    marginTop: 8,
  },
  brikPriceIcon: {
    ...viewStyles.tinyIcon,
    position: "absolute",
    right: 8,
    top: "50%",
    transform: [{ translateY: -8 }],
  },
  percent: {
    position: "absolute",
    right: 8,
    textAlignVertical: "center",
    height: "100%",
  },
  swap: {
    ...viewStyles.icon,
    marginHorizontal: 4,
  },
})

export { UnStakeActionView }
