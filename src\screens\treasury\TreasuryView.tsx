import React from "react"
import { <PERSON><PERSON>View, StyleSheet, View } from "react-native"
import { MyWalletView, TreasuryRoundsView } from "./components"
import { useTranslation } from "react-i18next"
import {
  useAuctionEndAt,
  useAuctionLiquidityPercentage,
  useAuctionTotalDeposit,
  useBackerRoundContribution,
  useBackerRoundUnlockedAmount,
  useCoreTeamTokensUnlocked,
  useErc20Formatter,
  useErc20TotalSupply,
  useExternalTreasuryContribution,
  useExternalTreasuryTokensUnlocked,
  useMarketMakerContribution,
  useMarketMakerTokensUnlocked,
  usePreICOUnlockedAmount,
  usePrivateSaleContribution,
  usePrivateSaleUnlockedAmount,
  useSeedRoundContribution,
  useSeedRoundUnlockedAmount,
} from "src/api/contracts"
import {
  CONTRACT_ADDRESS_AUCTION_CURRENCY,
  CONTRACT_ADDRESS_PRIMARY_TOKEN,
  CONTRACT_ADDRESS_STAKE_TOKEN,
  PRIMARY_TOKEN_AUCTION_ISSUANCE,
  PRIMARY_TOKEN_BACKER_ROUND_ISSUANCE,
  PRIMARY_TOKEN_CORE_TEAM_ISSUANCE,
  PRIMARY_TOKEN_EXTERNAL_TREASURY_ISSUANCE,
  PRIMARY_TOKEN_MARKET_MAKER_ISSUANCE,
  PRIMARY_TOKEN_PRE_ICO_ISSUANCE,
  PRIMARY_TOKEN_PRIVATE_SALE_ISSUANCE,
  PRIMARY_TOKEN_SEED_ROUND_ISSUANCE,
} from "src/config/env"
import { parseCurrency } from "utils"
import { TreasuryItemAttrs } from "./components/TreasuryItem"
import { LoadingView, OverviewBrik, TopBar } from "components"
import Colors from "src/config/colors"
import { QueryKeys } from "src/config/queryKeys"
import { useQuery } from "@tanstack/react-query"
import { getOracles } from "src/api"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "TreasuryView" })

const TreasuryView: React.FC = () => {
  const { t } = useTranslation()

  const auctionEndAt = useAuctionEndAt()

  const backerRoundMinted = useBackerRoundUnlockedAmount()
  const externalTreasuryMinted = useExternalTreasuryTokensUnlocked()
  const marketMakerMinted = useMarketMakerTokensUnlocked()
  const privateSaleMinted = usePrivateSaleUnlockedAmount()
  const seedRoundMinted = useSeedRoundUnlockedAmount()
  const coreTeamMinted = useCoreTeamTokensUnlocked()
  const esopMinted = usePreICOUnlockedAmount()

  const backerRoundContributionWei = useBackerRoundContribution()
  const externalTreasuryContributionWei = useExternalTreasuryContribution()
  const marketMakerContributionWei = useMarketMakerContribution()
  const privateSaleContributionWei = usePrivateSaleContribution()
  const seedRoundContributionWei = useSeedRoundContribution()

  const totalDepositWei = useAuctionTotalDeposit()

  const currencyTokenFormatter = useErc20Formatter(
    CONTRACT_ADDRESS_AUCTION_CURRENCY
  )

  const { data, isLoading } = useQuery({
    queryKey: QueryKeys.ORACLE.LIST,
    queryFn: () => getOracles(),
  })

  const treasuryProperties = data?.treasury
  const totalSupply = treasuryProperties?.totalSupply || 0
  const sold = treasuryProperties?.sold || 0
  const unlocked = treasuryProperties?.unlocked || 0
  const tokenAllocations = treasuryProperties?.tokenAllocations || []

  const items: TreasuryItemAttrs[] = []
  const auctionStarted = auctionEndAt && Number(auctionEndAt) > 0

  const primaryTokenFormatter = useErc20Formatter(
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )
  const primaryTokenTotalSupplyWei = useErc20TotalSupply(
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )
  const primaryTokenTotalSupply = primaryTokenFormatter.format(
    primaryTokenTotalSupplyWei
  )

  const stakeTokenFormatter = useErc20Formatter(CONTRACT_ADDRESS_STAKE_TOKEN)
  const stakeTokenTotalSupplyWei = useErc20TotalSupply(
    CONTRACT_ADDRESS_STAKE_TOKEN
  )
  const stakeTokenTotalSupply = stakeTokenFormatter.format(
    stakeTokenTotalSupplyWei
  )
  //TO DO: pass lint , will update when has api final
  logger.debug("Stake token total supply", { stakeTokenTotalSupply })
  const liquidityPercentage = useAuctionLiquidityPercentage()

  if (auctionStarted) {
    items.push({
      title: t("Backer Round"),
      contribution: parseCurrency(
        (currencyTokenFormatter.format(totalDepositWei) *
          (liquidityPercentage || 0)) /
          100
      ),
      issuance: parseCurrency(PRIMARY_TOKEN_AUCTION_ISSUANCE),
      percentage: (
        (PRIMARY_TOKEN_AUCTION_ISSUANCE * 100) /
        primaryTokenTotalSupply
      ).toFixed(1),
    })
  }

  if (backerRoundMinted) {
    items.push({
      title: t("treasury-BackerRound"),
      contribution: parseCurrency(
        currencyTokenFormatter.format(backerRoundContributionWei)
      ),
      issuance: parseCurrency(PRIMARY_TOKEN_BACKER_ROUND_ISSUANCE),
      percentage: (
        (PRIMARY_TOKEN_BACKER_ROUND_ISSUANCE * 100) /
        primaryTokenTotalSupply
      ).toFixed(1),
    })
  }
  if (externalTreasuryMinted) {
    items.push({
      title: t("treasury-ExternalTreasury"),
      contribution: parseCurrency(
        currencyTokenFormatter.format(externalTreasuryContributionWei)
      ),
      issuance: parseCurrency(PRIMARY_TOKEN_EXTERNAL_TREASURY_ISSUANCE),
      percentage: (
        (PRIMARY_TOKEN_EXTERNAL_TREASURY_ISSUANCE * 100) /
        primaryTokenTotalSupply
      ).toFixed(1),
    })
  }
  if (marketMakerMinted) {
    items.push({
      title: t("treasury-MarketMaker"),
      contribution: parseCurrency(
        currencyTokenFormatter.format(marketMakerContributionWei)
      ),
      issuance: parseCurrency(PRIMARY_TOKEN_MARKET_MAKER_ISSUANCE),
      percentage: (
        (PRIMARY_TOKEN_MARKET_MAKER_ISSUANCE * 100) /
        primaryTokenTotalSupply
      ).toFixed(1),
    })
  }
  if (privateSaleMinted) {
    items.push({
      title: t("treasury-PrivateSale"),
      contribution: parseCurrency(
        currencyTokenFormatter.format(privateSaleContributionWei)
      ),
      issuance: parseCurrency(PRIMARY_TOKEN_PRIVATE_SALE_ISSUANCE),
      percentage: (
        (PRIMARY_TOKEN_PRIVATE_SALE_ISSUANCE * 100) /
        primaryTokenTotalSupply
      ).toFixed(1),
    })
  }
  if (seedRoundMinted) {
    items.push({
      title: t("treasury-SeedRound"),
      contribution: parseCurrency(
        currencyTokenFormatter.format(seedRoundContributionWei)
      ),
      issuance: parseCurrency(PRIMARY_TOKEN_SEED_ROUND_ISSUANCE),
      percentage: (
        (PRIMARY_TOKEN_SEED_ROUND_ISSUANCE * 100) /
        primaryTokenTotalSupply
      ).toFixed(1),
    })
  }
  if (coreTeamMinted) {
    items.push({
      title: t("treasury-CoreTeam"),
      issuance: parseCurrency(PRIMARY_TOKEN_CORE_TEAM_ISSUANCE),
      percentage: (
        (PRIMARY_TOKEN_CORE_TEAM_ISSUANCE * 100) /
        primaryTokenTotalSupply
      ).toFixed(1),
    })
  }
  if (esopMinted) {
    items.push({
      title: t("treasury-Esop"),
      issuance: parseCurrency(PRIMARY_TOKEN_PRE_ICO_ISSUANCE),
      percentage: (
        (PRIMARY_TOKEN_PRE_ICO_ISSUANCE * 100) /
        primaryTokenTotalSupply
      ).toFixed(1),
    })
  }

  return (
    <View style={styles.container}>
      <TopBar title={t("Treasury").toUpperCase()} />
      {isLoading ? (
        <LoadingView />
      ) : (
        <ScrollView>
          <>
            <MyWalletView />
            <OverviewBrik
              totalAmount={totalSupply}
              brikSold={sold}
              brikUnlock={unlocked}
            />
            <TreasuryRoundsView tokenAllocations={tokenAllocations} />
          </>
        </ScrollView>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    flex: 1,
    backgroundColor: Colors.white,
  },
})

export { TreasuryView }
