import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import brikIcon from "assets/images/ic_brik.png"
import { viewStyles } from "src/config/styles"

export interface TreasuryItemAttrs {
  title: string
  issuance?: string
  contribution?: string
  percentage?: string
}

interface TreasuryItemProps extends TreasuryItemAttrs {
  key: number
}

export const TreasuryItem: React.FC<TreasuryItemProps> = ({
  title,
  issuance,
  contribution,
  percentage,
}: TreasuryItemProps) => {
  const { t } = useTranslation()
  return (
    <View style={styles.container}>
      <View>
        <View style={styles.row}>
          <Text style={styles.title}>{title}</Text>
          <View style={styles.valueContainer}>
            <Text style={styles.value}>{issuance}</Text>
            <Image source={brikIcon} style={styles.icon} />
          </View>
        </View>
        {contribution && (
          <View style={styles.row}>
            <Text style={styles.label}>{t("treasury-Contribution")}</Text>
            <View style={styles.valueContainer}>
              <Text style={styles.value}>{contribution}</Text>
              <Image
                source={{
                  uri: "https://s2.coinmarketcap.com/static/img/coins/128x128/21763.png",
                }}
                style={styles.icon}
              />
            </View>
          </View>
        )}
        {percentage && (
          <View style={styles.row}>
            <Text style={styles.label}>{t("treasury-SupplyPercent")}</Text>
            <Text style={styles.value}>{percentage}%</Text>
          </View>
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginHorizontal: 16,
    marginVertical: 16,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: "500",
    color: "#4B5563",
  },
  label: {
    fontSize: 16,
    color: "#4B5563",
  },
  valueContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  value: {
    fontSize: 16,
    color: "#4B5563",
    marginRight: 4,
  },
  icon: {
    ...viewStyles.smallIcon,
    marginLeft: 4,
  },
})
