import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import brikIcon from "assets/images/ic_brik.png"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { formatCurrency } from "utils"
import { TokenAllocation, TreasuryType } from "src/api"

interface AmountItemViewProps {
  label: string
  value: string
}

const AmountItemView: React.FC<AmountItemViewProps> = ({ label, value }) => {
  return (
    <View style={styles.rowSpaceBetween}>
      <Text style={styles.label}>{label}</Text>
      <Text style={styles.value}>{value}</Text>
    </View>
  )
}

interface TreasuryItemViewProps {
  tokenAllocation: TokenAllocation
}

export const TreasuryItemView: React.FC<TreasuryItemViewProps> = ({
  tokenAllocation,
}) => {
  const { t } = useTranslation()
  const { treasuryType, minted, allocation, percentage, sold, unlocked } =
    tokenAllocation

  const allocationTitles: Record<TreasuryType, string> = {
    BACKER: t("Backer Round"),
    SEED: t("Seed Round"),
    PRIVATE_SALE_1: t("Private sale 1"),
    PRIVATE_SALE_2: t("Private sale 2"),
    PUBLIC_SALE: t("Public sale"),
    MARKET_MAKER: t("Market maker"),
    CORE_TEAM: t("Core team"),
    EXTERNAL_TREASURY: t("External treasury"),
    STAKING_REWARD: t("Staking reward"),
  }

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: minted ? Colors.white : Colors.black4 },
      ]}
    >
      <View>
        <View style={styles.rowSpaceBetween}>
          <Text style={styles.title}>{allocationTitles[treasuryType]}</Text>
          <View style={styles.valueContainer}>
            <Text style={styles.totalAmount}>{formatCurrency(allocation)}</Text>
            <Image source={brikIcon} style={viewStyles.tinyIcon} />
          </View>
        </View>
        <AmountItemView label={t("Percentage")} value={`${percentage}%`} />
        {minted && (
          <AmountItemView label={t("BRIK sold")} value={formatCurrency(sold)} />
        )}
        {minted && (
          <AmountItemView
            label={t("BRIK unlock")}
            value={formatCurrency(unlocked)}
          />
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.black5,
    marginTop: 12,
    padding: 12,
    backgroundColor: Colors.white,
  },
  rowSpaceBetween: {
    justifyContent: "space-between",
    flexDirection: "row",
    marginBottom: 8,
    alignItems: "center",
  },
  title: {
    ...textStyles.titleS,
    color: textColors.textBlack11,
  },
  totalAmount: {
    ...textStyles.titleS,
    marginEnd: 4,
    color: textColors.textBlack11,
  },
  label: {
    ...textStyles.bodyM,
    color: textColors.textBlack9,
  },
  valueContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  value: {
    ...textStyles.bodyM,
    color: textColors.textBlack9,
  },
  icon: {
    ...viewStyles.smallIcon,
    marginLeft: 4,
  },
})
