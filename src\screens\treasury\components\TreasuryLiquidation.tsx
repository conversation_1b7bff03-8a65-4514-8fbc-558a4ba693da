import React, { useEffect, useState } from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { CardView, PrimaryButton } from "components"
import Colors, { textColors } from "src/config/colors"
import { textStyles } from "src/config/styles"
import { convertHexToRGBA } from "utils"
import { useTranslation } from "react-i18next"
import usdtIcon from "assets/images/ic_usdt.png"
import brikIcon from "assets/images/ic_brik.png"

const TreasuryLiquidation: React.FC = () => {
  const { t } = useTranslation()
  const timeUnlocked = new Date("2025-02-23T08:55:30")
  const [timeToUnlock, setTimeToUnlock] = useState<string>("")

  useEffect(() => {
    const calculateTimeDifference = () => {
      const now = new Date()
      const delta = timeUnlocked.getTime() - now.getTime()

      const days = Math.floor(delta / (1000 * 60 * 60 * 24))
      const hours = Math.floor(
        (delta % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      )
      const minutes = Math.floor((delta % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((delta % (1000 * 60)) / 1000)

      setTimeToUnlock(`${days}d ${hours}h ${minutes}m ${seconds}s`)
    }

    calculateTimeDifference()

    const intervalId = setInterval(calculateTimeDifference, 1000)

    return () => clearInterval(intervalId)
  }, [])

  return (
    <CardView style={styles.card}>
      <View style={styles.container}>
        <Text style={styles.title}>{t("treasury-Liquidation")}</Text>
        <View style={styles.liquidationRow}>
          <Text style={textStyles.body1}>0.00</Text>
          <Image source={brikIcon} style={styles.brikIcon} />
        </View>
        <Progress progress={50} />
        <View style={styles.receiveRow}>
          <Text style={textStyles.body1}>{t("treasury-YouWillReceive")}</Text>
          <View style={styles.flex} />
          <Text style={textStyles.body1}>0</Text>
          <Image source={usdtIcon} style={styles.usdtIcon} />
        </View>
        <PrimaryButton
          onPress={() => {}}
          height={36}
          borderRadius={8}
          enabled={false}
          title={`${t("Unlocked after")} ${timeToUnlock}`}
          style={styles.button}
        />
      </View>
    </CardView>
  )
}

const Progress: React.FC<{
  progress: number
}> = ({ progress }) => {
  return (
    <View>
      <View style={styles.progressBarContainer}>
        <View style={[styles.progressBar, { width: `${progress}%` }]} />
        <View
          style={[
            styles.progressBarBackground,
            { width: `${100 - progress}%` },
          ]}
        />
      </View>
      <View style={styles.progressLabels}>
        {["0%", "25%", "50%", "75%", "100%"].map((label, index) => (
          <Text key={index} style={styles.progressLabel}>
            {label}
          </Text>
        ))}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  card: {
    marginTop: 16,
  },
  container: {
    padding: 12,
  },
  title: {
    ...textStyles.titleL,
    color: textColors.textGray600,
    fontWeight: "500",
  },
  liquidationRow: {
    flexDirection: "row",
    borderWidth: 0.5,
    borderRadius: 8,
    borderColor: Colors.border,
    padding: 12,
    marginTop: 12,
    justifyContent: "space-between",
    alignItems: "center",
  },
  brikIcon: {
    width: 24,
    height: 24,
  },
  receiveRow: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
  },
  flex: {
    flex: 1,
  },
  usdtIcon: {
    width: 16,
    height: 16,
    marginStart: 8,
  },
  button: {
    marginTop: 12,
  },
  progressBarContainer: {
    flexDirection: "row",
    borderRadius: 2,
    overflow: "hidden",
    marginTop: 12,
  },
  progressBar: {
    height: 6,
    backgroundColor: Colors.primary,
  },
  progressBarBackground: {
    height: 6,
    backgroundColor: convertHexToRGBA(Colors.primary, 0.4),
  },
  progressLabels: {
    flexDirection: "row",
    marginTop: 8,
    justifyContent: "space-between",
  },
  progressLabel: {
    ...textStyles.bodyM,
    color: textColors.textGray600,
  },
})

export { TreasuryLiquidation }
