import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { TreasuryItemView } from "./TreasuryItemView"
import listIcon from "assets/images/ic_list.png"
import { TokenAllocation } from "src/api"

interface TreasuryRoundsViewProps {
  tokenAllocations: TokenAllocation[]
}

const TreasuryRoundsView: React.FC<TreasuryRoundsViewProps> = ({
  tokenAllocations,
}) => {
  const { t } = useTranslation()

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <Image source={listIcon} style={viewStyles.icon} />
        <Text style={styles.title}>{t("Details")}</Text>
      </View>
      <View style={styles.listContainer}>
        {tokenAllocations.map((item, index) => (
          <TreasuryItemView tokenAllocation={item} key={index} />
        ))}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 12,
    marginVertical: 12,
  },
  title: {
    ...textStyles.titleM,
    color: textColors.textBlack11,
    marginStart: 8,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 4,
  },
  investmentLabel: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
  },
  totalAmount: {
    ...textStyles.labelL,
    color: textColors.textBlack11,
    marginEnd: 8,
  },
  listContainer: {
    width: "100%",
    marginTop: 8,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  divider: {
    backgroundColor: Colors.black5,
    height: 1,
    marginTop: 12,
    marginHorizontal: 4,
  },
})

export { TreasuryRoundsView }
