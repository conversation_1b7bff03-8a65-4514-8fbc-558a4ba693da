import React from "react"
import { Image, ImageRequireSource, StyleSheet, Text, View } from "react-native"
import { CardView } from "components"
import { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import {
  useErc20Formatter,
  useErc20TotalSupply,
  useTreasuryLiquidity,
} from "src/api/contracts"
import {
  CONTRACT_ADDRESS_AUCTION_CURRENCY,
  CONTRACT_ADDRESS_PRIMARY_TOKEN,
} from "src/config/env"
import { parseCurrency } from "utils"
import { useTranslation } from "react-i18next"
import brikIcon from "assets/images/ic_brik.png"
import usdtIcon from "assets/images/ic_usdt.png"

const TreasuryTotalBrik: React.FC = () => {
  const { t } = useTranslation()
  const primaryTokenFormatter = useErc20Formatter(
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )
  const currencyTokenFormatter = useErc20Formatter(
    CONTRACT_ADDRESS_AUCTION_CURRENCY
  )
  const primaryTokenSupplyWei = useErc20TotalSupply(
    CONTRACT_ADDRESS_PRIMARY_TOKEN
  )
  const primaryTokenSupply = primaryTokenFormatter.format(primaryTokenSupplyWei)
  const treasuryLiquidityWei = useTreasuryLiquidity()
  const treasuryLiquidity = currencyTokenFormatter.format(treasuryLiquidityWei)
  return (
    <View>
      <CardView>
        <View style={styles.container}>
          <InfoRow
            label={t("Total BRIK")}
            value={parseCurrency(primaryTokenSupply)}
            iconSource={brikIcon}
          />
          <InfoRow
            label={t("Unlocked amount")}
            value={parseCurrency(1e8)}
            iconSource={brikIcon}
            marginTop={8}
          />
          <InfoRow
            label={t("treasury-Liquidity")}
            value={parseCurrency(treasuryLiquidity)}
            iconSource={usdtIcon}
            marginTop={4}
          />
          <InfoRow
            label={t("Price")}
            value={parseCurrency(treasuryLiquidity / primaryTokenSupply, {
              precision: 6,
              separator: ",",
              symbol: "",
            })}
            iconSource={usdtIcon}
            marginTop={4}
          />
        </View>
      </CardView>
    </View>
  )
}

interface InfoRowProps {
  label: string
  value: string
  iconSource: ImageRequireSource
  marginTop?: number
}

const InfoRow: React.FC<InfoRowProps> = ({
  label,
  value,
  iconSource,
  marginTop = 0,
}) => (
  <View style={[styles.infoRow, { marginTop }]}>
    <Text style={styles.label}>{label}</Text>
    <Text style={styles.value}>{value}</Text>
    <Image source={iconSource} style={styles.icon} />
  </View>
)

const styles = StyleSheet.create({
  container: {
    padding: 12,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  label: {
    ...textStyles.body1,
    color: textColors.textGray600,
    flexGrow: 1,
  },
  value: {
    ...textStyles.bodyM,
    color: textColors.textGray600,
  },
  icon: {
    ...viewStyles.smallIcon,
    marginStart: 8,
  },
})

export { TreasuryTotalBrik }
