import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { CardView, GradientText } from "components"
import { textColors } from "src/config/colors"
import { textStyles, viewStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import brikiIcon from "assets/images/ic_briki.png"

const TreasuryTotalBriki: React.FC<{
  stakeTokenTotalSupply: string
  supplyPercent: string
}> = ({ stakeTokenTotalSupply, supplyPercent }) => {
  const { t } = useTranslation()
  return (
    <CardView style={styles.card}>
      <View>
        <View style={styles.row}>
          <View style={styles.flex}>
            <Text style={styles.totalBrikiText}>{t("Total BRIKI")}</Text>
            <Text style={styles.stakedBrikText}>{t("(Staked BRIK)")}</Text>
          </View>
          <Text style={styles.totalBrikiValue}>{stakeTokenTotalSupply}</Text>
          <Image source={brikiIcon} style={styles.icon} />
        </View>
        <View style={styles.infoRow}>
          <GradientText text={t("APY")} style={textStyles.body1} />
          <Text style={styles.infoText}>{t("After auction ends")}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={textStyles.body1}>{t("Daily staking rewards")}</Text>
          <Text style={styles.infoText}>{t("After auction ends")}</Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={textStyles.body1}>{t("treasury-SupplyPercent")}</Text>
          <Text style={textStyles.body1}>{supplyPercent} %</Text>
        </View>
      </View>
    </CardView>
  )
}

const styles = StyleSheet.create({
  card: {
    padding: 12,
    marginTop: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  flex: {
    flex: 1,
  },
  totalBrikiText: {
    ...textStyles.titleL,
    color: textColors.textGray600,
  },
  stakedBrikText: {
    ...textStyles.bodyM,
    color: textColors.textGray600,
    marginTop: 8,
  },
  totalBrikiValue: {
    ...textStyles.body1,
    fontWeight: "500",
  },
  icon: {
    ...viewStyles.smallIcon,
    marginStart: 8,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 2,
  },
  infoText: {
    ...textStyles.bodyM,
    color: textColors.textGray600,
  },
})

export { TreasuryTotalBriki }
