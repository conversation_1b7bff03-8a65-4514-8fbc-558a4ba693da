import React from "react"
import { StyleSheet, View } from "react-native"
import VerifyAccountView from "./VerifyAccountView"
import { Background, TopBar } from "components"
import { useTranslation } from "react-i18next"

const VerifyAccountScreen: React.FC = () => {
  const { t } = useTranslation()

  return (
    <Background>
      <View style={styles.container}>
        <TopBar
          enableBack={true}
          title={t("Account verification").toUpperCase()}
        />
        <VerifyAccountView />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 20,
  },
})

export default VerifyAccountScreen
