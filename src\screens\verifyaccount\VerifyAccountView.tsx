import React, { use<PERSON><PERSON>back, useState } from "react"
import {
  CustomPressable,
  DateTimeInput,
  <PERSON>rrorLabel,
  InputField,
  PrimaryButton,
} from "components"
import { Image, ScrollView, StyleSheet, Text, View } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors, { textColors } from "src/config/colors"
import { ActivityIndicator } from "react-native-paper"
import { useTranslation } from "react-i18next"
import { Controller, useForm } from "react-hook-form"
import { z } from "zod"
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  getImageType,
  getPhotoFileName,
  useChoosePhoto,
} from "utils/choosePhotoExt"
import { ImagePickerAsset } from "expo-image-picker/src/ImagePicker.types"
import { profileAtom } from "src/context/AuthContext"
import { useAtom } from "jotai"
import { getMy<PERSON>rofile, User, verifyProfile } from "src/api"
import { Dropdown } from "react-native-element-dropdown"
import { useAccount } from "wagmi"
import trashIcon from "assets/images/ic_trash.png"
import validSuccessIcon from "assets/images/ic_valid_success.png"
import { LabelView } from "components/common/LabelView"
import { useMutation } from "@tanstack/react-query"
import { showSuccess } from "utils/toast"
import { useHandleError } from "src/api/errors/handleError"

interface UploadImageLabelProps {
  title: string
  description: string
  isUploadSuccess: boolean
}

const UploadImageLabel: React.FC<UploadImageLabelProps> = ({
  title,
  description,
  isUploadSuccess,
}) => {
  const { t } = useTranslation()

  return (
    <View>
      <LabelView label={title} require={true} style={{ marginBottom: 8 }} />
      {isUploadSuccess ? (
        <View style={styles.row}>
          <Image source={validSuccessIcon} style={viewStyles.icon} />
          <Text style={styles.valid}>{t("Valid photo.")}</Text>
        </View>
      ) : (
        <Text style={styles.description}>{description}</Text>
      )}
    </View>
  )
}

const ImageUploader: React.FC<{
  imageUri?: string
  onPress: () => void
  titleView: React.ReactNode
  onClear: () => void
}> = ({ imageUri, onPress, titleView, onClear }) => {
  const { t } = useTranslation()

  return (
    <View style={styles.imageContainer}>
      <View style={{ flex: 1 }}>{titleView}</View>
      <View style={{ flex: 1 }}>
        {imageUri ? (
          <>
            <Image source={{ uri: imageUri }} style={styles.image} />
            <CustomPressable onPress={onClear} style={styles.trash} scale={1.5}>
              <Image source={trashIcon} style={viewStyles.smallIcon} />
            </CustomPressable>
          </>
        ) : (
          <CustomPressable onPress={onPress} scale={1.2}>
            <Text style={styles.submitPhoto}>{t("Submit Photo")}</Text>
          </CustomPressable>
        )}
      </View>
    </View>
  )
}

const VerifyAccountView: React.FC = () => {
  const { t } = useTranslation()
  const { handleError } = useHandleError()
  const navigation = useNavigation<NavigationProp<ParamListBase>>()
  const { address } = useAccount()
  const [profile, setProfile] = useAtom(profileAtom)
  const [loading, setLoading] = useState(false)

  const mutateVerifyProfile = useMutation({
    mutationFn: (body: FormData) => verifyProfile(body),
    onSuccess: async (data) => {
      if (data) {
        showSuccess(t("Verify account success"))
        mutationGetProfile.mutate()
      }
    },
    onError: (err) => {
      setLoading(false)
      handleError(err, t("Verify account fail"))
    },
  })

  const mutationGetProfile = useMutation({
    mutationFn: () => {
      return getMyProfile(address)
    },
    onSuccess: (userProfile: User) => {
      setProfile(userProfile)
    },
    onSettled() {
      setLoading(false)
      onGoBack()
    },
  })

  const onGoBack = () => {
    navigation.goBack()
  }

  const onSubmit = useCallback(
    (data: Payload) => {
      setLoading(true)

      const timestampInSeconds = Math.floor(
        new Date(data.dateOfBirthInSeconds).getTime() / 1000
      )

      const body = new FormData()
      body.append("fullName", data.fullName)
      body.append("nationalId", data.nationalId)
      body.append("zone", data.country)

      const frontImageFileName = getPhotoFileName(
        data.nationalIDCardFrontImage.fileName || null,
        data.nationalIDCardFrontImage.uri
      )
      const frontImageType = getImageType(frontImageFileName)

      const backImageFileName = getPhotoFileName(
        data.nationalIDCardBackImage.fileName || null,
        data.nationalIDCardBackImage.uri
      )
      const backImageType = getImageType(backImageFileName)
      // @ts-ignore
      body.append("nationalIDCardFrontImage", {
        uri: data.nationalIDCardFrontImage?.uri,
        name: frontImageFileName,
        type: frontImageType,
      })
      // @ts-ignore
      body.append("nationalIDCardBackImage", {
        uri: data.nationalIDCardBackImage?.uri,
        name: backImageFileName,
        type: backImageType,
      })
      body.append("dateOfBirthInSeconds", timestampInSeconds.toString())

      mutateVerifyProfile.mutate(body)
    },
    [mutateVerifyProfile]
  )

  const imagePickerAssetSchema = z.object({
    uri: z.string(),
    fileName: z.string(),
    width: z.number().optional(),
    height: z.number().optional(),
    type: z.string().optional(),
  })

  const formSchema = z.object({
    fullName: z
      .string()
      .min(2, t("Full name must be at least 2 characters"))
      .max(100, t("Full name must be at most 100 characters")),
    dateOfBirthInSeconds: z
      .string()
      .min(1, t("Please select Date of birth"))
      .refine(
        (date) => {
          const today = new Date()
          const birthDate = new Date(date)
          let age = today.getFullYear() - birthDate.getFullYear()
          const monthDiff = today.getMonth() - birthDate.getMonth()
          if (
            monthDiff < 0 ||
            (monthDiff === 0 && today.getDate() < birthDate.getDate())
          ) {
            age--
          }

          return age >= 18
        },
        {
          message: t("You must be at least 18 years old"),
        }
      ),
    nationalId: z
      .string()
      .min(9, t("Citizen ID must be at least 9 characters"))
      .max(12, t("Citizen ID must be at most 12 characters")),
    nationalIDCardFrontImage: imagePickerAssetSchema.refine(
      (data) => data?.uri !== undefined || data?.fileName !== undefined
    ),
    nationalIDCardBackImage: imagePickerAssetSchema.refine(
      (data) => data?.uri !== undefined || data?.fileName !== undefined
    ),
    country: z.string(),
  })

  const { handleChoosePhoto } = useChoosePhoto()

  const onChoosePhoto = useCallback(
    (onChange: (value: ImagePickerAsset | undefined) => void) => {
      handleChoosePhoto((files) => {
        onChange(files[0])
      })
    },
    []
  )

  type Payload = z.infer<typeof formSchema>

  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: profile?.alias || "",
      nationalId: "",
      dateOfBirthInSeconds: "",
      nationalIDCardFrontImage: undefined,
      nationalIDCardBackImage: undefined,
      country: "VIETNAM",
    },
  })

  return (
    <ScrollView>
      <View style={styles.container}>
        <Controller
          control={form.control}
          name="fullName"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputField
              label={t("Full name")}
              value={value}
              require={true}
              onChangeText={onChange}
              onBlur={onBlur}
              placeholder={""}
              style={styles.marginTop12}
              error={
                form.formState.errors.fullName?.message &&
                String(form.formState.errors.fullName?.message)
              }
            />
          )}
        />

        <Controller
          control={form.control}
          name="dateOfBirthInSeconds"
          render={({ field: { onChange, value } }) => (
            <>
              <DateTimeInput
                title={t("Date of birth")}
                value={value}
                require={true}
                onChangeDate={(date) => {
                  onChange(date?.toDateString() || "")
                }}
              />
              <ErrorLabel
                error={form.formState.errors.dateOfBirthInSeconds?.message}
              />
            </>
          )}
        />
        <Controller
          control={form.control}
          name="nationalId"
          render={({ field: { onChange, onBlur, value } }) => (
            <InputField
              label={t("Citizen Identification Number")}
              value={value}
              onChangeText={onChange}
              placeholder={""}
              require={true}
              onBlur={onBlur}
              style={styles.marginTop12}
              error={
                form.formState.errors.nationalId?.message &&
                String(form.formState.errors.nationalId?.message)
              }
            />
          )}
        />
        <View style={styles.divider} />
        <Text style={[textStyles.labelL, styles.marginTop16]}>
          {t("Provide images of your National ID Card")}
        </Text>
        <Text style={[textStyles.bodyM, styles.marginTop12]}>
          {t(
            "Please provide clear and complete images of both sides of the document."
          )}
        </Text>
        <Controller
          control={form.control}
          name="nationalIDCardFrontImage"
          render={({ field: { onChange, value } }) => {
            const imagePickerAsset = value as ImagePickerAsset | undefined
            return (
              <ImageUploader
                titleView={
                  <UploadImageLabel
                    isUploadSuccess={imagePickerAsset?.uri != null}
                    title={t("Front of ID card:")}
                    description={t(
                      "Take a photo of the front side of your identification card."
                    )}
                  />
                }
                imageUri={imagePickerAsset?.uri}
                onPress={() => onChoosePhoto(onChange)}
                onClear={() => {
                  onChange(undefined)
                }}
              />
            )
          }}
        />
        <Controller
          control={form.control}
          name="nationalIDCardBackImage"
          render={({ field: { onChange, value } }) => {
            const imagePickerAsset = value as ImagePickerAsset | undefined
            return (
              <ImageUploader
                titleView={
                  <UploadImageLabel
                    isUploadSuccess={imagePickerAsset?.uri != null}
                    title={t("Back of ID card:")}
                    description={t(
                      "Take a photo of the back side of your identification card."
                    )}
                  />
                }
                imageUri={imagePickerAsset?.uri}
                onPress={() => onChoosePhoto(onChange)}
                onClear={() => {
                  onChange(undefined)
                }}
              />
            )
          }}
        />
        <Text style={[textStyles.labelL, { marginTop: 12 }]}>
          {t("Country")}
        </Text>
        <Controller
          control={form.control}
          render={({ field: { onChange, value } }) => (
            <Dropdown
              value={value}
              data={[{ label: t("VIETNAM"), value: "VIETNAM" }]}
              labelField="label"
              valueField="value"
              selectedTextStyle={styles.dropdownItem}
              placeholderStyle={styles.dropdownItem}
              itemTextStyle={styles.dropdownItem}
              onChange={(item) => onChange(item.value)}
              style={[viewStyles.input, styles.dropdown]}
            />
          )}
          name="country"
          rules={{ required: true }}
        />
        <PrimaryButton
          title={t("Verify account")}
          onPress={form.handleSubmit(onSubmit)}
          color={Colors.primary}
          contentColor={textColors.textBlack}
          style={styles.verifyAccount}
          width={"100%"}
          isLoading={loading}
          icon={
            loading && <ActivityIndicator size="small" color={Colors.white} />
          }
        />
      </View>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: "white",
    paddingTop: 12,
  },
  uploadView: {
    backgroundColor: Colors.primary,
    padding: 12,
    borderRadius: 8,
  },
  marginTop12: {
    marginTop: 12,
  },
  trash: {
    position: "absolute",
    right: 8,
    top: 8,
    padding: 4,
  },
  marginTop16: {
    marginTop: 16,
  },
  description: {
    ...textStyles.bodyM,
    color: Colors.black7,
  },
  valid: {
    ...textStyles.bodyM,
    color: Colors.secondaryNormal,
    marginStart: 4,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.black4,
    marginTop: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  submitPhoto: {
    ...textStyles.labelL,
    color: textColors.textBlack,
    backgroundColor: Colors.surfaceDark,
    borderRadius: 8,
    textAlign: "center",
    textAlignVertical: "center",
    height: "100%",
  },
  imageContainer: {
    width: "100%",
    aspectRatio: 16 / 9,
    borderWidth: 1,
    borderColor: Colors.black5,
    padding: 12,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
    marginTop: 16,
    flexDirection: "row",
  },
  image: {
    width: "100%",
    height: "100%",
  },
  dropdown: {
    marginTop: 8,
  },
  dropdownItem: {
    ...textStyles.bodyM,
    color: textColors.textBlack9,
    height: 20,
  },
  verifyAccount: {
    width: "100%",
    marginTop: 20,
    marginBottom: 30,
    alignSelf: "center",
  },
})

export default VerifyAccountView
