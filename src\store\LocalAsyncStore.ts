import AsyncStorage from "@react-native-async-storage/async-storage"
import Logger from "src/utils/logger"

const logger = new Logger({ tag: "LocalAsyncStore" })

const KEY_LANGUAGE = "selectedLanguage"

const loadLanguage = async () => {
  try {
    const savedLanguage = await AsyncStorage.getItem(KEY_LANGUAGE)
    return savedLanguage || "en"
  } catch (error) {
    logger.error("Error loading language", error)
    return "en"
  }
}

const saveLanguage = async (language: string) => {
  try {
    await AsyncStorage.setItem(KEY_LANGUAGE, language)
  } catch (error) {
    logger.error("Error saving language", error)
  }
}

export { loadLanguage, saveLanguage }
