{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    // Path alias config
    "baseUrl": ".",
    "paths": {
      "components": ["src/components/index"],
      "components/*": ["src/components/*"],
      "screens": ["src/screens/index"],
      "screens/*": ["src/screens/*"],
      "utils": ["src/utils/index"],
      "utils/*": ["src/utils/*"],
      "hooks": ["src/hooks/index"],
      "hooks/*": ["src/hooks/*"],
      "navigation": ["src/navigation/index"],
      "navigation/*": ["src/navigation/*"],
      "const": ["src/const/index"],
      "const/*": ["src/const/*"],
      "assets/*": ["src/assets/*"],
      "src/*": ["src/*"],
      "model": ["src/model/index"],
      "locales/*": ["src/locales/*"]
    }
  }
}
