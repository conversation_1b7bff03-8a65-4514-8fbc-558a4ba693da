// withRemovedPermissions.js (Bạn có thể đặt tên file khác nếu muốn)
const { withAndroidManifest } = require("@expo/config-plugins")

// Hàm helper để loại bỏ permissions
const removePermissions = (androidManifest, permissionsToRemove) => {
  const { manifest } = androidManifest

  // Đảm bảo 'uses-permission' tồn tại và là một mảng
  if (
    !manifest["uses-permission"] ||
    !Array.isArray(manifest["uses-permission"])
  ) {
    return androidManifest
  }

  // Lọc các permission
  manifest["uses-permission"] = manifest["uses-permission"].filter(
    (permissionElement) => {
      // Kiểm tra cấu trúc cơ bản trước khi truy cập thuộc tính
      if (
        permissionElement &&
        typeof permissionElement === "object" &&
        permissionElement.$ &&
        permissionElement.$["android:name"]
      ) {
        const permissionName = permissionElement.$["android:name"]
        // <PERSON><PERSON><PERSON> tra xem permission name có nằm trong danh sách cần xóa không
        return !permissionsToRemove.includes(permissionName)
      }
      // Giữ lại các phần tử không khớp cấu trúc mong đợi
      return true
    }
  )

  return androidManifest
}

// Hàm plugin chính
const withRemovedPermissions = (config) => {
  // Sử dụng withAndroidManifest
  return withAndroidManifest(config, (config) => {
    // config.modResults chứa đối tượng AndroidManifest
    config.modResults = removePermissions(config.modResults, [
      // Danh sách các quyền cần loại bỏ
      "android.permission.SYSTEM_ALERT_WINDOW",
      "android.permission.RECORD_AUDIO",
    ])
    return config
  })
}

// Export plugin sử dụng module.exports của CommonJS
module.exports = withRemovedPermissions
